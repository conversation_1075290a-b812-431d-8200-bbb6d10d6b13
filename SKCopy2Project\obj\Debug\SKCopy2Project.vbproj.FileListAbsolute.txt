D:\Users\Matti\Documents\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.vbprojResolveAssemblyReference.cache
D:\Users\Matti\Documents\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.Resources.resources
D:\Users\Matti\Documents\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.vbproj.GenerateResource.Cache
D:\Users\Matti\Documents\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\bin\Debug\SKCopy2Project.dll.config
D:\Users\Matti\Documents\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\bin\Debug\SKCopy2Project.dll
D:\Users\Matti\Documents\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\bin\Debug\SKCopy2Project.pdb
D:\Users\Matti\Documents\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\bin\Debug\SKCopy2Project.xml
D:\Users\Matti\Documents\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\bin\Debug\Autodesk.Connectivity.Explorer.Extensibility.dll
D:\Users\Matti\Documents\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\bin\Debug\Autodesk.Connectivity.Extensibility.Framework.dll
D:\Users\Matti\Documents\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\bin\Debug\Autodesk.Connectivity.WebServices.dll
D:\Users\Matti\Documents\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\bin\Debug\Autodesk.DataManagement.Client.Framework.dll
D:\Users\Matti\Documents\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\bin\Debug\Autodesk.DataManagement.Client.Framework.Vault.dll
D:\Users\Matti\Documents\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\bin\Debug\Microsoft.Web.Services3.dll
D:\Users\Matti\Documents\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\bin\Debug\Autodesk.Connectivity.Explorer.Extensibility.xml
D:\Users\Matti\Documents\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\bin\Debug\Autodesk.Connectivity.Extensibility.Framework.xml
D:\Users\Matti\Documents\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\bin\Debug\Autodesk.Connectivity.WebServices.xml
D:\Users\Matti\Documents\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\bin\Debug\Autodesk.DataManagement.Client.Framework.xml
D:\Users\Matti\Documents\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\bin\Debug\Autodesk.DataManagement.Client.Framework.Vault.xml
D:\Users\Matti\Documents\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.ItabERPExport.MyCustomTabControl.resources
D:\Users\Matti\Documents\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.dll
D:\Users\Matti\Documents\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.xml
D:\Users\Matti\Documents\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.pdb
C:\ProgramData\Autodesk\Vault 2016\Extensions\SKCopy2Project\SKCopy2Project.dll.config
C:\ProgramData\Autodesk\Vault 2016\Extensions\SKCopy2Project\SKCopy2Project.dll
C:\ProgramData\Autodesk\Vault 2016\Extensions\SKCopy2Project\SKCopy2Project.pdb
C:\ProgramData\Autodesk\Vault 2016\Extensions\SKCopy2Project\SKCopy2Project.xml
C:\ProgramData\Autodesk\Vault 2016\Extensions\SKCopy2Project\SKCopy2Project.vcet.config
D:\Users\Matti\Documents\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.Form1.resources
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.vbprojResolveAssemblyReference.cache
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.Form1.resources
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.Resources.resources
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.ItabERPExport.MyCustomTabControl.resources
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.vbproj.GenerateResource.Cache
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.dll
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.xml
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.pdb
C:\ProgramData\Autodesk\Vault 2017\Extensions\SKCopy2Project\SKCopy2Project.vcet.config
C:\ProgramData\Autodesk\Vault 2017\Extensions\SKCopy2Project\SKCopy2Project.dll.config
C:\ProgramData\Autodesk\Vault 2017\Extensions\SKCopy2Project\SKCopy2Project.dll
C:\ProgramData\Autodesk\Vault 2017\Extensions\SKCopy2Project\SKCopy2Project.pdb
C:\ProgramData\Autodesk\Vault 2017\Extensions\SKCopy2Project\SKCopy2Project.xml
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.vbprojResolveAssemblyReference.cache
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.Form1.resources
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.Resources.resources
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.ItabERPExport.MyCustomTabControl.resources
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.vbproj.GenerateResource.Cache
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.dll
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.xml
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.pdb
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project-ver 2 toimiva myös derivoiduilla\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.Form1.resources
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project-ver 2 toimiva myös derivoiduilla\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.Resources.resources
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project-ver 2 toimiva myös derivoiduilla\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.ItabERPExport.MyCustomTabControl.resources
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project-ver 2 toimiva myös derivoiduilla\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.vbproj.GenerateResource.Cache
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project-ver 2 toimiva myös derivoiduilla\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.dll
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project-ver 2 toimiva myös derivoiduilla\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.xml
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project-ver 2 toimiva myös derivoiduilla\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.pdb
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project-ver 2 toimiva myös derivoiduilla\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.skPromote.resources
C:\ProgramData\Autodesk\Vault 2018\Extensions\SKCopy2Project\SKCopy2Project.vcet.config
C:\ProgramData\Autodesk\Vault 2018\Extensions\SKCopy2Project\SKCopy2Project.dll.config
C:\ProgramData\Autodesk\Vault 2018\Extensions\SKCopy2Project\SKCopy2Project.dll
C:\ProgramData\Autodesk\Vault 2018\Extensions\SKCopy2Project\SKCopy2Project.pdb
C:\ProgramData\Autodesk\Vault 2018\Extensions\SKCopy2Project\SKCopy2Project.xml
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project-ver 2 toimiva myös derivoiduilla\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.skAddBomRow.resources
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project-ver 2 toimiva myös derivoiduilla\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.SKPrint_to_folder_form.resources
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project-ver 2 toimiva myös derivoiduilla\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.vbproj.ResolveComReference.cache
C:\ProgramData\Autodesk\Vault 2019\Extensions\SKCopy2Project\SKCopy2Project.vcet.config
C:\ProgramData\Autodesk\Vault 2019\Extensions\SKCopy2Project\SKCopy2Project.dll.config
C:\ProgramData\Autodesk\Vault 2019\Extensions\SKCopy2Project\SKCopy2Project.dll
C:\ProgramData\Autodesk\Vault 2019\Extensions\SKCopy2Project\SKCopy2Project.pdb
C:\ProgramData\Autodesk\Vault 2019\Extensions\SKCopy2Project\SKCopy2Project.xml
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project-ver 2 toimiva myös derivoiduilla\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.sk_copyitemF.resources
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project-ver 2 toimiva myös derivoiduilla\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.SKPrint_to_folder_by_state_form.resources
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project-ver 2 toimiva myös derivoiduilla\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.vbproj.CoreCompileInputs.cache
C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\SKcopy2project-ver 2 toimiva myös derivoiduilla\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.vbprojAssemblyReference.cache
D:\Test\SKcopy2project-ver 2 toimiva myös derivoiduilla\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.vbproj.AssemblyReference.cache
D:\ProgramData\Autodesk\Vault 2019\Extensions\SKCopy2Project\SK-VaultExtension.vcet.config
D:\ProgramData\Autodesk\Vault 2019\Extensions\SKCopy2Project\SK-VaultExtension.dll.config
D:\ProgramData\Autodesk\Vault 2019\Extensions\SKCopy2Project\SK-VaultExtension.dll
D:\ProgramData\Autodesk\Vault 2019\Extensions\SKCopy2Project\SK-VaultExtension.pdb
D:\Test\SKcopy2project-ver 2 toimiva myös derivoiduilla\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.copy2ProjectAisi.resources
D:\Test\SKcopy2project-ver 2 toimiva myös derivoiduilla\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.Form1.resources
D:\Test\SKcopy2project-ver 2 toimiva myös derivoiduilla\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.sk_copyitemF.resources
D:\Test\SKcopy2project-ver 2 toimiva myös derivoiduilla\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.Resources.resources
D:\Test\SKcopy2project-ver 2 toimiva myös derivoiduilla\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.ItabERPExport.MyCustomTabControl.resources
D:\Test\SKcopy2project-ver 2 toimiva myös derivoiduilla\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.skAddBomRow.resources
D:\Test\SKcopy2project-ver 2 toimiva myös derivoiduilla\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.SKPrint_to_folder_by_state_form.resources
D:\Test\SKcopy2project-ver 2 toimiva myös derivoiduilla\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.SKPrint_to_folder_form.resources
D:\Test\SKcopy2project-ver 2 toimiva myös derivoiduilla\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.skPromote.resources
D:\Test\SKcopy2project-ver 2 toimiva myös derivoiduilla\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.sk_copyItemI.resources
D:\Test\SKcopy2project-ver 2 toimiva myös derivoiduilla\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.vbproj.GenerateResource.cache
D:\Test\SKcopy2project-ver 2 toimiva myös derivoiduilla\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SKCopy2Project.vbproj.CoreCompileInputs.cache
D:\Test\SKcopy2project-ver 2 toimiva myös derivoiduilla\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SK-VaultExtension.dll
D:\Test\SKcopy2project-ver 2 toimiva myös derivoiduilla\SKcopy2project-ver 2\SKCopy2Project\SKCopy2Project\obj\Debug\SK-VaultExtension.pdb
c:\ProgramData\Autodesk\Vault 2022\Extensions\SK-VaultExtension\SK-VaultExtension.vcet.config
c:\ProgramData\Autodesk\Vault 2022\Extensions\SK-VaultExtension\SK-VaultExtension.dll.config
c:\ProgramData\Autodesk\Vault 2022\Extensions\SK-VaultExtension\SK-VaultExtension.dll
D:\ProgramData\Autodesk\Vault 2022\Extensions\SK-VaultExtension\SK-VaultExtension.vcet.config
D:\ProgramData\Autodesk\Vault 2022\Extensions\SK-VaultExtension\SK-VaultExtension.dll.config
D:\ProgramData\Autodesk\Vault 2022\Extensions\SK-VaultExtension\SK-VaultExtension.dll
D:\ProgramData\Autodesk\Vault 2022\Extensions\SK-VaultExtension\SK-VaultExtension.pdb
C:\ProgramData\Autodesk\Vault 2022\Extensions\SK-VaultExtension\SK-VaultExtension.pdb
