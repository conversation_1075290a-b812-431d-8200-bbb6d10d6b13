﻿Imports Autodesk.Connectivity.Explorer.Extensibility
Imports Autodesk.Connectivity.WebServices
Imports Autodesk.Connectivity.WebServicesTools
Imports ACW = Autodesk.Connectivity.WebServices
Imports VDF = Autodesk.DataManagement.Client.Framework
Imports Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections

Public Class SKPrint_to_folder_by_state_form

    Public Shared m_conn As Connection = SKCopy2Project.SKCopy2ProjectCommandExtension.m_conn
    Private Sub SKPrint_to_folder_by_state_form_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        'create dropdow list to lifecycles
        Try
            Dim len As Integer = SKCopy2Project.SKCopy2ProjectCommandExtension.lfMatrix.GetLength(1) - 1
            For i As Integer = 0 To len
                ComboBox1.Items.Add(SKCopy2Project.SKCopy2ProjectCommandExtension.lfMatrix(0, i))
            Next

        Catch ex As Exception

        End Try




        Button1.Enabled = False
        Button4.Enabled = False
        Label2.Text = ""
    End Sub

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        Me.Cursor = Cursors.WaitCursor
        Label2.Text = ""
        Try
            DataGridView1.Rows.Clear()
            Dim i As Long = 0
            Dim mgr As WebServiceManager = m_conn.WebServiceManager
            Dim oFile As ACW.File = Nothing
            Dim oFiles As ACW.FileArray = Nothing
            Dim myFileArray As ACW.FileArray = Nothing
            For Each selection As ISelection In SKCopy2Project.SKCopy2ProjectCommandExtension.SKprint2folderStateSelections


                If selection.TypeId = SelectionTypeId.File Then
                    ' our ISelection.Id is really a File.MasterId


                    oFile = mgr.DocumentService.GetLatestFileByMasterId(selection.Id)
                ElseIf selection.TypeId = SelectionTypeId.FileVersion Then
                    ' our ISelection.Id is really a File.Id

                    oFile = mgr.DocumentService.GetFileById(selection.Id)
                End If

                If oFile IsNot Nothing And oFile.Name.Split(".").Last.ToLower = "dwg" Then
                    Dim row(5) As String

                    Dim myFileIds As Long() = New Long() {oFile.Id}
                    row(0) = oFile.MasterId
                    row(2) = oFile.Name
                    myFileArray = m_conn.WebServiceManager.DocumentService.GetFilesByHistoryType(myFileIds, ACW.FileHistoryTypeOptions.All).First

                    Dim resultfile As ACW.File = Nothing
                    For Each f As ACW.File In myFileArray.Files
                        If f.FileLfCyc.LfCycStateName = ComboBox1.Text Then
                            resultfile = f
                        End If
                    Next

                    If resultfile IsNot Nothing Then
                        row(1) = resultfile.Id
                        row(3) = "Yes"
                        'find possible visual attachemt
                        If resultfile.DesignVisAttmtStatus <> DesignVisualizationAttachmentStatus.None Then
                            Dim visfiles As FileAssocArray()
                            visfiles = m_conn.WebServiceManager.DocumentService.GetDesignVisualizationAttachmentsByFileMasterIds(resultfile.MasterId.ToSingleArray)
                            'GtMatrixT(21, row) = visfiles(0).FileAssocs(0).CldFile.Id
                            For Each assocarr As FileAssocArray In visfiles
                                For Each assoc As FileAssoc In assocarr.FileAssocs
                                    'compare fileresult id to dwf's parent id
                                    If resultfile.Id = assoc.ParFile.Id Then
                                        'we have correct DWF -> put id to row
                                        row(4) = "Yes"
                                        row(5) = assoc.CldFile.Id
                                        Exit For

                                    End If
                                Next


                            Next

                        End If
                    End If
                    Dim gridrow As String() = New String() {row(0), row(1), row(2), row(3), row(4), row(5)}
                    DataGridView1.Rows.Add(gridrow)
                    If row(3) Is Nothing Then
                        DataGridView1.Rows(DataGridView1.RowCount - 1).Cells(3).Style.BackColor = Color.DarkRed
                    End If
                    If row(4) Is Nothing Then
                        DataGridView1.Rows(DataGridView1.RowCount - 1).Cells(4).Style.BackColor = Color.DarkRed
                    End If
                End If

            Next
        Catch ex As Exception

        End Try
        Me.Cursor = Cursors.Default

    End Sub

    Private Sub ComboBox1_TextChanged(sender As Object, e As EventArgs) Handles ComboBox1.TextChanged
        'if there is something in combo
        Try
            If ComboBox1.Text <> "" Then
                Button1.Enabled = True
            End If
        Catch ex As Exception

        End Try
    End Sub

    Private Sub Button2_Click(sender As Object, e As EventArgs) Handles Button2.Click
        Try

            Dim lastfolder = TextBox1.Text
            Dim fb As New FolderBrowserDialog
            fb.RootFolder = Environment.SpecialFolder.MyComputer
            fb.SelectedPath = lastfolder

            If fb.ShowDialog = Windows.Forms.DialogResult.OK Then
                lastfolder = fb.SelectedPath
            End If
            TextBox1.Text = fb.SelectedPath.ToString

        Catch ex As Exception

        End Try
    End Sub

    Private Sub TextBox1_TextChanged(sender As Object, e As EventArgs) Handles TextBox1.TextChanged
        Try
            If TextBox1.Text <> "" Then
                Button4.Enabled = True
            End If
        Catch ex As Exception

        End Try
    End Sub

    Private Sub Button4_Click(sender As Object, e As EventArgs) Handles Button4.Click
        'start download
        Label2.Text = ""
        Try
            If CheckBox1.Checked = False And CheckBox2.Checked = False Then
                MessageBox.Show("Select DWF and/or PDF option.")
            Else
                Try
                    If DataGridView1.Rows.Count > 0 Then

                        'get ids to matrix to download
                        Dim dwfIds As Long() = Nothing
                        Dim ii As Integer = 0
                        Try
                            For i As Integer = 0 To DataGridView1.Rows.Count - 1
                                If DataGridView1.Rows(i).Cells(4).Value IsNot Nothing Then ' dwf found
                                    ReDim Preserve dwfIds(ii)
                                    dwfIds(ii) = DataGridView1.Rows(i).Cells(5).Value
                                    ii += 1
                                End If
                            Next
                        Catch ex As Exception
                        End Try
                        Try
                            If dwfIds IsNot Nothing Then
                                Me.Cursor = Cursors.WaitCursor
                                Button4.Enabled = False
                                'delete files in target folder
                                '' Dim temppath As String = TextBox1.Text
                                '' If System.IO.Directory.Exists(temppath) Then
                                'get all files
                                '' Dim di As New IO.DirectoryInfo(temppath)
                                ''di.Delete(True)
                                ''End If

                                'download ids to textbox1 folder
                                For i As Integer = 0 To dwfIds.Count - 1
                                    Dim f As ACW.File = m_conn.WebServiceManager.DocumentService.GetFileById(dwfIds(i))
                                    Dim settings As New VDF.Vault.Settings.AcquireFilesSettings(m_conn)
                                    settings.LocalPath = New VDF.Currency.FolderPathAbsolute(TextBox1.Text)
                                    Dim fileiter As VDF.Vault.Currency.Entities.FileIteration = SKPrint_to_folder_form.getFileIteration(f.Name, m_conn)
                                    settings.AddFileToAcquire(fileiter, VDF.Vault.Settings.AcquireFilesSettings.AcquisitionOption.Download)
                                    'remove file if exist before download
                                    'if file already exist delete
                                    If System.IO.File.Exists(TextBox1.Text & "\" & f.Name) Then
                                        System.IO.File.Delete(TextBox1.Text & "\" & f.Name)
                                    End If
                                    Dim results As VDF.Vault.Results.AcquireFilesResults = m_conn.FileManager.AcquireFiles(settings)

                                    'remove .dwg from dwf filename
                                    Dim targetFname As String = f.Name.Replace(".dwg", "")

                                    'if target file already exist delete
                                    If System.IO.File.Exists(TextBox1.Text & "\" & targetFname) Then
                                        System.IO.File.Delete(TextBox1.Text & "\" & targetFname)
                                    End If
                                    'change name
                                    If System.IO.File.Exists(TextBox1.Text & "\" & f.Name) Then
                                        My.Computer.FileSystem.RenameFile(TextBox1.Text & "\" & f.Name, targetFname)
                                        'change file as writable
                                        Dim attributes As System.IO.FileAttributes
                                        attributes = System.IO.File.GetAttributes(TextBox1.Text & "\" & targetFname)
                                        If (attributes And System.IO.FileAttributes.ReadOnly = System.IO.FileAttributes.ReadOnly) Then
                                            ' nake file writable.
                                            attributes = SKPrint_to_folder_form.RemoveAttribute(attributes, System.IO.FileAttributes.ReadOnly)
                                            System.IO.File.SetAttributes(TextBox1.Text & "\" & targetFname, attributes)
                                        End If
                                    End If

                                Next

                                Label2.Text = "DWFs Downloaded"
                            Me.Refresh()


                            'next if PDF is selected
                            Try
                                If CheckBox1.Checked = True Then
                                    Label2.Text = "Starting Printing PDFs"
                                    Me.Refresh()
                                        SKPrint_to_folder_form.createPDFs(TextBox1.Text & "\", TextBox1.Text & "\", False, Print1stPageOnly.Checked)
                                        Label2.Text = "PDFs Done!"
                                    Me.Refresh()
                                End If
                            Catch ex As Exception

                            End Try

                            'last remove dwf's if not selected

                            Try
                                If CheckBox2.Checked = False Then
                                    Dim dwfPaths As String() = Nothing
                                    dwfPaths = IO.Directory.GetFiles(TextBox1.Text & "\", "*.dwf*", IO.SearchOption.AllDirectories)
                                    For i As Integer = 0 To dwfPaths.Count - 1
                                        System.IO.File.Delete(dwfPaths(i))
                                    Next
                                End If
                            Catch ex As Exception

                            End Try


                            End If
                Catch ex As Exception

                        End Try
                    End If
                Catch ex As Exception

                End Try
            End If


        Catch ex As Exception

        End Try
        Me.Cursor = Cursors.Default
        Button4.Enabled = True


    End Sub

    Private Sub Button3_Click(sender As Object, e As EventArgs) Handles Button3.Click
        Close()
    End Sub
End Class