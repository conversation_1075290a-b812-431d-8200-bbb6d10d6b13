﻿Imports Autodesk.Connectivity.Explorer.Extensibility
Imports Autodesk.Connectivity.WebServices
Imports Autodesk.Connectivity.WebServicesTools
Imports ACW = Autodesk.Connectivity.WebServices
Imports VDF = Autodesk.DataManagement.Client.Framework
Imports VDFVF = Autodesk.DataManagement.Client.Framework.Vault.Forms
Imports VDFVCP = Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties
Imports Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections
Imports System.IO
Imports System.Xml
Imports System
Imports System.Web.Services.Protocols
Imports Autodesk.Connectivity.Extensibility.Framework
Imports Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties
Imports ACWT = Autodesk.Connectivity.WebServicesTools
Imports Autodesk.DataManagement.Client.Framework.Vault
Imports System.Windows.Forms
Public Class skPromote
    Private m_conn As Connection = SKCopy2Project.SKCopy2ProjectCommandExtension.m_conn
    Private dgnIsPrimary As Long = Nothing
    Private modelIsSecondary As Long = Nothing
    'Public test_file_islocked_readonly As Dictionary(Of Long, String)
    Public test_file_islocked_readonly As New List(Of String)
    'Private FileList As List(Of Autodesk.Connectivity.WebServices.File) = Nothing
    Public Shared itemMasterIds As Long()
    Public Shared filemasterIds As Long()
    Public Shared itemstoChange As Long()
    Public Shared itemstoChangeTo As Long()
    Public Shared filestoChange As Long()
    Public Shared filestoChangeTo As Long()
    ' Add these declarations at the class level
    Private WithEvents ProgressBar1 As New System.Windows.Forms.ProgressBar()
    Private WithEvents ListBox1 As New System.Windows.Forms.ListBox()
    Private WithEvents Labeltext As New System.Windows.Forms.Label()
    Private iRow As Integer = 0
    Private Sub skPromote_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        sk_usr.Text = m_conn.UserName
        ComboBox1.DataSource = New BindingSource(SKCopy2Project.SKCopy2ProjectCommandExtension.combobox_allowed_states, Nothing)
        ComboBox1.DisplayMember = "Value"
        ComboBox1.ValueMember = "Key"
        'ComboBox1.SelectedIndex = -1
        'sk_maingrp.Text = SKCopy2Project.SKCopy2ProjectCommandExtension.FIorPL
        'sk_subgrp.Text = SKCopy2Project.SKCopy2ProjectCommandExtension.currUsrGroup
        Me.Text = "SK Promote to next state', version " & Me.GetType.Assembly.GetName.Version.Major & "." & Me.GetType.Assembly.GetName.Version.Minor
        ' Initialize the progress controls
        InitializeProgressControls()
    End Sub
    Private Sub InitializeProgressControls()
        ' Configure ProgressBar
        ProgressBar1.Location = New System.Drawing.Point(12, 250)
        ProgressBar1.Size = New System.Drawing.Size(300, 23)
        ProgressBar1.Visible = False
        Me.Controls.Add(ProgressBar1)
        ' Configure ListBox
        ListBox1.Location = New System.Drawing.Point(330, 100)
        ListBox1.Size = New System.Drawing.Size(300, 173)
        ListBox1.Visible = False
        Me.Controls.Add(ListBox1)
        ' Configure Label
        Labeltext.Location = New System.Drawing.Point(12, 230)
        Labeltext.Size = New System.Drawing.Size(300, 20)
        Labeltext.Text = "Ready to check dependencies"
        Labeltext.Visible = False
        Me.Controls.Add(Labeltext)
    End Sub
    Private Sub DisplayError(ByVal ex As Exception)
        Dim errorCode As String
        Dim restrictionCodes As List(Of String)
        GetErrorAndRestrictionCodesString(ex, errorCode, restrictionCodes)
        Dim errorMsg As String = [String].Empty
        If errorCode IsNot Nothing Then
            Dim desc As String = Resource.ResourceManager.GetString("error" & errorCode)
            If desc Is Nothing Then
                desc = "Unknown error"
            End If
            errorMsg = "Error retuned from Vault Server: " & desc
        Else
            errorMsg = "Error: " & Convert.ToString(ex.Message)
        End If
        MessageBox.Show(errorMsg)
    End Sub
    Public Shared Sub GetErrorAndRestrictionCodesString(ByVal e As Exception, ByRef errorCode As String, ByRef restrictionCodes As List(Of String))
        Dim se As SoapException = TryCast(e, SoapException)
        errorCode = Nothing
        restrictionCodes = New List(Of String)()
        Dim restrictionErrors As String() = New String() {"1092", "1387", "1633"}
        If se IsNot Nothing Then
            Try
                errorCode = se.Detail("sl:sldetail")("sl:errorcode").InnerText.Trim()
                If restrictionErrors.Contains(errorCode) Then
                    Dim nodes As XmlNodeList = se.Detail("sl:sldetail")("sl:restrictions").ChildNodes
                    For Each node As XmlNode In nodes
                        If node.Name = "sl:restriction" Then
                            Dim element As XmlElement = TryCast(node, XmlElement)
                            If element IsNot Nothing Then
                                restrictionCodes.Add(element.GetAttribute("sl:code"))
                            End If
                        End If
                    Next
                End If
            Catch
            End Try
        End If
    End Sub
    Private Sub _sk_ok_Click(sender As Object, e As EventArgs) Handles _sk_ok.Click
        Dim docSvc As ACW.DocumentService = m_conn.WebServiceManager.DocumentService
        Dim itemSvc As ACW.ItemService = m_conn.WebServiceManager.ItemService
        Dim stateto As String = ComboBox1.Text
        Dim int_stateto As Long = -1
        Dim int_stateto_i As Long = -1
        ' Use LINQ for cleaner state lookup
        Dim stateMatrix = SKCopy2Project.SKCopy2ProjectCommandExtension.lfMatrix
        For i As Integer = 0 To stateMatrix.GetUpperBound(1)
            If stateto = stateMatrix(0, i) Then
                int_stateto = stateMatrix(1, i)
                Exit For
            End If
        Next
        Dim stateMatrixI = SKCopy2Project.SKCopy2ProjectCommandExtension.lfiMatrix
        For i As Integer = 0 To stateMatrixI.GetUpperBound(1)
            If stateto = stateMatrixI(0, i) Then
                int_stateto_i = stateMatrixI(1, i)
                Exit For
            End If
        Next
        'TEST if files are locked 
        test_file_islocked_readonly = Nothing
        For intI As Integer = 0 To DataGridView1.Rows.Count - 1
            Dim filename As String = DataGridView1.Rows(intI).Cells(1).Value
            If filename.EndsWith(".iam", StringComparison.OrdinalIgnoreCase) OrElse
               filename.EndsWith(".dwg", StringComparison.OrdinalIgnoreCase) Then
                CheckIfFileExistInVault(filename)
            End If
        Next
        If test_file_islocked_readonly IsNot Nothing AndAlso test_file_islocked_readonly.Count > 0 Then
            MessageBox.Show("Files are checked out: " & vbNewLine &
                           String.Join(Environment.NewLine, test_file_islocked_readonly))
            Exit Sub
        End If
        'END TEST
        Try
            Dim itemMasterIDi As Integer = 0
            Dim fileMasterIDi As Integer = 0
            'create item and file matrises from grid
            For row As Integer = 0 To DataGridView1.RowCount - 1
                If DataGridView1(0, row).Value = "Item" Then
                    'add item master id to itemmasterIDs
                    ReDim Preserve itemMasterIds(itemMasterIDi)
                    itemMasterIds(itemMasterIDi) = DataGridView1(4, row).Value
                    itemMasterIDi += 1
                Else
                    ReDim Preserve filemasterIds(fileMasterIDi)
                    filemasterIds(fileMasterIDi) = DataGridView1(4, row).Value
                    fileMasterIDi += 1
                End If
            Next
            'sort arrays
            If itemMasterIds IsNot Nothing Then
                Array.Sort(itemMasterIds)
            End If
            If filemasterIds IsNot Nothing Then
                Array.Sort(filemasterIds)
            End If
        Catch ex As Exception
        End Try
        Try
            'next create changestate id pairs (masterid and stateto id)
            ReDim itemstoChange(0)
            ReDim itemstoChangeTo(0)
            Dim iii As Integer = 1
            If itemMasterIds IsNot Nothing Then
                itemstoChange(0) = itemMasterIds(0) 'mid
                itemstoChangeTo(0) = int_stateto_i
                For i As Integer = 1 To itemMasterIds.Count - 1
                    If itemMasterIds(i - 1) <> itemMasterIds(i) Then
                        ReDim Preserve itemstoChange(iii)
                        ReDim Preserve itemstoChangeTo(iii)
                        itemstoChange(iii) = itemMasterIds(i) 'mid
                        itemstoChangeTo(iii) = int_stateto_i
                        iii += 1
                    End If
                Next
            End If
            ReDim filestoChange(0)
            ReDim filestoChangeTo(0)
            If filemasterIds IsNot Nothing Then
                filestoChange(0) = filemasterIds(0) 'mid
                filestoChangeTo(0) = int_stateto
                iii = 1
                For i As Integer = 1 To filemasterIds.Count - 1
                    If filemasterIds(i - 1) <> filemasterIds(i) Then
                        ReDim Preserve filestoChange(iii)
                        ReDim Preserve filestoChangeTo(iii)
                        filestoChange(iii) = filemasterIds(i) 'mid
                        filestoChangeTo(iii) = int_stateto
                        iii += 1
                    End If
                Next
            End If
        Catch ex As Exception
        End Try
        'first files to next
        Try
            _sk_ok.Enabled = False
            If filestoChange(0) > 0 Then
                Dim docsrvext As ACW.DocumentServiceExtensions = m_conn.WebServiceManager.DocumentServiceExtensions
                docsrvext.UpdateFileLifeCycleStates(filestoChange, filestoChangeTo, "State changed from " & SKCopy2Project.SKCopy2ProjectCommandExtension.stateFromstr)
                SKCopy2Project.SKCopy2ProjectCommandExtension.Write_log("C:\temp\Promote.log", "Files State changed from " & SKCopy2Project.SKCopy2ProjectCommandExtension.stateFromstr)
                'docsrvext.UpdateFileLifeCycleStates(SKCopy2Project.SKCopy2ProjectCommandExtension.filestoChange, SKCopy2Project.SKCopy2ProjectCommandExtension.filestoChangeTo, "State changed from " & SKCopy2Project.SKCopy2ProjectCommandExtension.stateFromstr)
            End If
        Catch ex As SoapException
            'files checked out?
            MessageBox.Show("Some files are checked out? " & ex.Message)
            Exit Sub
        End Try
        'second update item file links
        For i As Integer = 0 To itemstoChange.Count - 1
            Dim _item As Item
            Try
                'get item by master id
                _item = m_conn.WebServiceManager.ItemService.GetLatestItemByItemMasterId(itemstoChange(i))
                Dim primarylink As Long
                Try
                    primarylink = m_conn.WebServiceManager.ItemService.GetItemFileAssociationsByItemIds(_item.Id.ToSingleArray, ACW.ItemFileLnkTypOpt.Primary).First.CldFileId
                Catch ex As Exception
                    primarylink = Nothing
                End Try
                'find primarylink filetype
                Try
                    Dim oPrimaryFile As ACW.File = m_conn.WebServiceManager.DocumentService.GetFileById(primarylink)
                    If oPrimaryFile.Name.Split(".").Last.ToLower = "dgn" Then dgnIsPrimary = oPrimaryFile.Id
                Catch ex As Exception
                    SKCopy2Project.SKCopy2ProjectCommandExtension.Write_log("C:\temp\Promote_errors.log", ex.Message.ToString())
                End Try
                'sym_Updateitem(primarylink)
                Dim tertiarylinks As Long()
                Dim _itemfileassocs As ItemFileAssoc()
                Dim ii As Integer = 0
                Try
                    _itemfileassocs = m_conn.WebServiceManager.ItemService.GetItemFileAssociationsByItemIds(_item.Id.ToSingleArray, ACW.ItemFileLnkTypOpt.Tertiary)
                    If _itemfileassocs.Count > 0 Then
                        For Each itemfileassoc As ItemFileAssoc In _itemfileassocs
                            ReDim Preserve tertiarylinks(ii)
                            tertiarylinks(ii) = itemfileassoc.CldFileId
                            ii += 1
                        Next
                    End If
                    _itemfileassocs = Nothing
                    ii = 0
                Catch ex As Exception
                    tertiarylinks = Nothing
                End Try
                Dim secondarylinks As Long() = Nothing
                Try
                    _itemfileassocs = m_conn.WebServiceManager.ItemService.GetItemFileAssociationsByItemIds(_item.Id.ToSingleArray, ACW.ItemFileLnkTypOpt.Secondary)
                    If _itemfileassocs.Count > 0 Then
                        For Each itemfileassoc As ItemFileAssoc In _itemfileassocs
                            ReDim Preserve secondarylinks(ii)
                            secondarylinks(ii) = itemfileassoc.CldFileId
                            ii += 1
                            Try
                                If itemfileassoc.FileName.Split(".").Last.ToLower = "ipt" Or itemfileassoc.FileName.Split(".").Last.ToLower = "iam" Or itemfileassoc.FileName.Split(".").Last.ToLower = "dgn" Then 'lisää dgn 28.9.2021 lisätty 15.11.2021
                                    modelIsSecondary = itemfileassoc.CldFileId
                                End If
                            Catch ex As Exception
                            End Try
                        Next
                    End If
                    _itemfileassocs = Nothing
                    ii = 0
                Catch ex As Exception
                    secondarylinks = Nothing
                End Try
                Dim stdcomplinks As Long()
                Try
                    _itemfileassocs = m_conn.WebServiceManager.ItemService.GetItemFileAssociationsByItemIds(_item.Id.ToSingleArray, ACW.ItemFileLnkTypOpt.StandardComponent)
                    If _itemfileassocs.Count > 0 Then
                        For Each itemfileassoc As ItemFileAssoc In _itemfileassocs
                            ReDim Preserve stdcomplinks(ii)
                            stdcomplinks(ii) = itemfileassoc.CldFileId
                            ii += 1
                        Next
                    End If
                    _itemfileassocs = Nothing
                    ii = 0
                Catch ex As Exception
                    stdcomplinks = Nothing
                End Try
                Dim secsubcomplinks As Long()
                Try
                    _itemfileassocs = m_conn.WebServiceManager.ItemService.GetItemFileAssociationsByItemIds(_item.Id.ToSingleArray, ACW.ItemFileLnkTypOpt.SecondarySub)
                    If _itemfileassocs.Count > 0 Then
                        For Each itemfileassoc As ItemFileAssoc In _itemfileassocs
                            ReDim Preserve secsubcomplinks(ii)
                            secsubcomplinks(ii) = itemfileassoc.CldFileId
                            ii += 1
                        Next
                    End If
                    _itemfileassocs = Nothing
                    ii = 0
                Catch ex As Exception
                    secsubcomplinks = Nothing
                End Try
                Dim revisionid As Long = _item.RevId
                If primarylink <> Nothing Then
                    Try
                        itemSvc.AddFilesToPromote(primarylink.ToSingleArray, ItemAssignAll.Default, False)
                        If secondarylinks IsNot Nothing Then
                            itemSvc.AddFilesToPromote(secondarylinks, ItemAssignAll.Default, False)
                        End If
                        Dim timestamp As DateTime
                        Dim promoteOrder As GetPromoteOrderResults = itemSvc.GetPromoteComponentOrder(timestamp)
                        itemSvc.PromoteComponents(timestamp, promoteOrder.NonPrimaryArray)
                        Dim itemsAndFiles As ItemsAndFiles = itemSvc.GetPromoteComponentsResults(timestamp)
                        ' edit the items as needed
                        itemSvc.UpdateAndCommitItems(itemsAndFiles.ItemRevArray)
                        SKCopy2Project.SKCopy2ProjectCommandExtension.Write_log("C:\temp\Promote.log", "primarylink update and commit")
                    Catch ex As Exception
                        'MessageBox.Show(ex.Detail("Message").InnerXml)
                        'DisplayError(ex)
                        SKCopy2Project.SKCopy2ProjectCommandExtension.Write_log("C:\temp\Promote_errors.log", ex.Message.ToString())
                    End Try
                    ''Dim _editItem As Item = m_conn.WebServiceManager.ItemService.EditItems(revisionid.ToSingleArray).First
                    '''update filelinks
                    ''m_conn.WebServiceManager.ItemService.UpdateItemFileAssociations(_editItem.RevId, primarylink, False, secondarylinks, stdcomplinks, secsubcomplinks, tertiarylinks)
                    '''lets update comment
                    ''m_conn.WebServiceManager.ItemService.UpdateItemPropertyDefinitions(_editItem.MasterId.ToSingleArray, Nothing, Nothing, "Filelinks updated")
                    '''commit changes
                    ''m_conn.WebServiceManager.ItemService.UpdateAndCommitItems(_editItem.ToSingleArray)
                    '' Catch ex As Exception
                    ''  End Try
                End If
            Catch ex As Exception
                Try
                    'MessageBox.Show(ex.Detail("Message").InnerXml)
                    'try undo
                    'get item by master id
                    m_conn.WebServiceManager.ItemService.UndoEditItems(_item.Id.ToSingleArray)
                Catch
                End Try
            End Try
            Dim IdeeTxt As String = "N/A"
            'Update item file links again.
            Try
                Dim itemRevisionIds As Long() = New Long(0) {}
                Dim oItem As Item = m_conn.WebServiceManager.ItemService.GetLatestItemByItemMasterId(_item.MasterId)
                itemRevisionIds(0) = oItem.RevId
                Dim itemsToCommit As Item() = New Item(-1) {}
                Dim itemsToCommit_Ids As Long() = New Long(-1) {}
                Try
                    Dim notPromotableItems As New List(Of String)  ' List to store items that can't be promoted
                    ' Try to update promote components
                    itemSvc.UpdatePromoteComponents(itemRevisionIds, ItemAssignAll.Default, False)
                    Dim now As DateTime = DateTime.Now
                    Dim Order As GetPromoteOrderResults = itemSvc.GetPromoteComponentOrder(now)
                    If Order.PrimaryArray IsNot Nothing AndAlso Order.PrimaryArray.Length > 0 Then itemSvc.PromoteComponents(now, Order.PrimaryArray)
                    If Order.NonPrimaryArray IsNot Nothing AndAlso Order.NonPrimaryArray.Length > 0 Then itemSvc.PromoteComponentLinks(Order.NonPrimaryArray)
                    Dim promoteResult As ItemsAndFiles = itemSvc.GetPromoteComponentsResults(now)
                    Dim items As List(Of Item) = New List(Of Item)()
                    For ix As Integer = 0 To promoteResult.ItemRevArray.Length - 1
                        ' Check status - if status <= 1, item can't be promoted
                        IdeeTxt = promoteResult.ItemRevArray(ix).ItemNum.ToString()
                        If promoteResult.StatusArray(ix) > 1 Then
                            items.Add(promoteResult.ItemRevArray(ix))
                        Else
                            ' Add to not promotable list with reason based on status
                            Dim reason As String = GetReasonFromStatus(promoteResult.StatusArray(ix))
                            notPromotableItems.Add($"{promoteResult.ItemRevArray(ix).ItemNum}: {reason}")
                        End If
                        SKCopy2Project.SKCopy2ProjectCommandExtension.Write_log("C:\temp\Promote.log", promoteResult.ItemRevArray(ix).ItemNum.ToString())
                    Next
                    ' Display not promotable items if any exist
                    If notPromotableItems.Count > 0 Then
                        Dim message = String.Join(Environment.NewLine, notPromotableItems.ToArray())
                        MessageBox.Show("The following items cannot be promoted:" & vbNewLine & message, "Promotion Issues", MessageBoxButtons.OK, MessageBoxIcon.Warning)
                    End If
                    If items.Count > 0 Then itemSvc.UpdateAndCommitItems(items.ToArray())
                    SKCopy2Project.SKCopy2ProjectCommandExtension.Write_log("C:\temp\Promote.log", "primarylink update and commit again")
                Catch ex As Exception
                    m_conn.WebServiceManager.ItemService.UndoEditItems(oItem.Id.ToSingleArray)
                    ' Log the exception
                    SKCopy2Project.SKCopy2ProjectCommandExtension.Write_log("C:\temp\Promote_errors.log", "Error during promotion: " & ex.Message & ": " & IdeeTxt)
                End Try
            Catch ex As Exception
                SKCopy2Project.SKCopy2ProjectCommandExtension.Write_log("C:\temp\Promote_errors.log", ex.Message.ToString() & ": " & IdeeTxt)
            End Try
            'third change item to next
            Try
                m_conn.WebServiceManager.ItemService.UpdateItemLifeCycleStates(itemstoChange, itemstoChangeTo, "State changed from " & SKCopy2Project.SKCopy2ProjectCommandExtension.stateFromstr)
                SKCopy2Project.SKCopy2ProjectCommandExtension.Write_log("C:\temp\Promote.log", "Item State changed from " & SKCopy2Project.SKCopy2ProjectCommandExtension.stateFromstr)
                'SKCopy2Project.SKCopy2ProjectCommandExtension.filestoChangeTo
            Catch ex As Exception
                MessageBox.Show("Item change state did not complete successfully. " & ex.Message)
                SKCopy2Project.SKCopy2ProjectCommandExtension.Write_log("C:\temp\Promote_errors.log", ex.Message.ToString())
            End Try
            'During promotion from PL-Production to PL-WIP fill SKPL_DES_NAME with user 
            Dim editItem As ACW.Item = Nothing
            Try
                Dim stateFrom As String = SKCopy2Project.SKCopy2ProjectCommandExtension.stateFromstri
                If stateFrom = "PL-Production" Then
                    'find item property SKPL_DES_NAME 
                    Dim oItem As Item = m_conn.WebServiceManager.ItemService.GetLatestItemByItemMasterId(_item.MasterId)
                    'editItem = m_conn.WebServiceManager.ItemService.EditItems({_item.RevId}).First()
                    editItem = m_conn.WebServiceManager.ItemService.EditItems({oItem.RevId}).First()
                    Dim usrstr As String = m_conn.UserName
                    Dim oPropefs As ACW.PropDef() = m_conn.WebServiceManager.PropertyService.GetPropertyDefinitionsByEntityClassId("ITEM")
                    Dim propAndVals As New Dictionary(Of Autodesk.Connectivity.WebServices.PropDef, Object)
                    Dim propInstParams As List(Of PropInstParam) = New List(Of PropInstParam)()
                    For Each oPropdef As ACW.PropDef In oPropefs
                        If oPropdef.DispName = "SKPL_DES_NAME" Then
                            Dim propInstPar As PropInstParam = New PropInstParam()
                            propInstPar.PropDefId = oPropdef.Id
                            propInstPar.Val = usrstr
                            propInstParams.Add(propInstPar)
                            Exit For
                        End If
                    Next
                    Dim propInstParamsArray As PropInstParamArray = New PropInstParamArray()
                    propInstParamsArray.Items = propInstParams.ToArray()
                    Dim propInstParamsArrays As PropInstParamArray() = New PropInstParamArray(0) {}
                    propInstParamsArrays(0) = propInstParamsArray
                    Dim items As ACW.Item() = m_conn.WebServiceManager.ItemService.UpdateItemProperties(editItem.RevId.ToSingleArray, propInstParamsArrays)
                    itemSvc.UpdateAndCommitItems(items.ToArray())
                    SKCopy2Project.SKCopy2ProjectCommandExtension.Write_log("C:\temp\Promote.log", "Update and commit items from PL-Production to PL-WI")
                End If
            Catch ex As Exception
                m_conn.WebServiceManager.ItemService.UndoEditItems(editItem.Id.ToSingleArray)
                MessageBox.Show(m_conn.UserName & " did not update to 'SKPL_DES_NAME': " & ex.Message)
                SKCopy2Project.SKCopy2ProjectCommandExtension.Write_log("C:\temp\Promote_errors.log", ex.Message.ToString())
            End Try
            'last if primary is dgn and iam or ipt is found switch primary
            ''Try
            ''    If dgnIsPrimary <> Nothing And modelIsSecondary <> Nothing Then
            ''        Dim editItem As ACW.Item = m_conn.WebServiceManager.ItemService.EditItems({_item.RevId}).First()
            ''        Dim Primarylink As Long = m_conn.WebServiceManager.ItemService.GetItemFileAssociationsByItemIds(_item.Id.ToSingleArray, ACW.ItemFileLnkTypOpt.Primary).First.CldFileId
            ''        Dim secondaryLinks As Long() = Nothing
            ''        Try
            ''            Dim _itemfileassocs As ItemFileAssoc()
            ''            _itemfileassocs = m_conn.WebServiceManager.ItemService.GetItemFileAssociationsByItemIds(_item.Id.ToSingleArray, ACW.ItemFileLnkTypOpt.Secondary)
            ''            Dim ii As Integer = 0
            ''            If _itemfileassocs.Count > 0 Then
            ''                For Each itemfileassoc As ItemFileAssoc In _itemfileassocs
            ''                    ReDim Preserve secondaryLinks(ii)
            ''                    secondaryLinks(ii) = itemfileassoc.CldFileId
            ''                    Try
            ''                        If itemfileassoc.FileName.Split(".").Last.ToLower = "ipt" Or itemfileassoc.FileName.Split(".").Last.ToLower = "iam" Then
            ''                            modelIsSecondary = itemfileassoc.CldFileId
            ''                            secondaryLinks(ii) = Primarylink
            ''                        End If
            ''                    Catch ex As Exception
            ''                    End Try
            ''                    ii += 1
            ''                Next
            ''            End If
            ''            _itemfileassocs = Nothing
            ''            ii = 0
            ''        Catch ex As Exception
            ''            secondaryLinks = Nothing
            ''        End Try
            ''        Dim item As ACW.Item = m_conn.WebServiceManager.ItemService.UpdateItemFileAssociations(editItem.RevId, modelIsSecondary, Nothing, dgnIsPrimary.ToSingleArray, Nothing, Nothing, Nothing)
            ''    End If
            ''Catch ex As Exception
            ''    m_conn.WebServiceManager.ItemService.UndoEditItems(_item.Id.ToSingleArray)
            ''End Try
        Next i
        _sk_ok.Text = "Done"
        Try
            'MessageBox.Show("Promote is Done! Press F5 to refresh")
            Me.Close()
            Try
                SendKeys.Send("{F5}")
            Catch ex As Exception
            End Try
        Catch ex As Exception
            SKCopy2Project.SKCopy2ProjectCommandExtension.Write_log("C:\temp\Promote_errors.log", ex.Message.ToString())
        End Try
        Dim eContext As ICommandContext = SKCopy2Project.SKCopy2ProjectCommandExtension.eContext
        eContext.ForceRefresh = True
    End Sub
    Private Sub sym_Updateitem(fileIterationId As Long)
        Dim itemSvc As ItemService = m_conn.WebServiceManager.ItemService
        itemSvc.AddFilesToPromote(New Long() {fileIterationId}, ItemAssignAll.Yes, True)
        Dim timestamp As DateTime
        Dim promoteOrder As GetPromoteOrderResults = itemSvc.GetPromoteComponentOrder(timestamp)
        If promoteOrder.PrimaryArray IsNot Nothing AndAlso promoteOrder.PrimaryArray.Length > 0 Then itemSvc.PromoteComponents(timestamp, promoteOrder.PrimaryArray)
        If promoteOrder.NonPrimaryArray IsNot Nothing AndAlso promoteOrder.NonPrimaryArray.Length > 0 Then itemSvc.PromoteComponentLinks(promoteOrder.NonPrimaryArray)
        Dim promoteResult As ItemsAndFiles = itemSvc.GetPromoteComponentsResults(timestamp)
        Dim items As List(Of Item) = New List(Of Item)()
        For i As Integer = 0 To promoteResult.ItemRevArray.Length - 1
            If promoteResult.StatusArray(i) > 1 Then
                items.Add(promoteResult.ItemRevArray(i))
            End If
        Next
        If items.Count > 0 Then itemSvc.UpdateAndCommitItems(items.ToArray())
    End Sub
    Private Sub _sk_cancel_Click(sender As Object, e As EventArgs) Handles _sk_cancel.Click
        Close()
        Exit Sub
    End Sub
    Private Sub DataGridView1_CellContentClick(sender As Object, e As DataGridViewCellEventArgs) Handles DataGridView1.CellContentClick
    End Sub
    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        Dim linkTypeOptions = ItemFileLnkTypOpt.Primary Or ItemFileLnkTypOpt.PrimarySub Or ItemFileLnkTypOpt.Secondary
        Dim adminSvc = m_conn.WebServiceManager.AdminService
        Dim allUsers As User() = adminSvc.GetAllUsers()
        Dim allUserIds = allUsers.[Select](Function(u) u.Id).ToArray()
        Dim allGroups As Group() = adminSvc.GetAllGroups()
        Dim allGroupIds = allGroups.[Select](Function(g) g.Id).ToArray()
        Dim allGroupInfos As GroupInfo() = adminSvc.GetGroupInfosByGroupIds(allGroupIds)
        Dim userIdToGroupInfos = allUserIds.ToDictionary(Function(uid) uid, Function(uid) allGroupInfos.Where(Function(gi) gi.Users IsNot Nothing AndAlso gi.Users.Any(Function(u) u.Id = uid)))
        Dim sym_curr_user_groups = New List(Of String)
        Dim refs = New List(Of VDF.Vault.Currency.FileSystem.FileReference)()
        Dim test As String = m_conn.WebServiceManager.AdminService.Session.User.Id
        test = "115"
        Dim test2 As String = ""
        For Each user In allUsers
            Console.WriteLine("User {0} Groups (Direct Membership):", user.Name)
            Dim groupInfosForUser = userIdToGroupInfos(user.Id)
            If user.Name.Contains("Jouni") Then
                test2 = user.Name
            End If
            If user.Id = test Then
                For Each groupInfoForUser In groupInfosForUser
                    Dim groupForUser As Group = groupInfoForUser.Group
                    Console.WriteLine(" Group {0}", groupForUser.Name)
                    sym_curr_user_groups.Add(groupForUser.Name)
                Next
            End If
        Next
    End Sub
    Private Sub CheckIfFileExistInVault(ByVal VaultFileName As String)
        'Start at the root Folder.
        Dim root As VDF.Vault.Currency.Entities.Folder = m_conn.FolderManager.RootFolder
        Dim oFileIteration As VDF.Vault.Currency.Entities.FileIteration = Nothing
        Dim file_error As Boolean
        Try
            ' Get the FileIteration
            ' file_error = getFileIteration(VaultFileName, m_conn)
            file_error = IsFileCheckedOut(VaultFileName, m_conn)
            If file_error Then
                If test_file_islocked_readonly Is Nothing Then
                    test_file_islocked_readonly = New List(Of String)
                End If
                test_file_islocked_readonly.Add(VaultFileName)
            End If
            If oFileIteration Is Nothing Then
                VaultFileIsExisting = False
                Exit Sub
            Else
                'MsgBox("File found" &  vaultfilename)
                VaultFileIsExisting = True
                Exit Sub
            End If
        Catch ex As Exception
            SKCopy2Project.SKCopy2ProjectCommandExtension.Write_log("C:\temp\Promote_errors.log", "Error checking file: " & VaultFileName & " - " & ex.Message)
        End Try
    End Sub
    Public Function getFileIteration(ByVal nameOfFile As String, ByVal connection As VDF.Vault.Currency.Connections.Connection) As Boolean
        Dim conditions As ACW.SrchCond()
        ReDim conditions(0)
        Dim lCode As Long = 1
        Dim Defs As ACW.PropDef() = connection.WebServiceManager.PropertyService.GetPropertyDefinitionsByEntityClassId("FILE")
        Dim Prop As ACW.PropDef = Nothing
        For Each def As ACW.PropDef In Defs
            If def.DispName = "File Name" Then
                Prop = def
            End If
        Next def
        Dim searchCondition As ACW.SrchCond = New ACW.SrchCond()
        searchCondition.PropDefId = Prop.Id
        searchCondition.PropTyp = ACW.PropertySearchType.SingleProperty
        searchCondition.SrchOper = lCode
        searchCondition.SrchTxt = nameOfFile
        conditions(0) = searchCondition
        ' search for files
        Dim FileList As List(Of Autodesk.Connectivity.WebServices.File) = New List(Of Autodesk.Connectivity.WebServices.File)()
        Dim sBookmark As String = String.Empty
        Dim Status As ACW.SrchStatus = Nothing
        While (Status Is Nothing OrElse FileList.Count < Status.TotalHits)
            Dim files As Autodesk.Connectivity.WebServices.File() = connection.WebServiceManager.DocumentService.FindFilesBySearchConditions(conditions, Nothing, Nothing, True, True, sBookmark, Status)
            If (Not files Is Nothing) Then
                FileList.AddRange(files)
            End If
        End While
        Dim oFileIteration As VDF.Vault.Currency.Entities.FileIteration = Nothing
        Dim file_error As Boolean = False
        Dim file_error2 As Boolean = False
        For i As Integer = 0 To FileList.Count - 1
            If FileList(i).Name = nameOfFile Then
                oFileIteration = New VDF.Vault.Currency.Entities.FileIteration(connection, FileList(i))
                Dim filelocked As Boolean = oFileIteration.IsLocked
                Dim filecheckedout As Boolean = oFileIteration.IsCheckedOut
                If filelocked = True Then
                    file_error2 = True
                    'test_file_islocked_readonly.Add(nameOfFile)
                End If
                If filecheckedout = True Then
                    file_error = True
                End If
            End If
        Next
        Return file_error
    End Function
    Public Function IsFileCheckedOut(ByVal nameOfFile As String, ByVal connection As VDF.Vault.Currency.Connections.Connection) As Boolean
        Dim conditions(0) As ACW.SrchCond
        ' Get File Name property definition more efficiently
        Dim Prop As ACW.PropDef = connection.WebServiceManager.PropertyService.GetPropertyDefinitionsByEntityClassId("FILE").
            FirstOrDefault(Function(def) def.DispName = "File Name")
        If Prop Is Nothing Then Return False
        ' Create search condition
        conditions(0) = New ACW.SrchCond() With {
            .PropDefId = Prop.Id,
            .PropTyp = ACW.PropertySearchType.SingleProperty,
            .SrchOper = 1, ' Equals
            .SrchTxt = nameOfFile
        }
        ' Search for files with pagination
        Dim sBookmark As String = String.Empty
        Dim Status As ACW.SrchStatus = Nothing
        ' Only get the first batch of results since we're looking for an exact match
        Dim files As Autodesk.Connectivity.WebServices.File() =
            connection.WebServiceManager.DocumentService.FindFilesBySearchConditions(
                conditions, Nothing, Nothing, True, True, sBookmark, Status)
        ' Check if any matching file is checked out
        For Each file In files
            If file.Name = nameOfFile Then
                Dim fileIteration = New VDF.Vault.Currency.Entities.FileIteration(connection, file)
                Return fileIteration.IsCheckedOut
            End If
        Next
        Return False
    End Function
    Private Function GetReasonFromStatus(status As Integer) As String
        ' Interpret status codes - you may need to adjust these based on actual Vault status codes
        Select Case status
            Case 0
                Return "Item is not ready for promotion"
            Case 1
                Return "Item is locked or checked out by another user"
            Case Else
                Return "Unknown reason (Status: " & status.ToString() & ")"
        End Select
    End Function
    Private Function CheckFilesWithNewState() As List(Of String)
        Dim filesWithNewState As New List(Of String)
        ' Process each file in the DataGridView
        For intI As Integer = 0 To DataGridView1.Rows.Count - 1
            Dim filename As String = DataGridView1.Rows(intI).Cells(1).Value
            Dim masterId As Long = Convert.ToInt64(DataGridView1.Rows(intI).Cells(4).Value)
            Dim visited As New HashSet(Of Long)
            ' Check all dependencies recursively
            CheckDependenciesForNewState(masterId, filesWithNewState, visited)
        Next
        Return filesWithNewState
    End Function
    Private Sub CheckDependenciesForNewState(masterId As Long, ByRef filesWithNewState As List(Of String), ByRef visited As HashSet(Of Long))
        ' Skip if already visited to prevent infinite recursion
        Dim assocHashSet As New HashSet(Of Long)
        ' Update UI to show progress
        Labeltext.Text = $"Checking dependencies... ({filesWithNewState.Count} files found)"
        Application.DoEvents() ' Allow UI to refresh
        Try
            Dim latestFile As ACW.File = m_conn.WebServiceManager.DocumentService.GetLatestFileByMasterId(masterId)
            Dim fileIds() As Long = {latestFile.Id}
            ' Dim fileAssocs As ACW.FileAssocLite() = m_conn.WebServiceManager.DocumentService.GetFileAssociationLitesByIds(fileIds, ACW.FileAssocAlg.LatestConsumable, ACW.FileAssociationTypeEnum.Dependency, True, ACW.FileAssociationTypeEnum.Dependency, True, False, False, False) 'hakee myös ylätason
            'Dim fileAssocs As ACW.FileAssocLite() = m_conn.WebServiceManager.DocumentService.GetFileAssociationLitesByIds(fileIds, ACW.FileAssocAlg.LatestConsumable, ACW.FileAssociationTypeEnum.Dependency, False, ACW.FileAssociationTypeEnum.Dependency, False, True, False, False) 'päätaso vain
            Dim fileAssocs As ACW.FileAssocLite() = m_conn.WebServiceManager.DocumentService.GetFileAssociationLitesByIds(fileIds, ACW.FileAssocAlg.LatestConsumable, ACW.FileAssociationTypeEnum.Dependency, False, ACW.FileAssociationTypeEnum.Dependency, True, True, False, False) 'sama kuin gridiä tehdessä
            ' Show progress in status bar or label
            If fileAssocs.Length > 0 Then
                ProgressBar1.Maximum = fileAssocs.Length
                ProgressBar1.Value = 0
                For i As Integer = 0 To fileAssocs.Length - 1
                    Dim fileAssoc As ACW.FileAssocLite = fileAssocs(i)
                    If Not assocHashSet.Contains(fileAssoc.CldFileId) Then
                        Dim oAssocFile As ACW.File = m_conn.WebServiceManager.DocumentService.GetFileById(fileAssoc.CldFileId)
                        Dim fileFolder As ACW.Folder = m_conn.WebServiceManager.DocumentService.GetFolderById(oAssocFile.FolderId)
                        If Not oAssocFile.MasterId = masterId Then
                            assocHashSet.Add(fileAssoc.CldFileId)
                            ' Check if file is in "New" state
                            If oAssocFile.FileLfCyc.LfCycStateName.ToLowerInvariant() = "new" Then
                                Dim fullPath = fileFolder.FullName & "\" & oAssocFile.Name
                                If Not filesWithNewState.Contains(fullPath) Then
                                    ' Change from just the filename to include the full path
                                    filesWithNewState.Add(fullPath)
                                    Debug.WriteLine("Used file: " & oAssocFile.Name & " Path: " & fileFolder.FullName)
                                    iRow += 1
                                    Debug.WriteLine("iRow: " & iRow & " MasterId: " & oAssocFile.MasterId)
                                    Debug.WriteLine("Name: " & oAssocFile.Name & " FullName: " & fileFolder.FullName)
                                    Debug.WriteLine("CatName: " & oAssocFile.Cat.CatName & " LfCycStateName: " & oAssocFile.FileLfCyc.LfCycStateName)
                                    ' Debug.WriteLine("Label: " & oFile.FileRev.Label & " CreateDate: " & oAssocFile.CreateDate)
                                    Debug.WriteLine("CreateUserName: " & oAssocFile.CreateUserName & " Email: " & m_conn.WebServiceManager.AdminService.GetUserByUserId(oAssocFile.CreateUserId).Email)
                                    ListBox1.Items.Add(oAssocFile.Name)
                                    If ListBox1.Items.Count > 0 Then
                                        ListBox1.SelectedIndex = ListBox1.Items.Count - 1
                                    End If
                                Else
                                    Debug.WriteLine("Duplicate name : " & fileFolder.FullName & " Path: " & oAssocFile.MasterId)
                                End If
                                Application.DoEvents()
                            End If
                        End If
                    End If
                    ' Update progress with bounds checking
                    If i < ProgressBar1.Maximum Then
                        ProgressBar1.Value = i + 1
                    End If
                    Application.DoEvents()
                Next
            Else
                ' No associations found
                Labeltext.Text = "No dependencies found for this file"
            End If
        Catch ex As Exception
            Debug.WriteLine("Error: " & ex.Message)
        End Try
    End Sub
    Private Sub Button2_Click(sender As Object, e As EventArgs) Handles Button2.Click
        ' Show progress controls
        ProgressBar1.Visible = True
        ListBox1.Visible = True
        Labeltext.Visible = True
        ListBox1.Items.Clear()
        ' Ensure ListBox is on top
        ListBox1.BringToFront()
        ' Original code to check for locked/checked out files
        test_file_islocked_readonly = Nothing
        For intI As Integer = DataGridView1.Rows.Count - 1 To 0 Step -1
            Dim filename As String = DataGridView1.Rows(intI).Cells(1).Value
            If filename.Contains(".iam") Or filename.Contains(".dwg") Or filename.Contains(".ipt") Then
                CheckIfFileExistInVault(filename)
            End If
        Next intI
        If test_file_islocked_readonly IsNot Nothing AndAlso test_file_islocked_readonly.Count > 0 Then
            Dim message = String.Join(Environment.NewLine, test_file_islocked_readonly)
            MessageBox.Show("Files are checked out: " & vbNewLine & message)
        End If
        Try
            ' New code to check for files with "New" state
            Dim filesWithNewState As List(Of String) = CheckFilesWithNewState()
            If filesWithNewState.Count > 0 Then
                Dim newStateMessage = String.Join(Environment.NewLine, filesWithNewState.ToArray())
                Dim msgResult = MessageBox.Show("Files with 'New' state found: " & vbNewLine & newStateMessage, "Warning", MessageBoxButtons.OK)
                If msgResult = DialogResult.OK Then
                    Clipboard.SetText(newStateMessage)
                End If
            Else
                ListBox1.Items.Add("No files with 'New' state found")
            End If
        Catch ex As Exception
            Debug.WriteLine("Error: " & ex.Message)
        End Try
        ' Hide progress controls when done
        ProgressBar1.Visible = False
        Labeltext.Visible = False
    End Sub
    Private Sub ComboBox1_SelectedIndexChanged(sender As Object, e As EventArgs) Handles ComboBox1.SelectedIndexChanged
    End Sub
End Class
