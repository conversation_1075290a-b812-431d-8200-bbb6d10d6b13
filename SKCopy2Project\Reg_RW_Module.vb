﻿Module Main
    Public Function Reg_R(ByVal strKey As String, ByVal strDefaultKeyValue As String) As String
        Dim strCompany As String = My.Application.Info.CompanyName
        Dim strProduct As String = My.Application.Info.ProductName
        Dim strTitle As String = My.Application.Info.Title
        Dim strVersion As String = My.Application.Info.Version.ToString.Split(".")(0)
        Dim strKeyValue As String = Nothing

        'read from registry
        Try


            If My.Computer.Registry.GetValue("HKEY_CURRENT_USER\Software\" & strCompany & "\" & strProduct & "\" & strVersion & "\" & strTitle, strKey, Nothing) Is Nothing Then

                Try
                    My.Computer.Registry.CurrentUser.CreateSubKey("Software\" & strCompany & "\" & strProduct & "\" & strVersion & "\" & strTitle)
                Catch ex As Exception

                End Try

                Try
                    My.Computer.Registry.SetValue("HKEY_CURRENT_USER\Software\" & strCompany & "\" & strProduct & "\" & strVersion & "\" & strTitle, strKey, strDefaultKeyValue)
                    strKeyValue = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\Software\" & strCompany & "\" & strProduct & "\" & strVersion & "\" & strTitle, strKey, Nothing)
                Catch ex As Exception

                End Try
            Else
                strKeyValue = My.Computer.Registry.GetValue("HKEY_CURRENT_USER\Software\" & strCompany & "\" & strProduct & "\" & strVersion & "\" & strTitle, strKey, Nothing)

            End If



        Catch ex As Exception
            strKeyValue = strDefaultKeyValue
        End Try

        Return strKeyValue

    End Function
    Public Function Reg_W(ByVal strKey As String, ByVal strKeyValue As String) As String
        Dim strCompany As String = My.Application.Info.CompanyName
        Dim strProduct As String = My.Application.Info.ProductName
        Dim strTitle As String = My.Application.Info.Title
        Dim strVersion As String = My.Application.Info.Version.ToString.Split(".")(0)


        'read from registry
        Try
            My.Computer.Registry.SetValue("HKEY_CURRENT_USER\Software\" & strCompany & "\" & strProduct & "\" & strVersion & "\" & strTitle, strKey, strKeyValue)

        Catch ex As Exception

            Try
                If My.Computer.Registry.GetValue("HKEY_CURRENT_USER\Software\" & strCompany & "\" & strProduct & "\" & strVersion & "\" & strTitle, strKey, Nothing) Is Nothing Then

                    Try
                        My.Computer.Registry.CurrentUser.CreateSubKey("Software\" & strCompany & "\" & strProduct & "\" & strVersion & "\" & strTitle)
                    Catch

                    End Try

                    Try
                        My.Computer.Registry.SetValue("HKEY_CURRENT_USER\Software\" & strCompany & "\" & strProduct & "\" & strVersion & "\" & strTitle, strKey, strKeyValue)

                    Catch

                    End Try
                Else
                    My.Computer.Registry.SetValue("HKEY_CURRENT_USER\Software\" & strCompany & "\" & strProduct & "\" & strVersion & "\" & strTitle, strKey, strKeyValue)

                End If
            Catch

            End Try


            strKeyValue = strKeyValue
        End Try

        Return strKeyValue
    End Function
End Module
