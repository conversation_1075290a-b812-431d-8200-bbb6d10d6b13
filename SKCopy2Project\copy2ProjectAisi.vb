﻿Imports Inventor
Imports Excel = Microsoft.Office.Interop.Excel
Imports Autodesk.Connectivity.Explorer.Extensibility
Imports Autodesk.Connectivity.WebServices
Imports Autodesk.Connectivity.WebServicesTools
Imports ACW = Autodesk.Connectivity.WebServices
Imports VDF = Autodesk.DataManagement.Client.Framework
Imports VDFVF = Autodesk.DataManagement.Client.Framework.Vault.Forms
Imports VDFVCP = Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties
Imports Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections


Public Class copy2ProjectAisi
    Private m_conn As Connection = SKCopy2Project.SKCopy2ProjectCommandExtension.m_conn
    Private projectfolderName As String
    Private projectfolderID As Long

    Private SK_ITEM As String
    Private Shared rownumb As Integer = 21
    Public Shared masterArr(rownumb, 0) As String
    Public Shared drwArr(,) As String
    Public Shared derivedArr(,) As String
    Public Shared Materials430() As String
    Public Shared Materials304_316(,) As String
    Private fileiteration As Object
    Private SKnumberingcheme As Integer
    Private numbofModels As Integer = Nothing
    Private numbofDeriveds As Integer = Nothing
    Private numbofDrawings As Integer = Nothing
    Private numbofTotal As Integer = Nothing
    Private numbofSteps As Double = Nothing
    Private progressStep As Double = Nothing
    Private proggStep As Integer = 1
    '
    Private Shared SK_Shipserie As String()
    Private Shared sk_ship As Object(,)
    Private Shared SK_ShipCode As String() 'here it is YARD
    Private Shared SK_Yard As String()
    Private Shared SK_Area As Object(,)
    Private Shared sk_AreaCode As String()
    Private Shared isundermod As Boolean = False





    ' Public Sub New(conn As Connection)
    '     InitializeComponent()

    '    m_conn = co
    '  End Sub
    Private Sub copy2ProjectAisi_Load(sender As Object, e As EventArgs) Handles MyBase.Load

        Try
            Dim settings As New VDF.Vault.Forms.Settings.LoginSettings
            settings.AutoLoginMode = VDFVF.Settings.LoginSettings.AutoLoginModeValues.RestoreAndExecute
            '   Dim ee As CommandItemEventArgs = SKCopy2Project.SKCopy2ProjectCommandExtension.ee
            '  m_conn = ee.Context.Application.Connection
            '  m_conn = VDF.Vault.Forms.Library.Login(settings)
            If m_conn Is Nothing Then
                MessageBox.Show("Invalid login")
                Exit Sub
            End If
            'let's get correct numberinscheme

            Dim mgr As WebServiceManager = m_conn.WebServiceManager
            Dim entityClassId As String = VDF.Vault.Currency.Entities.EntityClassIds.Files
            Dim fNumchemes As NumSchm() = mgr.NumberingService.GetNumberingSchemes(entityClassId, NumSchmType.ApplicationDefault)

            For Each fNumcheme As ACW.NumSchm In fNumchemes
                If fNumcheme.Name = "SK-Custom-Code" Then
                    SKnumberingcheme = fNumcheme.SchmID
                End If
            Next


        Catch ex As Exception
            MessageBox.Show(ex.Message & " Mek:LoginError")
        End Try

        ' MessageBox.Show("Reading Register")


        ' m_conn = Autodesk.DataManagement.Client.Framework.Vault.Services.IVaultConnectionManagerService(getexistingconnection)
        'read folder from registry
        Dim regSK_Projectfolder As String = Reg_R("SK_Projectfolder", "$/")
        m_sourcePathTextBox.Text = regSK_Projectfolder
        Dim regSHIP_SERIE As String = Reg_R("SHIP_SERIE", "")
        ComboBox1.Text = Reg_R("SHIP_SERIE", "")
        Dim regSK_ITEM As String = Reg_R("SK_ITEM", "")
        TextBox2.Text = Reg_R("SK_ITEM", "")
        Dim regAREA As String = Reg_R("AREA", "")
        TextBox3.Text = regAREA 'Reg_R("AREA", "")
        Dim regAREA_NAME As String = Reg_R("AREA_NAME", "")
        ComboBox3.Text = Reg_R("AREA_NAME", "")
        Dim regDECK As String = Reg_R("DECK", "")
        TextBox5.Text = Reg_R("DECK", "")
        Dim regFIREZONE As String = Reg_R("FIREZONE", "")
        TextBox6.Text = Reg_R("FIREZONE", "")
        Dim regYARD As String = Reg_R("YARD", "")
        ComboBox2.Text = Reg_R("YARD", "")
        Dim regInsert As String = Reg_R("INSERT", "False")
        CheckBox1.Checked = regInsert



        If ComboBox1.Text <> "" Then

            ' MessageBox.Show("Getting areacodes")
            Try


                getAreaCodes() 'get active area codes
                getYard()
                If regYARD <> "" Then
                    ComboBox2.Text = regYARD
                End If
            Catch ex As Exception

            End Try
        End If


        'if not C -file then disable text fields
        Dim selectedFile As ACW.File = SKCopy2Project.SKCopy2ProjectCommandExtension.exportselectedFile
        Dim oFileNameFirstLetter As String = selectedFile.Name.Substring(0, 1)

        If oFileNameFirstLetter.ToLower <> "c" Then
            Button1.Enabled = False
            TextBox2.Enabled = False
            ComboBox1.Enabled = False
            ComboBox2.Enabled = False
            TextBox3.Enabled = False
            ComboBox3.Enabled = False
            TextBox5.Enabled = False
            TextBox6.Enabled = False


        Else
            Button1.Enabled = True
            TextBox2.Enabled = True
            ComboBox1.Enabled = True
            ComboBox2.Enabled = True
            TextBox3.Enabled = True
            ComboBox3.Enabled = True
            TextBox5.Enabled = True
            TextBox6.Enabled = True


        End If






        If regSK_Projectfolder IsNot Nothing Then
            'lets get the folder id for project
            Dim projectFolder As VDF.Vault.Currency.Entities.Folder = findfolder(m_sourcePathTextBox.Text)

            Me.Text = "SK Copy to Project from 304 to 316, version " & Me.GetType.Assembly.GetName.Version.Major & "." & Me.GetType.Assembly.GetName.Version.Minor
        End If
        'get shipserie from my documents\Vault\System\MPS\LinkIt_Shared\Data\ShipSeries

        '   MessageBox.Show("Reading shipdata folder")
        Try
            Dim path As String = My.Computer.FileSystem.SpecialDirectories.MyDocuments & "\Vault\System\MPS\LinkIt_Shared\Data\ShipSeries"
            '  MessageBox.Show(path)
            'list files in folder.
            Dim shipserietemp As String() = Directory.GetFiles(path, "*.xl*", SearchOption.TopDirectoryOnly)
            ''create a list for sk_shipserie
            If shipserietemp.Length > 0 Then
                Dim ii As Integer = 0
                For i As Integer = 0 To shipserietemp.Length - 1
                    If shipserietemp(i).Contains("~$") = False Then
                        'remove xls*
                        ReDim Preserve SK_Shipserie(ii)
                        SK_Shipserie(ii) = (shipserietemp(i).Split("\").Last.Split(".").First).ToUpper
                        ii += 1
                    End If
                Next
                ''Dim ii As Integer = 0
                ''For Each foundFile As String In My.Computer.FileSystem.GetFiles(path)
                ''    If foundFile.Split(".").Last Like "xl*" Then
                ''        ReDim Preserve SK_Shipserie(ii)
                ''        SK_Shipserie(ii) = (foundFile.Split(".").First).Split("\").Last.ToUpper
                ''        ii += 1
                ''    End If

                ''Next


                'udpate list in combo
                Dim col As New AutoCompleteStringCollection
                '  MessageBox.Show("Creating combobox list")

                makeCombolist(ComboBox1, SK_Shipserie)
            End If

        Catch ex As Exception
            MessageBox.Show(ex.Message.ToString)
        End Try

        'get 430 materials to array
        Dim objReader As System.IO.StreamReader = Nothing
        Dim fname As String = Nothing

        Try
            Dim path As String = My.Computer.FileSystem.SpecialDirectories.MyDocuments & "\Vault\System\MPS\LinkIt_Shared\Data\"
            fname = path & "430 materials table.txt"
            objReader = New System.IO.StreamReader(fname)
            Dim i As Integer = -1
            Do While objReader.Peek() <> -1
                Dim oTextline As String = objReader.ReadLine()
                If oTextline.Substring(0, 1) <> "!" Then
                    i += 1
                    ReDim Preserve Materials430(i)
                    Materials430(i) = oTextline
                End If

            Loop

        Catch ex As Exception
            MessageBox.Show("Cannot find " & fname)
            Exit Sub
        End Try
        If objReader IsNot Nothing Then
            objReader.Close()
        End If

        'Read comparison matrix 304 and 316
        Try
            Dim path As String = My.Computer.FileSystem.SpecialDirectories.MyDocuments & "\Vault\System\MPS\LinkIt_Shared\Data\"
            fname = path & "AISI 316 materials table.txt"
            objReader = New System.IO.StreamReader(fname)
            Dim i As Integer = -1
            Do While objReader.Peek() <> -1
                Dim oTextline As String = objReader.ReadLine()
                If oTextline.Substring(0, 1) <> "!" Then
                    i += 1
                    ReDim Preserve Materials304_316(1, i)
                    Materials304_316(0, i) = Trim(oTextline.Split(vbTab).First)
                    Materials304_316(1, i) = Trim(oTextline.Split(vbTab).Last)

                End If
            Loop
        Catch ex As Exception
            MessageBox.Show("Cannot find " & fname)
            Exit Sub
        End Try

        If objReader IsNot Nothing Then
            objReader.Close()
        End If




    End Sub

    Private Function makeCombolist(cBox As ComboBox, m_list As String())
        If Not m_list Is Nothing Then
            cBox.Items.Clear()

            For Each str As String In m_list
                cBox.Items.Add(str)
                '   MessageBox.Show(str)
            Next
        End If
    End Function

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        Dim settings = New VDFVF.Settings.SelectEntitySettings()
        settings.PersistenceKey = "MekSystems.SKCopy2ProjectApp.MyDialog"
        settings.MultipleSelect = False
        settings.ActionableEntityClassIds.Add(VDF.Vault.Currency.Entities.EntityClassIds.Folder)
        Dim mgr As WebServiceManager = m_conn.WebServiceManager

        '   m_conn = SKCopy2Project.SKCopy2ProjectCommandExtension.ee
        ' run the dialog
        Dim results = VDFVF.Library.SelectEntity(m_conn, settings)

        ' fill the textbox with the selected file path
        If results IsNot Nothing Then
            Dim selected = results.SelectedEntities.SingleOrDefault()
            If selected Is Nothing Then
                Return
            End If
            Dim selectedFolder = TryCast(selected, VDF.Vault.Currency.Entities.Folder)
            If selectedFolder Is Nothing Then
                Return
            End If
            ' m_conn.FileManager.LoadParentFolders(selectedFolder.ToSingleArray())
            m_sourcePathTextBox.Text = selectedFolder.FullName
        End If


        Reg_W("SK_Projectfolder", m_sourcePathTextBox.Text)
        'lets get the folder id for project
        Dim projectFolder As VDF.Vault.Currency.Entities.Folder = findfolder(m_sourcePathTextBox.Text)
        projectfolderID = projectFolder.Id
        projectfolderName = m_sourcePathTextBox.Text

        'lets find components


    End Sub

    Private Sub Button2_Click(sender As Object, e As EventArgs) Handles Button2.Click
        '  Dim logoutsettings As New VDFVF.Settings.LogoutSettings
        ' logoutsettings.LogOutMessage = Nothing

        'Dim settings As New VDF.Vault.Forms.Settings.LoginSettings
        'VDFVF.Library.Logout(m_conn, logoutsettings, Nothing)

        Close()


    End Sub

    Function findfolder(ByVal folderstring As String) As VDF.Vault.Currency.Entities.Folder
        Dim myFldrCol As System.Collections.Generic.List _
           (Of VDF.Vault.Currency.Entities.Folder)
        myFldrCol = m_conn.FolderManager.
    GetChildFolders(m_conn.FolderManager.RootFolder,
                                           True, False)


        ' Get the folder to add the new file to change
        ' the FullName test to a Folder in your vault
        Dim myFolder As _
        VDF.Vault.Currency.Entities.Folder = Nothing

        For Each Flder As _
        VDF.Vault.Currency.Entities.Folder In myFldrCol
            '    If Flder.NumberOfChildren > 0 Then
            ' myFolder = findsubfolder(folderstring, Flder)
            '   Else

            If Flder.FullName = folderstring Then
                myFolder = Flder
                Exit For
            End If
            '  End If
        Next
        Return myFolder
    End Function

    Private Sub Button3_Click(sender As Object, e As EventArgs) Handles Button3.Click  'OK button

        Labeltext.Text = ""
        Try 'main try
            Reg_W("SHIP_SERIE", ComboBox1.Text.ToUpper)
            Reg_W("SK_ITEM", TextBox2.Text.ToUpper)
            Reg_W("AREA", TextBox3.Text.ToUpper)
            Reg_W("AREA_NAME", ComboBox3.Text.ToUpper)
            Reg_W("DECK", TextBox5.Text.ToUpper)
            Reg_W("FIREZONE", TextBox6.Text.ToUpper)
            Reg_W("YARD", ComboBox2.Text.ToUpper)
            Reg_W("SK_Projectfolder", m_sourcePathTextBox.Text)
            Reg_W("INSERT", CheckBox1.Checked.ToString)

            Button3.Enabled = False
            Try


                Dim projectFolder As VDF.Vault.Currency.Entities.Folder = findfolder(m_sourcePathTextBox.Text)
                projectfolderID = projectFolder.Id
                projectfolderName = m_sourcePathTextBox.Text
            Catch ex As Exception
                MessageBox.Show("Check the Project folder")
                Exit Sub
            End Try



            If TextBox2.Text IsNot Nothing Then ' first if
                'lets us start a copy
                'component selected

                ' Dim instRow As Double = Nothing

                ' we only have one item selected, which is the expected behavior

                Dim selection As ISelection = SKCopy2Project.SKCopy2ProjectCommandExtension.exportselection
                Dim mgr As WebServiceManager = m_conn.WebServiceManager 'e.Context.Application.Connection.WebServiceManager
                Dim bAddedAttachment As Boolean = False
                Dim fileAssocParamListdrw As New ArrayList()
                Dim fileassocparamsdrw As ACW.FileAssocParam() = Nothing
                Dim tempI As Integer = 0
                Dim fi As Integer = 0
                Dim newfile As ACW.File
                ' Look of the File object.  How we do this depends on what is selected.
                Dim selectedFile As ACW.File = SKCopy2Project.SKCopy2ProjectCommandExtension.exportselectedFile
                If selection.TypeId = SelectionTypeId.File Then
                    ' our ISelection.Id is really a File.MasterId
                    selectedFile = mgr.DocumentService.GetLatestFileByMasterId(selection.Id)
                ElseIf selection.TypeId = SelectionTypeId.FileVersion Then
                    ' our ISelection.Id is really a File.Id
                    selectedFile = mgr.DocumentService.GetFileById(selection.Id)
                End If

                If selectedFile Is Nothing Then 'second if
                    MessageBox.Show("Selection is not a file.")
                ElseIf selectedFile.Name.Split(".").Last <> "iam" And selectedFile.Name.Split(".").Last <> "ipt" Then
                    MessageBox.Show("Selected file is not assembly or part ")

                Else



                    'is the assembly ERPTYPE PAR*
                    Dim props As VDFVCP.PropertyDefinitionDictionary =
     m_conn.PropertyManager.GetPropertyDefinitions(
   VDF.Vault.Currency.Entities.EntityClassIds.Files,
       Nothing, VDFVCP.PropertyDefinitionFilter.IncludeAll)


                    Dim myKeyValPair As KeyValuePair _
                     (Of String, VDFVCP.PropertyDefinition)
                    Dim myERPTYPE As VDFVCP.PropertyDefinition _
                                           = Nothing
                    Dim propDef As VDFVCP.PropertyDefinition
                    For Each myKeyValPair In props
                        ' Property definition from KeyValuePair
                        propDef = myKeyValPair.Value()
                        ' Using the display name to identify
                        ' the PropertyDefinition
                        If propDef.DisplayName =
                         "ERPTYPE" Then
                            'It is the PropertyDefinition
                            myERPTYPE = propDef

                        End If
                    Next

                    Dim mySK_ITEM As VDFVCP.PropertyDefinition _
                                           = Nothing
                    Dim propDef1 As VDFVCP.PropertyDefinition
                    For Each myKeyValPair In props
                        ' Property definition from KeyValuePair
                        propDef1 = myKeyValPair.Value()
                        ' Using the display name to identify
                        ' the PropertyDefinition
                        If propDef1.DisplayName =
                         "SK_ITEM" Then
                            'It is the PropertyDefinition
                            mySK_ITEM = propDef1
                        End If
                    Next

                    Dim myComment As VDFVCP.PropertyDefinition _
                                           = Nothing
                    Dim propDef2 As VDFVCP.PropertyDefinition
                    For Each myKeyValPair In props
                        ' Property definition from KeyValuePair
                        propDef2 = myKeyValPair.Value()
                        ' Using the display name to identify
                        ' the PropertyDefinition
                        If propDef2.DisplayName =
                         "Comment" Then
                            'It is the PropertyDefinition
                            myComment = propDef2
                        End If
                    Next

                    Dim myMATDESCR As VDFVCP.PropertyDefinition _
                                           = Nothing
                    Dim propDef3 As VDFVCP.PropertyDefinition
                    For Each myKeyValPair In props
                        ' Property definition from KeyValuePair
                        propDef3 = myKeyValPair.Value()
                        ' Using the display name to identify
                        ' the PropertyDefinition
                        If propDef3.DisplayName =
                         "MATDESCR" Then
                            'It is the PropertyDefinition
                            myMATDESCR = propDef3
                        End If
                    Next

                    Dim myMATCODE As VDFVCP.PropertyDefinition _
                                           = Nothing
                    Dim propDef4 As VDFVCP.PropertyDefinition
                    For Each myKeyValPair In props
                        ' Property definition from KeyValuePair
                        propDef4 = myKeyValPair.Value()
                        ' Using the display name to identify
                        ' the PropertyDefinition
                        If propDef4.DisplayName =
                         "MATCODE" Then
                            'It is the PropertyDefinition
                            myMATCODE = propDef4
                        End If
                    Next

                    Dim myMATDESCR_PL As VDFVCP.PropertyDefinition _
                                           = Nothing
                    Dim propDef5 As VDFVCP.PropertyDefinition
                    For Each myKeyValPair In props
                        ' Property definition from KeyValuePair
                        propDef5 = myKeyValPair.Value()
                        ' Using the display name to identify
                        ' the PropertyDefinition
                        If propDef5.DisplayName =
                         "MATDESCR_PL" Then
                            'It is the PropertyDefinition
                            myMATDESCR_PL = propDef5
                        End If
                    Next


                    Dim fileIter As _
VDF.Vault.Currency.Entities.FileIteration = Nothing
                    fileIter = New VDF.Vault.Currency.
    Entities.FileIteration(m_conn,
                            selectedFile)
                    Dim strmyERPTYPE As String =
                                m_conn.PropertyManager.GetPropertyValue _
                                     (fileIter, myERPTYPE, Nothing)
                    Dim strmySK_ITEM As String = m_conn.PropertyManager.GetPropertyValue _
                                     (fileIter, mySK_ITEM, Nothing)
                    Dim strmyComment As String = m_conn.PropertyManager.GetPropertyValue _
                                     (fileIter, myComment, Nothing)
                    ReDim masterArr(rownumb, 0)
                    Dim strmyMATDESCR As String = Nothing
                    Try
                        strmyMATDESCR = m_conn.PropertyManager.GetPropertyValue _
                 (fileIter, myMATDESCR, Nothing)
                    Catch ex As Exception

                    End Try

                    If strmyMATDESCR = Nothing Then
                        strmyMATDESCR = ""
                    End If
                    masterArr(19, 0) = strmyMATDESCR

                    Dim strmyMATCODE As String = Nothing
                    Try
                        strmyMATCODE = m_conn.PropertyManager.GetPropertyValue _
                                     (fileIter, myMATCODE, Nothing)

                    Catch ex As Exception

                    End Try


                    If strmyMATCODE = Nothing Then
                        strmyMATCODE = ""
                    End If
                    masterArr(18, 0) = strmyMATCODE

                    Dim strmyMATDESCR_PL As String
                    Try
                        strmyMATDESCR_PL = m_conn.PropertyManager.GetPropertyValue _
                 (fileIter, myMATDESCR_PL, Nothing)
                    Catch ex As Exception

                    End Try


                    If strmyMATDESCR_PL = Nothing Then
                        strmyMATDESCR_PL = ""
                    End If
                    masterArr(20, 0) = strmyMATDESCR_PL

                    If Not myERPTYPE Is Nothing Then
                        '    If strmyERPTYPE Like "PAR_DRW" = True Or strmyERPTYPE Like "PAR_ITEM" Then  'old version 22.5
                        ''If strmyERPTYPE Like "PAR*" = True Then
                        ''Else
                        ''    MessageBox.Show("Selected part or assembly is not PAR*")
                        ''    Exit Sub
                        ''End If
                        If strmyERPTYPE Like "PAR_ITEM*" = True Then
                            MessageBox.Show("Selected part or assembly is PAR_ITEM")

                            Exit Sub
                        End If

                    Else
                        MessageBox.Show("There is no ERPTYPE Property in selected assembly")
                        Exit Sub
                    End If
                    If Not myComment Is Nothing Then
                        '''If strmyComment Like "Model paths updated*" = True Then
                        '''    MessageBox.Show("Selected assembly is a direct SK Copy. Check out both model and drawing in Inventor, update them and checkin, before making  SK Copy.")
                        '''    Exit Sub
                        '''End If

                    Else
                        MessageBox.Show("There is no Comment Property in selected assembly")
                        Exit Sub
                    End If


                    If ComboBox1.Text = "" Then
                        MessageBox.Show("SHIP SERIE is missing")
                        Exit Sub
                    End If
                    If TextBox2.Text = "" Then
                        MessageBox.Show("SK Item is missing")
                        Exit Sub
                    End If





                    Dim folder As ACW.Folder = Nothing
                    folder = m_conn.WebServiceManager.DocumentService.GetFolderById(selectedFile.FolderId)
                    ' Dim fileAssocParamList As New ArrayList()
                    '  Dim iFile As Autodesk.Connectivity.WebServices.File = GetFileAndAssociations(folder.FullName & "/" & selectedFile.Name, fileAssocParamList) 'not needed anymore?
                    ' if file is assembly get assocs
                    Dim mArrlength As Integer
                    Dim dArrlength As Integer
                    Dim fids(,) As Long = Nothing
                    Dim fassocArr As FileAssocArray
                    Dim mi As Integer = 0 'masterArr col
                    Dim di As Integer = 0



                    If selectedFile.Name.Split(".").Last = "iam" Then ' is assembly

                        Try
                            fassocArr = m_conn.WebServiceManager.DocumentService.GetLatestFileAssociationsByMasterIds(selectedFile.MasterId.ToSingleArray, FileAssociationTypeEnum.None, False, FileAssociationTypeEnum.Dependency, True, False, False, False).First

                            'fileID array


                            'create the row 0 to masterarr
                            ' drwArr col
                            '  ReDim Preserve fids(fi)
                            '  fids(fi) = selectedFile.Id
                            '  fi += 1
                            ReDim Preserve masterArr(rownumb, mi)
                            ' ReDim Preserve drwArr(rownumb, di)
                            'Main assy
                            masterArr(0, mi) = folder.Id  'folderid
                            masterArr(1, mi) = folder.FullName 'fullpath
                            masterArr(2, mi) = m_conn.WorkingFoldersManager.GetWorkingFolder(folder.FullName.ToString).ToString
                            masterArr(3, mi) = Split(selectedFile.Name, ".").Last 'ext
                            masterArr(4, mi) = selectedFile.Name 'name
                            masterArr(5, mi) = strmyERPTYPE 'Par*
                            masterArr(6, mi) = selectedFile.Id ' ID
                            ' Dim fassoc As FileAssocLite() = m_conn.WebServiceManager.DocumentService.GetFileAssociationLitesByIds(selectedFile.Id.ToSingleArray, FileAssocAlg.LatestConsumable, FileAssociationTypeEnum.None, False, FileAssociationTypeEnum.Dependency, True, True, True, False)
                            masterArr(7, mi) = "" 'old parentid I think this should move down after new ID is created
                            masterArr(14, mi) = selectedFile.MasterId 'old masterid
                            masterArr(17, mi) = strmySK_ITEM
                            'take first letter from name
                            'if not c then try to find it
                            If selectedFile.Name.Substring(0, 1).ToLower() <> "c" Then
                                'check if strmyMATDESCR has "304)
                                If strmyMATDESCR Like "*304*" Or masterArr(3, mi) = "iam" Or strmyERPTYPE.ToLower = "fix_none" Then

                                    'try to find existing selectedfile.name & " - 316 Then
                                    Dim oNewFileName As String = masterArr(4, mi).Replace("." & masterArr(3, mi), "") & " - AISI 316." & masterArr(3, mi)
                                    If has316file(masterArr(1, mi), oNewFileName) IsNot Nothing Then
                                        MessageBox.Show(masterArr(1, mi) & "/" & oNewFileName & " exists already!")
                                        Exit Sub
                                    Else
                                        'create new name, existing folder
                                        'give the info what we know so far. Note no id and masterId sofar
                                        masterArr(9, mi) = oNewFileName ' new Name
                                        masterArr(10, mi) = masterArr(0, mi) 'new folderID
                                        masterArr(11, mi) = masterArr(1, mi) 'new Fullpath
                                        masterArr(12, mi) = masterArr(2, mi) 'new localPath


                                    End If


                                End If
                            End If


                            'next components

                            mi += 1


                            'add components to masterArr
                            For Each fAssoc As FileAssoc In fassocArr.FileAssocs
                                Dim fid As Long = fAssoc.CldFile.Id
                                Dim f As ACW.File = m_conn.WebServiceManager.DocumentService.FindFilesByIds(fid.ToSingleArray()).First
                                ReDim Preserve masterArr(rownumb, mi)
                                masterArr(0, mi) = f.FolderId 'folderid
                                folder = m_conn.WebServiceManager.DocumentService.GetFolderById(f.FolderId) 'folder info of file
                                masterArr(1, mi) = folder.FullName 'fullpath
                                masterArr(2, mi) = m_conn.WorkingFoldersManager.GetWorkingFolder(folder.FullName.ToString).ToString
                                masterArr(3, mi) = Split(f.Name, ".").Last 'ext
                                masterArr(4, mi) = f.Name 'name
                                'then has component ERP_Type and PAR*
                                fileIter = New VDF.Vault.Currency.Entities.FileIteration(m_conn, f)
                                strmyERPTYPE =
                                    m_conn.PropertyManager.GetPropertyValue _
                                         (fileIter, myERPTYPE, Nothing)
                                strmySK_ITEM = m_conn.PropertyManager.GetPropertyValue _
                                         (fileIter, mySK_ITEM, Nothing)
                                strmyMATDESCR = Nothing
                                strmyMATDESCR = m_conn.PropertyManager.GetPropertyValue _
                                         (fileIter, myMATDESCR, Nothing)
                                If strmyMATDESCR = Nothing Then
                                    strmyMATDESCR = ""
                                End If
                                masterArr(19, mi) = strmyMATDESCR
                                strmyMATCODE = Nothing
                                strmyMATCODE = m_conn.PropertyManager.GetPropertyValue _
                                         (fileIter, myMATCODE, Nothing)
                                If strmyMATCODE = Nothing Then
                                    strmyMATCODE = ""
                                End If
                                masterArr(18, mi) = strmyMATCODE
                                strmyMATDESCR_PL = Nothing
                                strmyMATDESCR_PL = m_conn.PropertyManager.GetPropertyValue _
                                         (fileIter, myMATDESCR_PL, Nothing)
                                If strmyMATDESCR_PL = Nothing Then
                                    strmyMATDESCR_PL = ""
                                End If
                                masterArr(20, mi) = strmyMATDESCR_PL
                                If Not myERPTYPE Is Nothing Then
                                    masterArr(5, mi) = strmyERPTYPE
                                End If

                                masterArr(6, mi) = f.Id ' id
                                masterArr(7, mi) = fAssoc.ParFile.Id 'parent id
                                masterArr(14, mi) = f.MasterId 'old masterid

                                If Not mySK_ITEM Is Nothing Then
                                    masterArr(17, mi) = strmySK_ITEM
                                End If

                                If f.Name.Substring(0, 1).ToLower() <> "c" Then
                                    'check if strmyMATDESCR has "304)
                                    If strmyMATDESCR Like "*304*" Or masterArr(3, mi) = "iam" Or strmyERPTYPE.ToLower = "fix_none" Then

                                        'try to find existing selectedfile.name & " - 316 Then
                                        Dim oNewFileName As String = masterArr(4, mi).Replace("." & masterArr(3, mi), "") & " - AISI 316." & masterArr(3, mi)

                                        Dim oFile As ACW.File = Nothing
                                        oFile = has316file(masterArr(1, mi), oNewFileName)


                                        'check if MATCODE is from from materials430
                                        Dim found430 As Boolean = False
                                        For i As Integer = 0 To Materials430.Length - 1
                                            If strmyMATCODE = Materials430(i) Then
                                                found430 = True
                                                Exit For
                                            End If
                                        Next


                                        If oFile IsNot Nothing And found430 = False Then
                                            'get info from new component and add to matrix
                                            masterArr(8, mi) = oFile.Id  'new ID
                                            masterArr(9, mi) = oFile.Name ' new Name
                                            masterArr(10, mi) = oFile.FolderId ' new folderId
                                            folder = m_conn.WebServiceManager.DocumentService.GetFolderById(oFile.FolderId)
                                            masterArr(11, mi) = folder.FullName 'new Fullpath
                                            masterArr(12, mi) = m_conn.WorkingFoldersManager.GetWorkingFolder(folder.FullName.ToString).ToString
                                            masterArr(13, mi) = oFile.MasterId 'new masterId

                                        Else
                                            'give ne info what we know so far. Note no id and masterId sofar
                                            masterArr(9, mi) = oNewFileName ' new Name
                                            masterArr(10, mi) = masterArr(0, mi) 'new folderID
                                            masterArr(11, mi) = masterArr(1, mi) 'new Fullpath
                                            masterArr(12, mi) = masterArr(2, mi) 'new localPath



                                        End If

                                    Else
                                        'use this standard component again
                                        masterArr(8, mi) = masterArr(6, mi) 'Id
                                        masterArr(9, mi) = masterArr(4, mi) 'name
                                        masterArr(10, mi) = masterArr(0, mi) 'folderId
                                        masterArr(11, mi) = masterArr(1, mi) 'fullpath
                                        masterArr(12, mi) = masterArr(2, mi) 'localpath
                                        masterArr(13, mi) = masterArr(14, mi) ' masterid

                                    End If
                                End If


                                mi += 1
                            Next


                        Catch ex As Exception
                            MessageBox.Show(ex.Message & " Mek:MasterArr")
                        End Try
                        'now we have MasterArr finished as all components. Note! there can be multiple same components in different levels








                        'what if Assembly had derived instance or IPN?

                        'let's go through par* assemblies in masterar

                        mArrlength = masterArr.GetLength(1) - 1
                        If drwArr IsNot Nothing Then


                            dArrlength = drwArr.GetLength(1) - 1
                        End If
                        Dim deI As Integer = 0

                        Try
                            For mi = 0 To mArrlength
                                If masterArr(3, mi) = "iam" Then 'And masterArr(5, mi) Like "PAR*" Then
                                    'does assembly have parent ipt
                                    fassocArr = m_conn.WebServiceManager.DocumentService.GetLatestFileAssociationsByMasterIds(Int64.Parse(masterArr(14, mi)).ToSingleArray, FileAssociationTypeEnum.Dependency, False, FileAssociationTypeEnum.None, False, False, False, False).First
                                    If Not fassocArr.FileAssocs Is Nothing Then
                                        For Each assoc In fassocArr.FileAssocs
                                            Dim fExt As String = Split(assoc.ParFile.Name, ".").Last
                                            If fExt Like "ipt" Or fExt Like "ipn" Then
                                                ReDim Preserve derivedArr(rownumb, deI)
                                                Dim fid As Long = assoc.ParFile.Id
                                                Dim f As ACW.File = m_conn.WebServiceManager.DocumentService.FindFilesByIds(fid.ToSingleArray()).First
                                                derivedArr(0, deI) = f.FolderId 'folderid
                                                folder = m_conn.WebServiceManager.DocumentService.GetFolderById(f.FolderId) 'folder info of file
                                                derivedArr(1, deI) = folder.FullName 'fullpath
                                                derivedArr(2, deI) = m_conn.WorkingFoldersManager.GetWorkingFolder(folder.FullName.ToString).ToString
                                                derivedArr(3, deI) = Split(f.Name, ".").Last 'ext
                                                derivedArr(4, deI) = f.Name 'name
                                                'then has component ERP_Type and PAR*
                                                fileIter = New VDF.Vault.Currency.Entities.FileIteration(m_conn, f)
                                                strmyERPTYPE =
                                                         m_conn.PropertyManager.GetPropertyValue _
                                                              (fileIter, myERPTYPE, Nothing)
                                                strmySK_ITEM = m_conn.PropertyManager.GetPropertyValue _
                                                              (fileIter, mySK_ITEM, Nothing)
                                                strmyMATDESCR = Nothing
                                                strmyMATDESCR = m_conn.PropertyManager.GetPropertyValue _
                                                                (fileIter, myMATDESCR, Nothing)
                                                If strmyMATDESCR = Nothing Then
                                                    strmyMATDESCR = ""
                                                End If
                                                strmyMATCODE = Nothing
                                                strmyMATCODE = m_conn.PropertyManager.GetPropertyValue _
                                                                (fileIter, myMATCODE, Nothing)
                                                If strmyMATCODE = Nothing Then
                                                    strmyMATCODE = ""
                                                End If



                                                If Not myERPTYPE Is Nothing Then
                                                    derivedArr(5, deI) = strmyERPTYPE
                                                End If
                                                derivedArr(6, deI) = f.Id ' id
                                                derivedArr(7, deI) = Int64.Parse(masterArr(6, mi)) 'Child id  '
                                                derivedArr(14, deI) = f.MasterId 'old masterid

                                                If Not mySK_ITEM Is Nothing Then
                                                    derivedArr(17, deI) = strmySK_ITEM
                                                End If
                                                If f.Name.Substring(0, 1).ToLower() <> "c" Then
                                                    'try to find existing selectedfile.name & " - 316 Then
                                                    Dim oNewFileName As String = derivedArr(4, deI).Replace("." & derivedArr(3, deI), "") & " - AISI 316." & derivedArr(3, deI)

                                                    Dim oFile As ACW.File = Nothing
                                                    oFile = has316file(derivedArr(1, deI), oNewFileName)

                                                    'check if MATCODE is from from materials430
                                                    Dim found430 As Boolean = False
                                                    For i As Integer = 0 To Materials430.Length - 1
                                                        If strmyMATCODE = Materials430(i) Then
                                                            found430 = True
                                                            Exit For
                                                        End If
                                                    Next


                                                    If oFile IsNot Nothing And found430 = False Then
                                                        'get info from new component and add to matrix
                                                        derivedArr(8, deI) = oFile.Id  'new ID
                                                        derivedArr(9, deI) = oFile.Name ' new Name
                                                        derivedArr(10, deI) = oFile.FolderId ' new folderId
                                                        folder = m_conn.WebServiceManager.DocumentService.GetFolderById(oFile.FolderId)
                                                        derivedArr(11, deI) = folder.FullName 'new Fullpath
                                                        derivedArr(12, deI) = m_conn.WorkingFoldersManager.GetWorkingFolder(folder.FullName.ToString).ToString
                                                        derivedArr(13, deI) = oFile.MasterId 'new masterId

                                                    Else
                                                        'give ne info what we know so far. Note no id and masterId sofar
                                                        derivedArr(9, deI) = oNewFileName ' new Name
                                                        derivedArr(10, deI) = derivedArr(0, deI) 'new folderID
                                                        derivedArr(11, deI) = derivedArr(1, deI) 'new Fullpath
                                                        derivedArr(12, deI) = derivedArr(2, deI) 'new localPath



                                                    End If


                                                End If


                                                mi += 1
                                                deI += 1

                                            End If
                                        Next
                                    End If
                                End If
                            Next
                        Catch ex As Exception
                            MessageBox.Show(ex.Message & " Mek:Derived")
                        End Try
                    Else ' is part
                        Try


                            ReDim Preserve masterArr(rownumb, mi)
                            '  ReDim Preserve drwArr(rownumb, di)
                            'Main assy
                            masterArr(0, mi) = folder.Id  'folderid
                            masterArr(1, mi) = folder.FullName 'fullpath
                            masterArr(2, mi) = m_conn.WorkingFoldersManager.GetWorkingFolder(folder.FullName.ToString).ToString
                            masterArr(3, mi) = Split(selectedFile.Name, ".").Last 'ext
                            masterArr(4, mi) = selectedFile.Name 'name
                            masterArr(5, mi) = strmyERPTYPE 'Par*
                            masterArr(6, mi) = selectedFile.Id ' ID
                            ' Dim fassoc As FileAssocLite() = m_conn.WebServiceManager.DocumentService.GetFileAssociationLitesByIds(selectedFile.Id.ToSingleArray, FileAssocAlg.LatestConsumable, FileAssociationTypeEnum.None, False, FileAssociationTypeEnum.Dependency, True, True, True, False)
                            masterArr(7, mi) = "" 'old parentid I think this should move down after new ID is created
                            masterArr(14, mi) = selectedFile.MasterId 'old masterid
                            masterArr(17, mi) = strmySK_ITEM
                            'take first letter from name
                            'if not c then try to find it
                            If selectedFile.Name.Substring(0, 1).ToLower() <> "c" Then

                                'check if MATCODE is from from materials430
                                Dim found430 As Boolean = False
                                For i As Integer = 0 To Materials430.Length - 1
                                    If strmyMATCODE = Materials430(i) Then
                                        found430 = True
                                        Exit For
                                    End If
                                Next
                                'check if strmyMATDESCR has "304)
                                If strmyMATDESCR Like "*304*" And found430 = False Then

                                    'try to find existing selectedfile.name & " - 316 Then
                                    Dim oNewFileName As String = masterArr(4, mi).Replace("." & masterArr(3, mi), "") & " - AISI 316." & masterArr(3, mi)
                                    If has316file(masterArr(1, mi), oNewFileName) IsNot Nothing Then
                                        MessageBox.Show(masterArr(1, mi) & "/" & oNewFileName & " exists already!")
                                        Exit Sub
                                    Else

                                    End If
                                End If
                            End If
                        Catch ex As Exception
                            MessageBox.Show(ex.Message & " Mek:singlecomponentcopy")
                        End Try

                    End If ' of selected file was excel




                    Dim derivedArrlength As Integer
                    If Not derivedArr Is Nothing Then

                        derivedArrlength = derivedArr.GetLength(1) - 1
                        'now possible derived components are collected
                    End If
                    Try
                        'what if Assembly has derived instance? END

                        'let us generate the new folders, new names for components which will be copied.
                        'if SK_ITEM has value new folder will be projectfolderName otherwise componentFolderName
                        ' if is PAR* then new name


                        For tempI = 0 To mArrlength
                            If masterArr(5, tempI) Like "PAR*" And masterArr(9, tempI) Is Nothing Then
                                'generate new name
                                Dim fieldinputs() As String = Nothing
                                fieldinputs = "C".ToSingleArray()

                                Dim newName As String = m_conn.WebServiceManager.DocumentService.GenerateFileNumber(SKnumberingcheme, fieldinputs)
                                masterArr(9, tempI) = newName & "." & masterArr(3, tempI)

                                'choose project folder
                                masterArr(10, tempI) = projectfolderID
                                masterArr(11, tempI) = projectfolderName

                                'and next find the similar components from array and copy the same values
                                For temp2 As Integer = tempI + 1 To mArrlength
                                    If masterArr(4, tempI) = masterArr(4, temp2) And masterArr(9, temp2) Is Nothing Then
                                        masterArr(9, temp2) = masterArr(9, tempI)
                                        masterArr(10, temp2) = masterArr(10, tempI)
                                        masterArr(11, temp2) = masterArr(11, tempI)
                                    End If

                                Next


                            Else
                                'copy old info to new (standard files)

                                ''masterArr(8, tempI) = masterArr(6, tempI)
                                ''masterArr(9, tempI) = masterArr(4, tempI)
                                ''masterArr(10, tempI) = masterArr(0, tempI)
                                ''masterArr(11, tempI) = masterArr(1, tempI)
                                ''masterArr(12, tempI) = masterArr(2, tempI)
                                ''masterArr(13, tempI) = masterArr(14, tempI)
                            End If
                        Next
                        'then create fIDs to correct order fo copying: first ipt, tehn sub-iam and last main assy

                        For tempI = mArrlength To 1 Step -1
                            If (masterArr(3, tempI) = "ipt" And masterArr(5, tempI) Like "PAR*") Or (masterArr(3, tempI) = "ipt" And masterArr(13, tempI) Is Nothing) Then
                                'if does not have new masterId (13) then copy
                                If masterArr(13, tempI) Is Nothing Then
                                    ReDim Preserve fids(1, fi)
                                    fids(0, fi) = Int64.Parse(masterArr(6, tempI))
                                    fids(1, fi) = Int64.Parse(masterArr(14, tempI))

                                    fi += 1
                                End If
                            End If
                        Next
                        'sub-assys
                        For tempI = mArrlength To 1 Step -1
                            If (masterArr(3, tempI) = "iam" And masterArr(5, tempI) Like "PAR*") Or (masterArr(3, tempI) = "iam" And masterArr(13, tempI) Is Nothing) Then
                                If masterArr(13, tempI) Is Nothing Then
                                    ReDim Preserve fids(1, fi)
                                    fids(0, fi) = Int64.Parse(masterArr(6, tempI))
                                    fids(1, fi) = Int64.Parse(masterArr(14, tempI))
                                    fi += 1
                                End If
                            End If
                        Next
                        'last main assy
                        ReDim Preserve fids(1, fi)
                        fids(0, fi) = Int64.Parse(masterArr(6, 0))
                        fids(1, fi) = Int64.Parse(masterArr(14, 0))
                        fi += 1


                    Catch ex As Exception
                        MessageBox.Show(ex.Message & " Mek:fids")
                    End Try


                    'let us create drwarr for drawings going through fids(masterid)


                    Try

                        For tempI = 0 To fids.Length / 2 - 1
                            fassocArr = m_conn.WebServiceManager.DocumentService.GetLatestFileAssociationsByMasterIds(fids(1, tempI).ToSingleArray, FileAssociationTypeEnum.None, False, FileAssociationTypeEnum.None, False, True, False, False).First
                            ' fassocArr = m_conn.WebServiceManager.DocumentService.GetLatestFileAssociationsByMasterIds(selectedFile.MasterId.ToSingleArray, FileAssociationTypeEnum.None, False, FileAssociationTypeEnum.Dependency, True, False, False, False).First
                            '  fassocArr = m_conn.WebServiceManager.DocumentService.GetLatestFileAssociationsByMasterIds(Int64.Parse(masterArr(14, mi)).ToSingleArray, FileAssociationTypeEnum.Dependency, False, FileAssociationTypeEnum.None, False, False, False, False).First
                            If fassocArr.FileAssocs IsNot Nothing Then


                                For Each fassoc In fassocArr.FileAssocs

                                    Dim f As ACW.File = fassoc.ParFile

                                    'let's first check that parfile name and f name are the same.
                                    Dim mfName As String = Split(fassoc.CldFile.Name, ".").First
                                    Dim dfname As String = Split(f.Name, ".").First
                                    If Not mfName = dfname Then
                                        Continue For
                                    End If

                                    'if file is ipn skip
                                    If Split(f.Name, ".").Last = "ipn" Then
                                        Continue For
                                    End If

                                    'new addition, check is there already same drawing in drwarr
                                    Dim fileExists As Boolean = False
                                    If drwArr IsNot Nothing Then


                                        dArrlength = drwArr.GetLength(1) - 1

                                        For i As Integer = 0 To dArrlength
                                            'check if name exist
                                            If drwArr(4, i) = f.Name Then
                                                fileExists = True
                                                Exit For
                                            End If
                                        Next
                                    End If
                                    If fileExists = False Then 'new addition

                                        ReDim Preserve drwArr(rownumb, di)

                                        drwArr(0, di) = f.FolderId
                                        folder = m_conn.WebServiceManager.DocumentService.GetFolderById(f.FolderId) 'folder info of file
                                        drwArr(1, di) = folder.FullName 'fullpath
                                        drwArr(2, di) = m_conn.WorkingFoldersManager.GetWorkingFolder(folder.FullName.ToString).ToString
                                        drwArr(3, di) = Split(f.Name, ".").Last 'ext
                                        drwArr(4, di) = f.Name 'name
                                        drwArr(6, di) = f.Id ' id
                                        drwArr(7, di) = fassoc.Id 'cld id (model)


                                        drwArr(14, di) = f.MasterId 'old masterid
                                        If Not mySK_ITEM Is Nothing Then
                                            drwArr(17, di) = strmySK_ITEM
                                        End If

                                        'new name from model
                                        For mi = 0 To mArrlength
                                            If fassoc.CldFile.Name = masterArr(4, mi) Then
                                                drwArr(9, di) = Split(masterArr(9, mi), ".").First & ".dwg"
                                                drwArr(10, di) = masterArr(10, mi)
                                                drwArr(11, di) = masterArr(11, mi)
                                                Exit For

                                            End If
                                        Next
                                        di += 1
                                    End If 'new addition
                                Next
                            End If
                        Next
                    Catch ex As Exception
                        MessageBox.Show(ex.Message & " Mek:drwArr")
                    End Try
                    'start of models derived and drawing

                    If drwArr IsNot Nothing Then

                        dArrlength = drwArr.GetLength(1) - 1
                    Else
                        dArrlength = 0
                    End If

                    Try

                        numbofModels = fids.Length / 2
                        If derivedArr IsNot Nothing Then
                            numbofDeriveds = derivedArrlength + 1
                        Else
                            numbofDeriveds = 0
                        End If

                        If drwArr IsNot Nothing Then
                            numbofDrawings = dArrlength + 1
                        Else
                            numbofDrawings = 0
                        End If
                        Try


                            numbofTotal = numbofModels + numbofDeriveds + numbofDrawings

                            numbofSteps = Math.Floor(100 / numbofTotal) ' step/addition
                            progressStep = numbofSteps

                            Label6.Text = "Files to be copied: " & numbofTotal
                            Me.Refresh()

                        Catch ex As Exception
                            MessageBox.Show(ex.Message & " Mek:Calculating number of copies")
                        End Try


                    Catch ex As Exception
                        MessageBox.Show(ex.Message & " Mek:Copy start")
                    End Try


                    'copy models and drawings
                    'first models
                    Try
                        Dim i As Integer = 0
                        Dim rowExists As Boolean = False
                        ' fids 0 to mIDs
                        Dim mIDs As Long() = Nothing
                        Try
                            Dim ii As Integer = 0
                            For i = 0 To fids.Length / 2 - 1
                                If i > 0 Then
                                    For temp3 As Integer = 0 To i - 1
                                        If fids(0, i) = fids(0, temp3) Then
                                            rowExists = True
                                        End If

                                    Next
                                Else
                                End If

                                If rowExists = False Then
                                    ReDim Preserve mIDs(ii)
                                    mIDs(ii) = fids(0, i)
                                    ii += 1
                                End If
                                rowExists = False
                            Next
                        Catch ex As Exception
                            MessageBox.Show(ex.Message & " Mek:Creating mIDs")
                        End Try

                        'create downloadtickets
                        Dim downloadTickets() As ACW.ByteArray = m_conn.WebServiceManager.DocumentService.GetDownloadTicketsByFileIds(mIDs)
                        Dim results As ACW.PropWriteResults = Nothing

                        Dim downloadticket As ACW.ByteArray
                        Dim uploadTicket As Byte()
                        i = 0
                        For Each downloadticket In downloadTickets


                            'get bom for the file
                            Dim tbom As Autodesk.Connectivity.WebServices.BOM = m_conn.WebServiceManager.DocumentService.GetBOMByFileId(mIDs(i))


                            'get newfilename and newfolderid
                            Dim newName As String = Nothing
                            Dim newNameFirstLetter As String = Nothing
                            Dim newFolderID As Long = Nothing
                            Dim copiedfrom As String = Nothing
                            Dim oldMasterid As Long = Nothing
                            Dim erptype As String = Nothing
                            Dim seednumber As String = Nothing


                            Dim parfile As ACW.File = m_conn.WebServiceManager.DocumentService.FindFilesByIds(mIDs(i).ToSingleArray()).First



                            mArrlength = masterArr.GetLength(1) - 1

                            For tempI = 0 To mArrlength
                                If mIDs(i) = Int64.Parse(masterArr(6, tempI)) Then
                                    newName = masterArr(9, tempI)
                                    newNameFirstLetter = newName.Substring(0, 1).ToLower()
                                    newFolderID = masterArr(10, tempI)
                                    copiedfrom = masterArr(4, tempI)
                                    oldMasterid = Int64.Parse(masterArr(14, tempI))
                                    erptype = masterArr(5, tempI)



                                    Exit For 'note if there is second similar istance in MasterArr, it has to be solved later on.
                                End If
                            Next






                            'try to find related file associations
                            Dim newIDs As Long() = Nothing
                            'if id = parent id then then take New master ID to long array
                            Try
                                fi = 0
                                If mArrlength > 0 Then


                                    For tempI = 1 To mArrlength
                                        If mIDs(i) = Int64.Parse(masterArr(7, tempI)) Then



                                            ReDim Preserve newIDs(fi)
                                            newIDs(fi) = Int64.Parse(masterArr(8, tempI))
                                            fi += 1


                                        End If
                                    Next
                                End If
                            Catch ex As Exception
                                MessageBox.Show(ex.Message & " Mek:NewIDs")
                            End Try



                            'then assoc parameters




                            Dim fileassocparamslist As New ArrayList
                            Dim fileassocparams As ACW.FileAssocParam() = Nothing

                            Try


                                If Not newIDs Is Nothing Then

                                    Dim fs As ACW.File() = m_conn.WebServiceManager.DocumentService.FindFilesByIds(newIDs)
                                    fi = 0
                                    For Each f As ACW.File In fs
                                        Dim param As New ACW.FileAssocParam
                                        Dim param1 As New ACW.FileAssocParam()
                                        param.CldFileId = f.Id
                                        param1.CldFileId = f.Id
                                        'lets get original refid based on old assembly masterid

                                        fassocArr = m_conn.WebServiceManager.DocumentService.GetLatestFileAssociationsByMasterIds(oldMasterid.ToSingleArray, FileAssociationTypeEnum.None, False, FileAssociationTypeEnum.Dependency, False, False, False, False).First
                                        For Each assoc As ACW.FileAssoc In fassocArr.FileAssocs
                                            For assocI As Integer = 1 To mArrlength
                                                If f.MasterId = assoc.CldFile.MasterId Then ' Int64.Parse(masterArr(13, assocI)) Then
                                                    param.RefId = assoc.RefId
                                                    param1.RefId = assoc.RefId
                                                    param.Source = assoc.Source
                                                    param1.Source = assoc.Source
                                                    param.Typ = assoc.Typ
                                                    param1.Typ = assoc.Typ
                                                    Exit For

                                                End If
                                            Next
                                            'let's be sure that param.refID is not nothing
                                        Next


                                        Try


                                            If param.RefId Is Nothing Then
                                                For Each assoc As ACW.FileAssoc In fassocArr.FileAssocs
                                                    'compare f.masterID to masterarr(13,0)
                                                    For assocI As Integer = 1 To mArrlength
                                                        If Not masterArr(13, assocI) Is Nothing Then




                                                            If f.MasterId = Int64.Parse(masterArr(13, assocI)) And assoc.CldFile.MasterId = Int64.Parse(masterArr(14, assocI)) Then
                                                                param.RefId = assoc.RefId
                                                                param1.RefId = assoc.RefId
                                                                param.Source = assoc.Source
                                                                param1.Source = assoc.Source
                                                                param.Typ = assoc.Typ
                                                                param1.Typ = assoc.Typ
                                                                Exit For
                                                            End If
                                                        End If
                                                    Next

                                                Next

                                            End If
                                        Catch ex As Exception
                                            MessageBox.Show(ex.Message & " Mek:updatingParams")
                                        End Try



                                        'now we should have old refids mapped
                                        folder = m_conn.WebServiceManager.DocumentService.GetFolderById(f.FolderId)
                                        param.ExpectedVaultPath = folder.FullName & "/" & f.Name
                                        param1.ExpectedVaultPath = folder.FullName & "/" & f.Name
                                        ReDim Preserve fileassocparams(fi)
                                        fileassocparams(fi) = param
                                        fileassocparamslist.Add(param1)
                                        fi += 1
                                    Next

                                End If  'Fileassocparams with refIDs

                            Catch ex As Exception
                                MessageBox.Show(ex.Message & " Mek:Fileassocparams")
                            End Try

                            fi = 0

                            'fileassocparams array for some other use...
                            If fileassocparamslist IsNot Nothing Then
                                For Each param As ACW.FileAssocParam In fileassocparamslist
                                    ReDim Preserve fileassocparams(fi)
                                    fileassocparams(fi) = param
                                    fi += 1
                                Next
                            End If

                            '****************properties******************




                            Dim fileProps As CtntSrcPropDef() = m_conn.WebServiceManager.FilestoreService.GetContentSourcePropertyDefinitions(downloadticket.Bytes, True)
                            Dim propSet = fileProps.Where(Function(n) n.Typ = DataType.[String])
                            Dim pNumber As String = newName
                            pNumber = Strings.Left(pNumber, pNumber.Length - 4)
                            Dim oPropWriterequests As New ACW.PropWriteRequests
                            Dim propWrites() As PropWriteReq = Nothing
                            Dim propsetI As Integer = 0

                            Try
                                'lets figure out if ERPTYPE is PAR_MATER

                                Dim isMater As Boolean = False
                                Dim hasSeed As Boolean = False


                                'find correct row from masterarr
                                'read matcode property from mIDs(i)
                                Dim MasterArrRow As Integer = 0
                                For MasterArrRow = 0 To mArrlength
                                    If mIDs(i) = masterArr(6, MasterArrRow) Then
                                        Exit For
                                    End If
                                Next



                                For Each prop As CtntSrcPropDef In propSet
                                    Dim erpcodeval As String = Nothing
                                    'If erptype = "PAR_DRW" Then
                                    '    erpcodeval = TextBox2.Text.ToUpper & "-" & pNumber.ToUpper
                                    'Else
                                    erpcodeval = pNumber.ToUpper
                                    'End If
                                    Dim Material316Str As String = Nothing
                                    If prop.DispName = "Part Number" Then


                                        Dim propWrite As New PropWriteReq() With {
                            .CanCreate = False,
                            .Moniker = prop.Moniker,
                            .Val = erpcodeval
                        }
                                        ReDim Preserve propWrites(propsetI)
                                        propWrites(propsetI) = propWrite
                                        propsetI += 1
                                    ElseIf (prop.DispName = "ERPCODE" And erptype <> "PAR_MATER") And (prop.DispName = "ERPCODE" And erptype <> "FIX_MATER") Then
                                        Dim propWrite As New PropWriteReq() With {
                                        .CanCreate = False,
                                        .Moniker = prop.Moniker,
                                        .Val = erpcodeval
                                    }
                                        ReDim Preserve propWrites(propsetI)
                                        propWrites(propsetI) = propWrite
                                        propsetI += 1
                                    ElseIf prop.DispName = "ERPCODE" And erptype = "FIX_MATER" Then

                                        If masterArr(18, MasterArrRow) IsNot Nothing Then
                                            'then find it from material304316arr
                                            For intX As Integer = 0 To Materials304_316.GetUpperBound(1)
                                                If masterArr(18, MasterArrRow) = Materials304_316(0, intX) Then
                                                    Material316Str = Materials304_316(1, intX)
                                                    Exit For
                                                End If
                                            Next
                                        End If
                                        If Material316Str IsNot Nothing Then
                                            Dim propWrite As New PropWriteReq() With {
                                        .CanCreate = False,
                                        .Moniker = prop.Moniker,
                                        .Val = Material316Str ' get new val from material list
                                    }
                                            ReDim Preserve propWrites(propsetI)
                                            propWrites(propsetI) = propWrite
                                            propsetI += 1
                                        End If

                                    ElseIf prop.DispName = "SHIP_SERIE" And newNameFirstLetter = "c" Then
                                        Dim propWrite As New PropWriteReq() With {
                                            .CanCreate = False,
                                            .Moniker = prop.Moniker,
                                            .Val = ComboBox1.Text.ToUpper
                                        }
                                        ReDim Preserve propWrites(propsetI)
                                        propWrites(propsetI) = propWrite
                                        propsetI += 1
                                    ElseIf prop.DispName = "SK_ITEM" And newNameFirstLetter = "c" Then
                                        Dim propWrite As New PropWriteReq() With {
                                            .CanCreate = False,
                                            .Moniker = prop.Moniker,
                                            .Val = TextBox2.Text.ToUpper
                                        }
                                        ReDim Preserve propWrites(propsetI)
                                        propWrites(propsetI) = propWrite
                                        propsetI += 1
                                    ElseIf prop.DispName = "AREA" And newNameFirstLetter = "c" Then
                                        Dim propWrite As New PropWriteReq() With {
                                            .CanCreate = False,
                                            .Moniker = prop.Moniker,
                                            .Val = TextBox3.Text.ToUpper
                                        }
                                        ReDim Preserve propWrites(propsetI)
                                        propWrites(propsetI) = propWrite
                                        propsetI += 1
                                    ElseIf prop.DispName = "AREA_NAME" And newNameFirstLetter = "c" Then
                                        Dim propWrite As New PropWriteReq() With {
                                            .CanCreate = False,
                                            .Moniker = prop.Moniker,
                                            .Val = ComboBox3.Text.ToUpper
                                        }
                                        ReDim Preserve propWrites(propsetI)
                                        propWrites(propsetI) = propWrite
                                        propsetI += 1
                                    ElseIf prop.DispName = "DECK" And newNameFirstLetter = "c" Then
                                        Dim propWrite As New PropWriteReq() With {
                                            .CanCreate = False,
                                            .Moniker = prop.Moniker,
                                            .Val = TextBox5.Text.ToUpper
                                        }
                                        ReDim Preserve propWrites(propsetI)
                                        propWrites(propsetI) = propWrite
                                        propsetI += 1
                                    ElseIf prop.DispName = "FIREZONE" And newNameFirstLetter = "c" Then
                                        Dim propWrite As New PropWriteReq() With {
                                            .CanCreate = False,
                                            .Moniker = prop.Moniker,
                                            .Val = TextBox6.Text.ToUpper
                                        }
                                        ReDim Preserve propWrites(propsetI)
                                        propWrites(propsetI) = propWrite
                                        propsetI += 1
                                    ElseIf prop.DispName = "YARD" And newNameFirstLetter = "c" Then
                                        Dim propWrite As New PropWriteReq() With {
                                            .CanCreate = False,
                                            .Moniker = prop.Moniker,
                                            .Val = ComboBox2.Text.ToUpper
                                        }
                                        ReDim Preserve propWrites(propsetI)
                                        propWrites(propsetI) = propWrite
                                        propsetI += 1
                                    ElseIf prop.DispName = "SEED" And newNameFirstLetter = "c" Then
                                        Dim propWrite As New PropWriteReq() With {
                                            .CanCreate = False,
                                            .Moniker = prop.Moniker,
                                            .Val = copiedfrom.Split(".").First
                                        }
                                        ReDim Preserve propWrites(propsetI)
                                        propWrites(propsetI) = propWrite
                                        propsetI += 1
                                        hasSeed = True

                                        'next material 304 -> 316 properties
                                        'ALTERNATIVE_MATERIAL
                                    ElseIf prop.DispName = "ALTERNATIVE_MATERIAL" Then
                                        Dim propWrite As New PropWriteReq() With {
                                            .CanCreate = False,
                                            .Moniker = prop.Moniker,
                                            .Val = True
                                        }
                                        ReDim Preserve propWrites(propsetI)
                                        propWrites(propsetI) = propWrite
                                        propsetI += 1
                                        'MATCODE replacement from table
                                    ElseIf prop.DispName = "MATCODE" Then

                                        If masterArr(18, MasterArrRow) IsNot Nothing Then
                                            'then find it from material304316arr
                                            For intX As Integer = 0 To Materials304_316.GetUpperBound(1)
                                                If masterArr(18, MasterArrRow) = Materials304_316(0, intX) Then
                                                    Material316Str = Materials304_316(1, intX)
                                                    Exit For
                                                End If
                                            Next
                                        End If
                                        If Material316Str IsNot Nothing Then
                                            Dim propWrite As New PropWriteReq() With {
                                            .CanCreate = False,
                                            .Moniker = prop.Moniker,
                                            .Val = Material316Str
                                        }
                                            ReDim Preserve propWrites(propsetI)
                                            propWrites(propsetI) = propWrite
                                            propsetI += 1
                                        End If





                                        'MATDESCR replace 304 -> 316

                                    ElseIf prop.DispName = "MATDESCR" Then

                                        If masterArr(19, MasterArrRow) IsNot Nothing Then
                                            Dim propWrite As New PropWriteReq() With {
                                            .CanCreate = False,
                                            .Moniker = prop.Moniker,
                                            .Val = masterArr(19, MasterArrRow).Replace("304", "316")
                                        }
                                            ReDim Preserve propWrites(propsetI)
                                            propWrites(propsetI) = propWrite
                                            propsetI += 1
                                        End If
                                        'MATDESCR_PL replace 304 ->316
                                    ElseIf prop.DispName = "MATDESCR_PL" Then

                                        If masterArr(20, MasterArrRow) IsNot Nothing Then
                                            Dim propWrite As New PropWriteReq() With {
                                            .CanCreate = False,
                                            .Moniker = prop.Moniker,
                                            .Val = masterArr(20, MasterArrRow).Replace("304", "316")
                                        }
                                            ReDim Preserve propWrites(propsetI)
                                            propWrites(propsetI) = propWrite
                                            propsetI += 1
                                        End If

                                    End If
                                Next

                                '****************properties end**************
                            Catch ex As Exception
                                MessageBox.Show(ex.Message & " Mek:mPropUpdate")
                            End Try
                            If propWrites IsNot Nothing Then
                                oPropWriterequests.Requests = propWrites
                            End If
                            Try


                                'now the file should be ready for copying
                                Dim Ext As String = newName.Split(".").Last
                                uploadTicket = m_conn.WebServiceManager.FilestoreService.CopyFile(downloadticket.Bytes, Ext, True, oPropWriterequests, results)
                                newfile = m_conn.WebServiceManager.DocumentService.AddUploadedFile(newFolderID, newName, "New copy from " & copiedfrom, DateTime.Now, fileassocparams, tbom,
                                         parfile.FileClass, parfile.Hidden, uploadTicket.ToByteArray())
                                Labeltext.Text = proggStep & " file(s) copied and updated"
                                proggStep += 1
                                Me.Refresh()


                                'then let's add new file info to masterarr
                                'compare existing mids(i) to masterarr(6,tempi)
                                For tempI = 0 To mArrlength

                                    If mIDs(i) = Int64.Parse(masterArr(6, tempI)) Then


                                        masterArr(8, tempI) = newfile.Id 'new id


                                        folder = m_conn.WebServiceManager.DocumentService.GetFolderById(newfile.FolderId)
                                        masterArr(11, tempI) = folder.FullName
                                        masterArr(12, tempI) = m_conn.WorkingFoldersManager.GetWorkingFolder(folder.FullName.ToString).ToString
                                        masterArr(13, tempI) = newfile.MasterId
                                        masterArr(21, tempI) = True
                                        Exit For


                                    End If
                                Next

                            Catch ex As Exception
                                MessageBox.Show(ex.Message & " Mek:mCopyAndmasterArrUpdate")
                            End Try


                            'let's download file and update local file references
                            Try

                                Dim fileIteration As VDF.Vault.Currency.Entities.FileIteration = New VDF.Vault.Currency.Entities.FileIteration(m_conn, newfile)
                                '  downloadticket = m_conn.WebServiceManager.DocumentService.GetDownloadTicketsByMasterIds(newfile.MasterId.ToSingleArray()).First()
                                Dim settings As VDF.Vault.Settings.AcquireFilesSettings = New VDF.Vault.Settings.AcquireFilesSettings(m_conn)
                                settings.CheckoutComment = "File is CheckedOut By UpdatingReferences"
                                settings.LocalPath = Nothing
                                settings.OptionsRelationshipGathering.FileRelationshipSettings.IncludeChildren = True
                                settings.OptionsRelationshipGathering.FileRelationshipSettings.RecurseChildren = True
                                settings.OptionsRelationshipGathering.FileRelationshipSettings.IncludeLibraryContents = True
                                settings.OptionsRelationshipGathering.FileRelationshipSettings.VersionGatheringOption = Autodesk.DataManagement.Client.Framework.Vault.Currency.VersionGatheringOption.Latest
                                settings.DefaultAcquisitionOption = VDF.Vault.Settings.AcquireFilesSettings.AcquisitionOption.Download
                                settings.AddFileToAcquire(fileIteration, VDF.Vault.Settings.AcquireFilesSettings.AcquisitionOption.Download)
                                Dim fResults As VDF.Vault.Results.AcquireFilesResults = m_conn.FileManager.AcquireFiles(settings)


                                For Each acquireFilesResult As VDF.Vault.Results.FileAcquisitionResult In fResults.FileResults
                                    Dim assocs As ACW.FileAssocArray() = m_conn.WebServiceManager.DocumentService.GetFileAssociationsByIds(New Long() {acquireFilesResult.File.EntityIterationId}, FileAssociationTypeEnum.None, False, FileAssociationTypeEnum.Dependency, False, False, False)
                                    If assocs.First().FileAssocs Is Nothing Then
                                        Continue For
                                    End If

                                    'lets check that ERPTYPE = PAR* if not then Continue For
                                    fileIter = New VDF.Vault.Currency.Entities.FileIteration(m_conn, acquireFilesResult.File)
                                    strmyERPTYPE = m_conn.PropertyManager.GetPropertyValue(fileIter, myERPTYPE, Nothing)
                                    If Not myERPTYPE Is Nothing Then
                                        'if masterarr(18,0) is not nothing jump next
                                        Dim tempIII As Integer = Nothing
                                        For tempIII = 0 To mArrlength
                                            If fileIter.EntityMasterId = masterArr(13, tempIII) Then
                                                Exit For
                                            End If
                                        Next

                                        ''If strmyERPTYPE Like "PAR*" = True Then 'And masterArr(3, tempI) = "iam"  And Not masterArr(18, tempI) Is Nothing And Split(newfile.Name, ".").Last = "iam" Then 'fassoc is  assy in derived  component
                                        ''    '  ElseIf strmyERPTYPE Like "PAR*" = True And masterArr(3, tempI) = "ipt" And Not masterArr(18, tempI) Is Nothing And Split(newfile.Name, ".").Last = "ipt" Then
                                        ''Else
                                        ''    Continue For ' not PAR*
                                        ''End If
                                        If tempIII <> Nothing Then

                                            If masterArr(9, tempIII).Substring(0, 1).ToLower = "c" Or masterArr(9, tempIII) Like "*316*" Then
                                            Else
                                                Continue For 'is not C or 316
                                            End If
                                        End If

                                    Else
                                        Continue For ' No ERPTYPE at all
                                    End If
                                    Dim fileAssocs = assocs.First().FileAssocs.Where(Function(fa) fa.ParFile.MasterId = acquireFilesResult.File.EntityMasterId)
                                    Dim refs = New List(Of VDF.Vault.Currency.FileSystem.FileReference)()
                                    For Each fileAssoc As FileAssoc In fileAssocs
                                        Dim fileCld = fResults.FileResults.FirstOrDefault(Function(f) f.File.EntityMasterId = fileAssoc.CldFile.MasterId)
                                        If fileCld Is Nothing Then
                                            Continue For
                                        End If
                                        Dim reference = New VDF.Vault.Currency.FileSystem.FileReference(fileAssoc.RefId, fileCld.LocalPath, fileAssoc.Source)
                                        refs.Add(reference)
                                    Next
                                    Dim settings1 As VDF.Vault.Settings.AcquireFilesSettings = New VDF.Vault.Settings.AcquireFilesSettings(m_conn)
                                    settings1.AddFileToAcquire(fileIteration, VDF.Vault.Settings.AcquireFilesSettings.AcquisitionOption.Checkout)
                                    Dim f1Results As VDF.Vault.Results.AcquireFilesResults = m_conn.FileManager.AcquireFiles(settings1)

                                    Dim updateReferenceModel = New VDF.Vault.Models.UpdateFileReferencesModel()
                                    updateReferenceModel.SetTargetFilePath(acquireFilesResult.File, acquireFilesResult.LocalPath)
                                    updateReferenceModel.ForceUpdateOfTargetFilePaths = True

                                    updateReferenceModel.UpdateRefsInLocalFile(acquireFilesResult.File, acquireFilesResult.LocalPath, refs)

                                    updateReferenceModel.UpdateVaultStatus = True


                                    Try
                                        '    uploadTicket = m_conn.WebServiceManager.FilestoreService.CopyFile(downloadticket.Bytes, True, Nothing, results)
                                        Dim fldrPath As VDF.Currency.FolderPathAbsolute = New VDF.Currency.FolderPathAbsolute(masterArr(12, tempI))
                                        Dim filePath As String = System.IO.Path.Combine(fldrPath.ToString(), fileIteration.EntityName)
                                        Dim filePathAbs As VDF.Currency.FilePathAbsolute = New VDF.Currency.FilePathAbsolute(filePath)
                                        m_conn.FileManager.CheckinFile(fileIteration, "Model paths updated in Assembly", False, fileassocparams,
                                                                                                          Nothing, False, Nothing,
                                                                                ACW.FileClassification.None, False, filePathAbs)


                                    Catch ex As Exception
                                        MessageBox.Show(ex.Message & " Mek:mUpdate")
                                    End Try

                                Next
                            Catch ex As Exception
                                MessageBox.Show(ex.Message & " Mek:mDownload")
                            End Try

                            'update also other possible instances with same Masterid
                            Try
                                For tempIi As Integer = 1 To mArrlength
                                    If mIDs(i) = Int64.Parse(masterArr(6, tempIi)) Then

                                        masterArr(8, tempIi) = newfile.Id 'new id

                                        folder = m_conn.WebServiceManager.DocumentService.GetFolderById(newfile.FolderId)
                                        masterArr(11, tempIi) = folder.FullName
                                        masterArr(12, tempIi) = m_conn.WorkingFoldersManager.GetWorkingFolder(folder.FullName.ToString).ToString
                                        masterArr(13, tempIi) = newfile.MasterId
                                        masterArr(21, tempI) = True
                                    End If
                                Next
                            Catch ex As Exception
                                MessageBox.Show(ex.Message & " Mek:masterArrUpdateAftermUpdate")
                            End Try







                            i += 1 'next mids
                        Next

                    Catch ex As Exception
                        MessageBox.Show(ex.Message & " Mek:CopyingMainTry")

                    End Try


                    '   Labeltext.Text = "Models copied and updated"
                    '   Me.Refresh()
                    '**************************** update derived components in derivedarr***********************************************



                    Try
                        If Not derivedArr Is Nothing Then


                            If derivedArr.Length > 0 Then


                                ' fids 0 to deIDs
                                Dim deIDs As Long() = Nothing
                                Dim i As Integer = 0

                                For i = 0 To derivedArrlength
                                    ReDim Preserve deIDs(i)
                                    deIDs(i) = Int64.Parse(derivedArr(6, i))

                                Next


                                'create downloadtickets
                                Dim downloadTickets() As ACW.ByteArray = m_conn.WebServiceManager.DocumentService.GetDownloadTicketsByFileIds(deIDs)
                                Dim results As ACW.PropWriteResults = Nothing

                                Dim downloadticket As ACW.ByteArray
                                Dim uploadTicket As Byte()
                                i = 0
                                For Each downloadticket In downloadTickets


                                    'get bom for the file
                                    Dim tbom As Autodesk.Connectivity.WebServices.BOM = m_conn.WebServiceManager.DocumentService.GetBOMByFileId(deIDs(i))


                                    'get newfilename and newfolderid
                                    Dim newname As String = Nothing
                                    Dim newNameFirstLetter As String = Nothing
                                    Dim newFolderID As Long = Nothing
                                    Dim copiedfrom As String = Nothing
                                    Dim oldMasterid As Long = Nothing
                                    'yo pitää generoida.
                                    Dim fieldinputs() As String = Nothing
                                    fieldinputs = "C".ToSingleArray()





                                    Dim parfile As ACW.File = m_conn.WebServiceManager.DocumentService.FindFilesByIds(deIDs(i).ToSingleArray()).First



                                    '  mArrlength = masterArr.GetLength(1) - 1
                                    If derivedArr(9, tempI) = Nothing Then


                                        For tempI = 0 To derivedArrlength
                                            If deIDs(i) = Int64.Parse(derivedArr(6, tempI)) Then
                                                newname = m_conn.WebServiceManager.DocumentService.GenerateFileNumber(SKnumberingcheme, fieldinputs)

                                                derivedArr(9, tempI) = newname & "." & derivedArr(3, tempI)


                                                '************Change for derived ipt name or ipn name to same as model
                                                ' derivedArr(9, di) = Split(masterArr(9, mi), ".").First & ".dwg"
                                                '************Change for derived ipt name or ipn name to same as model***End
                                                newname = derivedArr(9, tempI)
                                                newNameFirstLetter = newname.Substring(0, 1).ToLower()

                                                derivedArr(10, tempI) = projectfolderID
                                                ' newName = derivedArr(9, tempI)
                                                ' newFolderID = derivedArr(10, tempI)
                                                copiedfrom = derivedArr(4, tempI)
                                                oldMasterid = Int64.Parse(derivedArr(14, tempI))
                                                newFolderID = derivedArr(10, tempI)

                                                Exit For 'note if there is second similar istance in MasterArr, it has to be solved later on.
                                            End If
                                        Next
                                    Else
                                        newname = derivedArr(9, tempI)
                                    End If


                                    copiedfrom = derivedArr(4, tempI)
                                    oldMasterid = Int64.Parse(derivedArr(14, tempI))
                                    newFolderID = derivedArr(10, tempI)
                                    'try to find related file associations
                                    Dim newIDs As Long() = Nothing
                                    'if id = child id then then take New master ID to long array
                                    Try
                                        fi = 0
                                        For tempI = 0 To mArrlength
                                            If derivedArr(7, i) = Int64.Parse(masterArr(6, tempI)) Then
                                                ReDim Preserve newIDs(fi)
                                                newIDs(fi) = Int64.Parse(masterArr(13, tempI))
                                                fi += 1
                                                Exit For

                                            End If
                                        Next
                                    Catch ex As Exception
                                        MessageBox.Show(ex.Message)
                                    End Try

                                    'then assoc parameters

                                    Dim fileassocparamslist As New ArrayList
                                    Dim fileassocparams As ACW.FileAssocParam() = Nothing

                                    If Not newIDs Is Nothing Then

                                        Dim fs As ACW.File() = m_conn.WebServiceManager.DocumentService.FindLatestFilesByMasterIds(newIDs)
                                        fi = 0
                                        For Each f As ACW.File In fs
                                            Dim param As New ACW.FileAssocParam
                                            Dim param1 As New ACW.FileAssocParam()
                                            param.CldFileId = f.Id
                                            param1.CldFileId = f.Id
                                            'lets get original refid based on old assembly masterid

                                            fassocArr = m_conn.WebServiceManager.DocumentService.GetLatestFileAssociationsByMasterIds(oldMasterid.ToSingleArray, FileAssociationTypeEnum.None, False, FileAssociationTypeEnum.Dependency, False, False, False, False).First
                                            For Each assoc As ACW.FileAssoc In fassocArr.FileAssocs
                                                For assocI As Integer = 0 To mArrlength
                                                    If f.MasterId = Int64.Parse(masterArr(13, assocI)) Then 'assoc.CldFile.MasterId Then ' 
                                                        param.RefId = assoc.RefId
                                                        param1.RefId = assoc.RefId
                                                        param.Source = assoc.Source
                                                        param1.Source = assoc.Source
                                                        param.Typ = assoc.Typ
                                                        param1.Typ = assoc.Typ
                                                        Exit For

                                                    End If
                                                Next
                                                'let's sure that param.refID is not nothing
                                            Next
                                            Try


                                                If param.RefId Is Nothing Then
                                                    For Each assoc As ACW.FileAssoc In fassocArr.FileAssocs
                                                        'compare f.masterID to masterarr(13,0)
                                                        For assocI As Integer = 1 To mArrlength
                                                            If Not masterArr(13, assocI) Is Nothing Then
                                                                If f.MasterId = Int64.Parse(masterArr(13, assocI)) And assoc.CldFile.MasterId = Int64.Parse(masterArr(14, assocI)) Then
                                                                    param.RefId = assoc.RefId
                                                                    param1.RefId = assoc.RefId
                                                                    param.Source = assoc.Source
                                                                    param1.Source = assoc.Source
                                                                    param.Typ = assoc.Typ
                                                                    param1.Typ = assoc.Typ
                                                                    Exit For
                                                                End If
                                                            End If
                                                        Next

                                                    Next

                                                End If
                                            Catch ex As Exception
                                                MessageBox.Show(ex.Message)
                                            End Try



                                            'now we should have old refids mapped
                                            folder = m_conn.WebServiceManager.DocumentService.GetFolderById(f.FolderId)
                                            param.ExpectedVaultPath = folder.FullName & "/" & f.Name
                                            param1.ExpectedVaultPath = folder.FullName & "/" & f.Name
                                            ReDim Preserve fileassocparams(fi)
                                            fileassocparams(fi) = param
                                            fileassocparamslist.Add(param1)
                                            fi += 1
                                        Next

                                    End If  'Fileassocparams with refIDs



                                    fi = 0

                                    'fileassocparams array for some other use...
                                    If fileassocparamslist IsNot Nothing Then
                                        For Each param As ACW.FileAssocParam In fileassocparamslist
                                            ReDim Preserve fileassocparams(fi)
                                            fileassocparams(fi) = param
                                            fi += 1
                                        Next
                                    End If
                                    '**********************Properties*****************
                                    Dim fileProps As CtntSrcPropDef() = m_conn.WebServiceManager.FilestoreService.GetContentSourcePropertyDefinitions(downloadticket.Bytes, True)
                                    Dim propSet = fileProps.Where(Function(n) n.Typ = DataType.[String])
                                    Dim pNumber As String = derivedArr(9, tempI)
                                    pNumber = Strings.Left(pNumber, pNumber.Length - 4)
                                    Dim oPropWriterequests As New ACW.PropWriteRequests
                                    Dim propWrites() As PropWriteReq = Nothing
                                    Dim propsetI As Integer = 0
                                    For Each prop As CtntSrcPropDef In propSet

                                        If prop.DispName = "Part Number" Then


                                            Dim propWrite As New PropWriteReq() With {
                            .CanCreate = False,
                            .Moniker = prop.Moniker,
                            .Val = pNumber.ToUpper
                        }
                                            ReDim Preserve propWrites(propsetI)
                                            propWrites(propsetI) = propWrite
                                            propsetI += 1
                                        ElseIf prop.DispName = "ERPCODE" Then
                                            Dim propWrite As New PropWriteReq() With {
                                            .CanCreate = False,
                                            .Moniker = prop.Moniker,
                                            .Val = pNumber.ToUpper
                                        }
                                            ReDim Preserve propWrites(propsetI)
                                            propWrites(propsetI) = propWrite
                                            propsetI += 1

                                        ElseIf prop.DispName = "SHIP_SERIE" And newNameFirstLetter = "c" Then
                                            Dim propWrite As New PropWriteReq() With {
                                            .CanCreate = False,
                                            .Moniker = prop.Moniker,
                                            .Val = ComboBox1.Text.ToUpper
                                        }
                                            ReDim Preserve propWrites(propsetI)
                                            propWrites(propsetI) = propWrite
                                            propsetI += 1
                                        ElseIf prop.DispName = "SK_ITEM" And newNameFirstLetter = "c" Then
                                            Dim propWrite As New PropWriteReq() With {
                                            .CanCreate = False,
                                            .Moniker = prop.Moniker,
                                            .Val = TextBox2.Text.ToUpper
                                        }
                                            ReDim Preserve propWrites(propsetI)
                                            propWrites(propsetI) = propWrite
                                            propsetI += 1
                                        ElseIf prop.DispName = "AREA" And newNameFirstLetter = "c" Then
                                            Dim propWrite As New PropWriteReq() With {
                                            .CanCreate = False,
                                            .Moniker = prop.Moniker,
                                            .Val = TextBox3.Text.ToUpper
                                        }
                                            ReDim Preserve propWrites(propsetI)
                                            propWrites(propsetI) = propWrite
                                            propsetI += 1
                                        ElseIf prop.DispName = "AREA_NAME" And newNameFirstLetter = "c" Then
                                            Dim propWrite As New PropWriteReq() With {
                                            .CanCreate = False,
                                            .Moniker = prop.Moniker,
                                            .Val = ComboBox3.Text.ToUpper
                                        }
                                            ReDim Preserve propWrites(propsetI)
                                            propWrites(propsetI) = propWrite
                                            propsetI += 1
                                        ElseIf prop.DispName = "DECK" And newNameFirstLetter = "c" Then
                                            Dim propWrite As New PropWriteReq() With {
                                            .CanCreate = False,
                                            .Moniker = prop.Moniker,
                                            .Val = TextBox5.Text.ToUpper
                                        }
                                            ReDim Preserve propWrites(propsetI)
                                            propWrites(propsetI) = propWrite
                                            propsetI += 1
                                        ElseIf prop.DispName = "FIREZONE" And newNameFirstLetter = "c" Then
                                            Dim propWrite As New PropWriteReq() With {
                                            .CanCreate = False,
                                            .Moniker = prop.Moniker,
                                            .Val = TextBox6.Text.ToUpper
                                        }
                                            ReDim Preserve propWrites(propsetI)
                                            propWrites(propsetI) = propWrite
                                            propsetI += 1
                                        ElseIf prop.DispName = "YARD" And newNameFirstLetter = "c" Then
                                            Dim propWrite As New PropWriteReq() With {
                                            .CanCreate = False,
                                            .Moniker = prop.Moniker,
                                            .Val = ComboBox2.Text.ToUpper
                                        }
                                            ReDim Preserve propWrites(propsetI)
                                            propWrites(propsetI) = propWrite
                                            propsetI += 1
                                        ElseIf prop.DispName = "SEED" And newNameFirstLetter = "c" Then
                                            Dim propWrite As New PropWriteReq() With {
                                                .CanCreate = False,
                                                .Moniker = prop.Moniker,
                                                .Val = copiedfrom.Split(".").First
                                            }
                                            ReDim Preserve propWrites(propsetI)
                                            propWrites(propsetI) = propWrite
                                            propsetI += 1
                                        End If
                                    Next
                                    '**********************Properties end*************
                                    If propWrites IsNot Nothing Then
                                        oPropWriterequests.Requests = propWrites
                                    End If
                                    'now the file should be ready for copying
                                    Dim Ext As String = newname.Split(".").Last
                                    uploadTicket = m_conn.WebServiceManager.FilestoreService.CopyFile(downloadticket.Bytes, Ext, True, oPropWriterequests, results)
                                    newfile = m_conn.WebServiceManager.DocumentService.AddUploadedFile(newFolderID, newname, "New copy from " & copiedfrom, DateTime.Now, fileassocparams, tbom,
                                             parfile.FileClass, parfile.Hidden, uploadTicket.ToByteArray())
                                    Labeltext.Text = proggStep & " file(s) copied and updated"
                                    proggStep += 1
                                    Me.Refresh()


                                    'then let's add new file info to derivedarr
                                    'compare existing deds(i) to derivedarr(6,tempi)
                                    For tempI = 0 To derivedArrlength

                                        If deIDs(i) = Int64.Parse(derivedArr(6, tempI)) Then


                                            derivedArr(8, tempI) = newfile.Id 'new id


                                            folder = m_conn.WebServiceManager.DocumentService.GetFolderById(newfile.FolderId)
                                            derivedArr(11, tempI) = folder.FullName
                                            derivedArr(12, tempI) = m_conn.WorkingFoldersManager.GetWorkingFolder(folder.FullName.ToString).ToString
                                            derivedArr(13, tempI) = newfile.MasterId
                                            derivedArr(21, tempI) = True
                                            Exit For


                                        End If
                                    Next



                                    'let's download file and update local file references
                                    Try

                                        Dim fileIteration As VDF.Vault.Currency.Entities.FileIteration = New VDF.Vault.Currency.Entities.FileIteration(m_conn, newfile)
                                        '  downloadticket = m_conn.WebServiceManager.DocumentService.GetDownloadTicketsByMasterIds(newfile.MasterId.ToSingleArray()).First()
                                        Dim settings As VDF.Vault.Settings.AcquireFilesSettings = New VDF.Vault.Settings.AcquireFilesSettings(m_conn)
                                        settings.CheckoutComment = "File is CheckedOut By UpdatingReferences"
                                        settings.LocalPath = Nothing
                                        settings.OptionsRelationshipGathering.FileRelationshipSettings.IncludeChildren = True
                                        settings.OptionsRelationshipGathering.FileRelationshipSettings.RecurseChildren = False
                                        settings.OptionsRelationshipGathering.FileRelationshipSettings.IncludeLibraryContents = True
                                        settings.OptionsRelationshipGathering.FileRelationshipSettings.VersionGatheringOption = Autodesk.DataManagement.Client.Framework.Vault.Currency.VersionGatheringOption.Latest
                                        settings.DefaultAcquisitionOption = VDF.Vault.Settings.AcquireFilesSettings.AcquisitionOption.Download
                                        settings.AddFileToAcquire(fileIteration, VDF.Vault.Settings.AcquireFilesSettings.AcquisitionOption.Download)
                                        Dim fResults As VDF.Vault.Results.AcquireFilesResults = m_conn.FileManager.AcquireFiles(settings)


                                        For Each acquireFilesResult As VDF.Vault.Results.FileAcquisitionResult In fResults.FileResults
                                            Dim assocs As ACW.FileAssocArray() = m_conn.WebServiceManager.DocumentService.GetFileAssociationsByIds(New Long() {acquireFilesResult.File.EntityIterationId}, FileAssociationTypeEnum.None, False, FileAssociationTypeEnum.Dependency, False, False, False)
                                            If assocs.First().FileAssocs Is Nothing Then
                                                Continue For
                                            End If

                                            'lets check that ERPTYPE = PAR* if not then Continue For
                                            fileIter = New VDF.Vault.Currency.Entities.FileIteration(m_conn, acquireFilesResult.File)
                                            strmyERPTYPE = m_conn.PropertyManager.GetPropertyValue(fileIter, myERPTYPE, Nothing)
                                            If Not myERPTYPE Is Nothing Then
                                                'if masterarr(18,0) is not nothing jump next
                                                Dim tempIII As Integer = Nothing
                                                For tempIII = 0 To mArrlength
                                                    If fileIter.EntityMasterId = masterArr(13, tempIII) Then
                                                        Exit For
                                                    End If
                                                Next

                                                ''If strmyERPTYPE Like "PAR*" = True And Split(fileIter.EntityName, ".").Last <> "iam" Then 'And masterArr(3, tempI) = "iam"  And Not masterArr(18, tempI) Is Nothing And Split(newfile.Name, ".").Last = "iam" Then 'fassoc is  assy in derived  component
                                                ''    '  ElseIf strmyERPTYPE Like "PAR*" = True And masterArr(3, tempI) = "ipt" And Not masterArr(18, tempI) Is Nothing And Split(newfile.Name, ".").Last = "ipt" Then
                                                ''Else
                                                ''    Continue 
                                                ''For ' not PAR* or is iam
                                                ''    ''End If
                                                ''    Else
                                                ''Continue For ' No ERPTYPE at all
                                                If tempIII <> Nothing Then
                                                    If derivedArr(9, tempIII).Substring(0, 1).ToLower = "c" Or derivedArr(9, tempIII) Like "*316*" Then
                                                    Else
                                                        Continue For
                                                    End If
                                                End If

                                            End If
                                            Dim fileAssocs = assocs.First().FileAssocs.Where(Function(fa) fa.ParFile.MasterId = acquireFilesResult.File.EntityMasterId)
                                            Dim refs = New List(Of VDF.Vault.Currency.FileSystem.FileReference)()
                                            For Each fileAssoc As FileAssoc In fileAssocs
                                                Dim fileCld = fResults.FileResults.FirstOrDefault(Function(f) f.File.EntityMasterId = fileAssoc.CldFile.MasterId)
                                                If fileCld Is Nothing Then
                                                    Continue For
                                                End If
                                                Dim reference = New VDF.Vault.Currency.FileSystem.FileReference(fileAssoc.RefId, fileCld.LocalPath, fileAssoc.Source)
                                                refs.Add(reference)
                                            Next
                                            Dim settings1 As VDF.Vault.Settings.AcquireFilesSettings = New VDF.Vault.Settings.AcquireFilesSettings(m_conn)
                                            settings1.AddFileToAcquire(fileIteration, VDF.Vault.Settings.AcquireFilesSettings.AcquisitionOption.Checkout)
                                            Dim f1Results As VDF.Vault.Results.AcquireFilesResults = m_conn.FileManager.AcquireFiles(settings1)

                                            Dim updateReferenceModel = New VDF.Vault.Models.UpdateFileReferencesModel()
                                            updateReferenceModel.SetTargetFilePath(acquireFilesResult.File, acquireFilesResult.LocalPath)
                                            updateReferenceModel.ForceUpdateOfTargetFilePaths = True

                                            updateReferenceModel.UpdateRefsInLocalFile(acquireFilesResult.File, acquireFilesResult.LocalPath, refs)

                                            updateReferenceModel.UpdateVaultStatus = True


                                            Try
                                                '    uploadTicket = m_conn.WebServiceManager.FilestoreService.CopyFile(downloadticket.Bytes, True, Nothing, results)
                                                Dim fldrPath As VDF.Currency.FolderPathAbsolute = New VDF.Currency.FolderPathAbsolute(derivedArr(12, tempI))
                                                Dim filePath As String = System.IO.Path.Combine(fldrPath.ToString(), fileIteration.EntityName)
                                                Dim filePathAbs As VDF.Currency.FilePathAbsolute = New VDF.Currency.FilePathAbsolute(filePath)
                                                m_conn.FileManager.CheckinFile(fileIteration, "Model paths updated in derived component", False, fileassocparams,
                                                                                                                  Nothing, False, Nothing,
                                                                                        ACW.FileClassification.None, False, filePathAbs)


                                            Catch ex As Exception
                                                MessageBox.Show(ex.Message & " Mek:derUpdate")
                                            End Try

                                        Next
                                    Catch ex As Exception
                                        MessageBox.Show(ex.Message & " Mek:derDownload")
                                    End Try

                                    'update also other possible instances with same Masterid
                                    Try
                                        For tempIi As Integer = 0 To derivedArrlength
                                            If deIDs(i) = Int64.Parse(derivedArr(6, tempIi)) Then

                                                derivedArr(8, tempIi) = newfile.Id 'new id

                                                folder = m_conn.WebServiceManager.DocumentService.GetFolderById(newfile.FolderId)
                                                derivedArr(11, tempIi) = folder.FullName
                                                derivedArr(12, tempIi) = m_conn.WorkingFoldersManager.GetWorkingFolder(folder.FullName.ToString).ToString
                                                derivedArr(13, tempIi) = newfile.MasterId
                                                derivedArr(21, tempI) = True
                                                Exit For
                                            End If
                                        Next
                                    Catch ex As Exception
                                        MessageBox.Show(ex.Message & " Mek:derivedArrUpdateafterUpdate")
                                    End Try







                                    i += 1 'next mids
                                Next
                            End If
                        End If
                    Catch ex As Exception
                        MessageBox.Show(ex.Message & " Mek:derivedMain")

                    End Try


                    '   Labeltext.Text = "Derived Models copied and updated"
                    '   Me.Refresh()
                    '**************************** update derived compononents in derivedarr**********END*********************************





                    ''****************************models are now copied and updated let us look for the drawings*****************************
                    'ids to download
                    Dim dIDs As Long() = Nothing
                    If drwArr IsNot Nothing Then
                        Try
                            dArrlength = drwArr.GetLength(1) - 1
                            For di = 0 To dArrlength
                                ReDim Preserve dIDs(di)
                                dIDs(di) = drwArr(6, di)
                            Next

                            'now drawing IDs are collected
                            Dim downloadTickets() As ACW.ByteArray = m_conn.WebServiceManager.DocumentService.GetDownloadTicketsByFileIds(dIDs)
                            Dim results As ACW.PropWriteResults = Nothing
                            Dim downloadticket As ACW.ByteArray = Nothing
                            Dim i As Integer = 0
                            For Each downloadticket In downloadTickets
                                '   Dim uploadTicket As Byte() = m_conn.WebServiceManager.FilestoreService.CopyFile(downloadticket.Bytes, True, Nothing, results)
                                '     Dim tbom As Autodesk.Connectivity.WebServices.BOM = m_conn.WebServiceManager.DocumentService.GetBOMByFileId(dIDs(i))
                                'get newfilename and newfolderid
                                Dim newName As String = Nothing
                                Dim newNameFirstLetter As String = Nothing
                                Dim newFolderID As Long = Nothing
                                Dim copiedfrom As String = Nothing
                                Dim oldMasterid As Long = Nothing
                                Dim parfile As ACW.File = m_conn.WebServiceManager.DocumentService.FindFilesByIds(dIDs(i).ToSingleArray()).First
                                For tempI = 0 To dArrlength 'change m ->d
                                    If dIDs(i) = Int64.Parse(drwArr(6, tempI)) Then
                                        newName = drwArr(9, tempI)
                                        newNameFirstLetter = newName.Substring(0, 1).ToLower()
                                        newFolderID = drwArr(10, tempI)
                                        copiedfrom = drwArr(4, tempI)
                                        oldMasterid = Int64.Parse(drwArr(14, tempI))
                                        Exit For 'note if there is second similar instance in MasterArr, it has to be solved later on.

                                    End If
                                Next

                                'lets get the linked models (there can be several models connected)

                                Dim param As New ACW.FileAssocParam


                                Dim fileassocparamsdrwstr(,) As String = Nothing


                                fi = 0
                                fassocArr = m_conn.WebServiceManager.DocumentService.GetLatestFileAssociationsByMasterIds(oldMasterid.ToSingleArray, FileAssociationTypeEnum.None, False, FileAssociationTypeEnum.Dependency, False, False, False, False).First
                                For Each Assoc As FileAssoc In fassocArr.FileAssocs
                                    'lets check is the file updated

                                    For assocI As Integer = 0 To mArrlength
                                        Dim assocfound As Boolean = False
                                        If Not masterArr(8, assocI) Is Nothing Then
                                            If Assoc.CldFile.Id = Int64.Parse(masterArr(8, assocI)) Then
                                                '  ReDim Preserve fileassocparamsdrw(fi)
                                                ReDim Preserve fileassocparamsdrwstr(4, fi)
                                                ' param.CldFileId = Assoc.CldFile.Id
                                                fileassocparamsdrwstr(0, fi) = Assoc.CldFile.Id
                                                ' param.RefId = Assoc.RefId
                                                fileassocparamsdrwstr(1, fi) = Assoc.RefId
                                                '  param.Source = Assoc.Source
                                                fileassocparamsdrwstr(2, fi) = Assoc.Source
                                                ' param.Typ = Assoc.Typ
                                                fileassocparamsdrwstr(3, fi) = Assoc.Typ
                                                folder = m_conn.WebServiceManager.DocumentService.GetFolderById(Assoc.CldFile.FolderId)
                                                ' param.ExpectedVaultPath = Assoc.ExpectedVaultPath
                                                fileassocparamsdrwstr(4, fi) = Assoc.ExpectedVaultPath
                                                '  fileassocparam = param
                                                assocfound = True
                                            ElseIf assocfound = False Then ' new assoc lets' find new ID

                                                If Assoc.CldFile.Id = Int64.Parse(masterArr(6, assocI)) Then
                                                    '  ReDim Preserve fileassocparamsdrw(fi)
                                                    ReDim Preserve fileassocparamsdrwstr(4, fi)
                                                    If Not masterArr(8, assocI) Is Nothing Then
                                                        '     param.CldFileId = Int64.Parse(masterArr(8, assocI))
                                                        fileassocparamsdrwstr(0, fi) = Int64.Parse(masterArr(8, assocI))
                                                    End If
                                                    '  param.RefId = Assoc.RefId
                                                    fileassocparamsdrwstr(1, fi) = Assoc.RefId
                                                    '   param.Source = Assoc.Source
                                                    fileassocparamsdrwstr(2, fi) = Assoc.Source
                                                    '  param.Typ = Assoc.Typ
                                                    fileassocparamsdrwstr(3, fi) = Assoc.Typ
                                                    folder = m_conn.WebServiceManager.DocumentService.GetFolderById(Int64.Parse(masterArr(10, assocI)))
                                                    ' param.ExpectedVaultPath = masterArr(11, assocI) & "/" & masterArr(9, assocI)
                                                    fileassocparamsdrwstr(4, fi) = masterArr(11, assocI) & "/" & masterArr(9, assocI)
                                                    '  fileassocparam = param

                                                    assocfound = True

                                                ElseIf assocfound = False Then
                                                    'lets go through derived arr
                                                    If derivedArr IsNot Nothing Then


                                                        For assocd As Integer = 0 To derivedArrlength
                                                            If Not derivedArr(8, assocd) Is Nothing Then
                                                                If Assoc.CldFile.Id = Int64.Parse(derivedArr(6, assocd)) Then
                                                                    '   ReDim Preserve fileassocparamsdrw(fi)
                                                                    ReDim Preserve fileassocparamsdrwstr(4, fi)
                                                                    '   param.CldFileId = Assoc.CldFile.Id
                                                                    fileassocparamsdrwstr(0, fi) = Int64.Parse(derivedArr(8, assocd))
                                                                    '   param.RefId = Assoc.RefId
                                                                    fileassocparamsdrwstr(1, fi) = Assoc.RefId
                                                                    '     param.Source = Assoc.Source
                                                                    fileassocparamsdrwstr(2, fi) = Assoc.Source
                                                                    '    param.Typ = Assoc.Typ
                                                                    fileassocparamsdrwstr(3, fi) = Assoc.Typ
                                                                    folder = m_conn.WebServiceManager.DocumentService.GetFolderById(Int64.Parse(derivedArr(10, assocd)))
                                                                    ' param.ExpectedVaultPath = masterArr(11, assocI) & "/" & masterArr(9, assocI)
                                                                    fileassocparamsdrwstr(4, fi) = derivedArr(11, assocd) & "/" & derivedArr(9, assocd)
                                                                    '  fileassocparam = param
                                                                    '    fileassocparam = param
                                                                    assocfound = True
                                                                    Exit For

                                                                End If

                                                            End If

                                                        Next
                                                    End If


                                                End If
                                            End If
                                        End If
                                    Next


                                    '  fileassocparamsdrw(fi) = fileassocparam
                                    fi += 1
                                Next
                                'now we have fileassocparams and  fileassocparamsdrwstr
                                fi = 0
                                Dim fileassocparams2 As ACW.FileAssocParam()
                                For fi = 0 To fileassocparamsdrwstr.Length / 5 - 1
                                    Dim param1 As New ACW.FileAssocParam
                                    param1.CldFileId = Int64.Parse(fileassocparamsdrwstr(0, fi))
                                    param1.RefId = Int64.Parse(fileassocparamsdrwstr(1, fi))
                                    param1.Source = fileassocparamsdrwstr(2, fi)
                                    param1.Typ = Int64.Parse(fileassocparamsdrwstr(3, fi))
                                    param1.ExpectedVaultPath = fileassocparamsdrwstr(4, fi)
                                    ReDim Preserve fileassocparams2(fi)
                                    fileassocparams2(fi) = param1
                                Next


                                '***************Properties**************
                                Dim fileProps As CtntSrcPropDef() = m_conn.WebServiceManager.FilestoreService.GetContentSourcePropertyDefinitions(downloadticket.Bytes, True)
                                Dim propSet = fileProps.Where(Function(n) n.Typ = DataType.[String])
                                Dim pNumber As String = drwArr(9, tempI)
                                pNumber = Strings.Left(pNumber, pNumber.Length - 4)
                                '  Dim checkedOutFile As ACW.File = m_conn.WebServiceManager.DocumentService.CheckoutFile(fileiteration.EntityIterationId, CheckoutFileOptions.Master, My.Computer.Name.ToString, "c:\temp", "test create new version", downloadticket)
                                Dim oPropWriterequests As New ACW.PropWriteRequests
                                Dim propWrites() As PropWriteReq = Nothing
                                Dim propsetI As Integer = 0

                                Try


                                    For Each prop As CtntSrcPropDef In propSet

                                        If prop.DispName = "Part Number" Then


                                            Dim propWrite As New PropWriteReq() With {
                        .CanCreate = False,
                        .Moniker = prop.Moniker,
                        .Val = pNumber.ToUpper
                    }
                                            ReDim Preserve propWrites(propsetI)
                                            propWrites(propsetI) = propWrite
                                            propsetI += 1
                                        ElseIf prop.DispName = "ERPCODE" Then
                                            Dim propWrite As New PropWriteReq() With {
                                        .CanCreate = False,
                                        .Moniker = prop.Moniker,
                                        .Val = pNumber.ToUpper
                                    }
                                            ReDim Preserve propWrites(propsetI)
                                            propWrites(propsetI) = propWrite
                                            propsetI += 1

                                        ElseIf prop.DispName = "SHIP_SERIE" And newNameFirstLetter = "c" Then
                                            Dim propWrite As New PropWriteReq() With {
                                        .CanCreate = False,
                                        .Moniker = prop.Moniker,
                                        .Val = ComboBox1.Text.ToUpper
                                    }
                                            ReDim Preserve propWrites(propsetI)
                                            propWrites(propsetI) = propWrite
                                            propsetI += 1
                                        ElseIf prop.DispName = "SK_ITEM" And newNameFirstLetter = "c" Then
                                            Dim propWrite As New PropWriteReq() With {
                                        .CanCreate = False,
                                        .Moniker = prop.Moniker,
                                        .Val = TextBox2.Text.ToUpper
                                    }
                                            ReDim Preserve propWrites(propsetI)
                                            propWrites(propsetI) = propWrite
                                            propsetI += 1
                                        ElseIf prop.DispName = "AREA" And newNameFirstLetter = "c" Then
                                            Dim propWrite As New PropWriteReq() With {
                                        .CanCreate = False,
                                        .Moniker = prop.Moniker,
                                        .Val = TextBox3.Text.ToUpper
                                    }
                                            ReDim Preserve propWrites(propsetI)
                                            propWrites(propsetI) = propWrite
                                            propsetI += 1
                                        ElseIf prop.DispName = "AREA_NAME" And newNameFirstLetter = "c" Then
                                            Dim propWrite As New PropWriteReq() With {
                                        .CanCreate = False,
                                        .Moniker = prop.Moniker,
                                        .Val = ComboBox3.Text.ToUpper
                                    }
                                            ReDim Preserve propWrites(propsetI)
                                            propWrites(propsetI) = propWrite
                                            propsetI += 1
                                        ElseIf prop.DispName = "DECK" And newNameFirstLetter = "c" Then
                                            Dim propWrite As New PropWriteReq() With {
                                        .CanCreate = False,
                                        .Moniker = prop.Moniker,
                                        .Val = TextBox5.Text.ToUpper
                                    }
                                            ReDim Preserve propWrites(propsetI)
                                            propWrites(propsetI) = propWrite
                                            propsetI += 1
                                        ElseIf prop.DispName = "FIREZONE" And newNameFirstLetter = "c" Then
                                            Dim propWrite As New PropWriteReq() With {
                                        .CanCreate = False,
                                        .Moniker = prop.Moniker,
                                        .Val = TextBox6.Text.ToUpper
                                    }
                                            ReDim Preserve propWrites(propsetI)
                                            propWrites(propsetI) = propWrite
                                            propsetI += 1
                                        ElseIf prop.DispName = "YARD" And newNameFirstLetter = "c" Then
                                            Dim propWrite As New PropWriteReq() With {
                                        .CanCreate = False,
                                        .Moniker = prop.Moniker,
                                        .Val = ComboBox2.Text.ToUpper
                                    }
                                            ReDim Preserve propWrites(propsetI)
                                            propWrites(propsetI) = propWrite
                                            propsetI += 1
                                        ElseIf prop.DispName = "SEED" And newNameFirstLetter = "c" Then
                                            Dim propWrite As New PropWriteReq() With {
                                                .CanCreate = False,
                                                .Moniker = prop.Moniker,
                                                .Val = copiedfrom.Split(".").First
                                            }
                                            ReDim Preserve propWrites(propsetI)
                                            propWrites(propsetI) = propWrite
                                            propsetI += 1
                                        End If
                                    Next

                                Catch ex As Exception
                                    MessageBox.Show(ex.Message & " Mek:drwPropertiesUpdate")
                                End Try
                                If propWrites IsNot Nothing Then
                                    oPropWriterequests.Requests = propWrites
                                End If
                                '***************Properties end**********


                                Try


                                    'let's make a copy
                                    Dim Ext As String = newName.Split(".").Last
                                    Dim uploadTicket As Byte() = m_conn.WebServiceManager.FilestoreService.CopyFile(downloadticket.Bytes, Ext, True, oPropWriterequests, results)
                                    newfile = m_conn.WebServiceManager.DocumentService.AddUploadedFile(newFolderID, newName, "New copy from " & copiedfrom, DateTime.Now, fileassocparams2, Nothing,
                                             parfile.FileClass, parfile.Hidden, uploadTicket.ToByteArray())

                                    Labeltext.Text = proggStep & " file(s) copied and updated."
                                    proggStep += 1
                                    Me.Refresh()

                                    'then let's add new file info to drwrarr
                                    'compare existing mids(i) to dwrarr(6,tempi)





                                    For tempI = 0 To dArrlength

                                        If dIDs(i) = Int64.Parse(drwArr(6, tempI)) Then

                                            drwArr(8, tempI) = newfile.Id 'new id
                                            folder = m_conn.WebServiceManager.DocumentService.GetFolderById(newfile.FolderId)
                                            drwArr(11, tempI) = folder.FullName
                                            drwArr(12, tempI) = m_conn.WorkingFoldersManager.GetWorkingFolder(folder.FullName.ToString).ToString
                                            drwArr(13, tempI) = newfile.MasterId
                                            Exit For
                                        End If
                                    Next

                                Catch ex As Exception
                                    ' MessageBox.Show(ex.Message & " Mek:drwCopydone")
                                End Try


                                'Update local refs
                                'let's download file and update local file references

                                Try

                                    Dim fileIteration As VDF.Vault.Currency.Entities.FileIteration = New VDF.Vault.Currency.Entities.FileIteration(m_conn, newfile)
                                    Dim settings As VDF.Vault.Settings.AcquireFilesSettings = New VDF.Vault.Settings.AcquireFilesSettings(m_conn)
                                    settings.CheckoutComment = "File is CheckedOut By UpdatingReferences"
                                    settings.LocalPath = Nothing
                                    settings.OptionsRelationshipGathering.FileRelationshipSettings.IncludeRelatedDocumentation = False
                                    settings.OptionsRelationshipGathering.FileRelationshipSettings.IncludeChildren = True
                                    settings.OptionsRelationshipGathering.FileRelationshipSettings.RecurseChildren = True
                                    settings.OptionsRelationshipGathering.FileRelationshipSettings.IncludeParents = True
                                    settings.OptionsRelationshipGathering.FileRelationshipSettings.IncludeLibraryContents = True
                                    settings.OptionsRelationshipGathering.FileRelationshipSettings.VersionGatheringOption = Autodesk.DataManagement.Client.Framework.Vault.Currency.VersionGatheringOption.Latest
                                    settings.DefaultAcquisitionOption = VDF.Vault.Settings.AcquireFilesSettings.AcquisitionOption.NoAction
                                    settings.AddFileToAcquire(fileIteration, VDF.Vault.Settings.AcquireFilesSettings.AcquisitionOption.Download)
                                    Dim fResults As VDF.Vault.Results.AcquireFilesResults = m_conn.FileManager.AcquireFiles(settings)


                                    For Each acquireFilesResult As VDF.Vault.Results.FileAcquisitionResult In fResults.FileResults
                                        Dim assocs As ACW.FileAssocArray() = m_conn.WebServiceManager.DocumentService.GetFileAssociationsByIds(New Long() {acquireFilesResult.File.EntityIterationId},
                                     FileAssociationTypeEnum.None, False, FileAssociationTypeEnum.Dependency, False, False, False)
                                        If assocs.First().FileAssocs Is Nothing Then
                                            Continue For
                                        End If
                                        fileIter = New VDF.Vault.Currency.Entities.FileIteration(m_conn, acquireFilesResult.File)
                                        ''strmyERPTYPE = m_conn.PropertyManager.GetPropertyValue(fileIter, myERPTYPE, Nothing)
                                        If Not myERPTYPE Is Nothing Then
                                            If strmyERPTYPE Like "PAR*" = True Then
                                            Else
                                                GoTo dwgcheck ' not PAR*
                                            End If
                                        Else
                                            Continue For ' No ERPTYPE at all
                                        End If
                                        'if file is model it won't be updated
dwgcheck:
                                        If Split(fileIter.EntityName, ".").Last <> "dwg" Then
                                            Continue For
                                        End If

                                        Dim fileAssocs = assocs.First().FileAssocs.Where(Function(fa) fa.ParFile.MasterId = acquireFilesResult.File.EntityMasterId)
                                        Dim refs = New List(Of VDF.Vault.Currency.FileSystem.FileReference)()
                                        For Each fileAssoc As FileAssoc In fileAssocs
                                            Dim fileCld = fResults.FileResults.FirstOrDefault(Function(f) f.File.EntityMasterId = fileAssoc.CldFile.MasterId)
                                            If fileCld Is Nothing Then
                                                Continue For
                                            End If
                                            Dim reference = New VDF.Vault.Currency.FileSystem.FileReference(fileAssoc.RefId, fileCld.LocalPath, fileAssoc.Source)
                                            refs.Add(reference)
                                        Next
                                        Dim settings1 As VDF.Vault.Settings.AcquireFilesSettings = New VDF.Vault.Settings.AcquireFilesSettings(m_conn)
                                        settings1.AddFileToAcquire(fileIteration, VDF.Vault.Settings.AcquireFilesSettings.AcquisitionOption.Checkout)

                                        Dim f1Results As VDF.Vault.Results.AcquireFilesResults = m_conn.FileManager.AcquireFiles(settings1)



                                        Dim updateReferenceModel = New VDF.Vault.Models.UpdateFileReferencesModel()
                                        updateReferenceModel.SetTargetFilePath(acquireFilesResult.File, acquireFilesResult.LocalPath)
                                        updateReferenceModel.ForceUpdateOfTargetFilePaths = True
                                        updateReferenceModel.UpdateRefsInLocalFile(acquireFilesResult.File, acquireFilesResult.LocalPath, refs)
                                        updateReferenceModel.UpdateVaultStatus = True

                                    Next
                                Catch ex As Exception
                                    MessageBox.Show(ex.Message & " Mek:drwDownloadAndUpdate")
                                End Try

                                Try

                                    Dim fileIteration As VDF.Vault.Currency.Entities.FileIteration = New VDF.Vault.Currency.Entities.FileIteration(m_conn, newfile)
                                    '    uploadTicket = m_conn.WebServiceManager.FilestoreService.CopyFile(downloadticket.Bytes, True, Nothing, results)
                                    Dim fldrPath As VDF.Currency.FolderPathAbsolute = New VDF.Currency.FolderPathAbsolute(drwArr(12, tempI))
                                    Dim filePath As String = System.IO.Path.Combine(fldrPath.ToString(), fileIteration.EntityName)
                                    Dim filePathAbs As VDF.Currency.FilePathAbsolute = New VDF.Currency.FilePathAbsolute(filePath)
                                    m_conn.FileManager.CheckinFile(fileIteration, "Model paths updated in drawing", False, fileassocparams2,
                                                                                                                                              Nothing, False, Nothing,
                                                                                                                    ACW.FileClassification.DesignDocument, False, filePathAbs)



                                Catch ex As Exception
                                    MessageBox.Show(ex.Message & " Mek:drwUpdate")

                                End Try


                                i += 1

                            Next
                            ' Labeltext.Text = "Drawings copied and updated"
                            '  Me.Refresh()




                        Catch ex As Exception
                            MessageBox.Show(ex.Message & " Mek:DrwMain")
                            fids = Nothing
                            dIDs = Nothing
                        End Try
                        'aprentice ends
                    End If


                End If 'second latest

            End If 'final if


        Catch ex As Exception
            MessageBox.Show(ex.Message & " Mek:MainTry")
            Button3.Enabled = True

        End Try
        'let's clear arrays
        Label6.Text = Label6.Text & " | New Model: " & (masterArr(9, 0))
        My.Computer.Clipboard.SetText((masterArr(9, 0)))



        ' numbofSteps = numbofSteps + numbofSteps

        Labeltext.Text = "Done!"
        Button3.Enabled = True

        proggStep = Nothing
        Me.Refresh()
        '**********************************************************************INSERT TO CAD********************************************
        Try
            If CheckBox1.Checked = True Then
                ''                'FolderPath

                ''                Dim totalResults As New List(Of ACW.File)()

                ''                'search file
                Dim mgr As WebServiceManager = m_conn.WebServiceManager
                ''                Dim filePropDefs As PropDef() = mgr.PropertyService.GetPropertyDefinitionsByEntityClassId("FILE")
                ''                Dim fileNamePropDef As PropDef = filePropDefs.[Single](Function(n) n.SysName = "ClientFileName")
                ''                Dim fname As New SrchCond() With {
                ''                                    .PropDefId = fileNamePropDef.Id,
                ''                .PropTyp = PropertySearchType.SingleProperty,
                ''       .SrchOper = 3,
                ''       .SrchRule = SearchRuleType.Must,
                ''       .SrchTxt = masterArr(9, 0)
                ''}
                ''                Dim bookmark As String = String.Empty
                ''                Dim status As SrchStatus = Nothing

                ''                While status Is Nothing OrElse totalResults.Count < status.TotalHits

                ''                    Dim results As ACW.File() =
                ''                    mgr.DocumentService.FindFilesBySearchConditions(
                ''           New SrchCond() {fname},
                ''          Nothing, Nothing, False, True, bookmark,
                ''              status)
                ''                    If results IsNot Nothing Then
                ''                        totalResults.AddRange(results)
                ''                    Else
                ''                        Exit While
                ''                    End If
                ''                End While
                Dim selectedfile As ACW.File = Nothing
                Try


                    selectedfile = mgr.DocumentService.GetLatestFileByMasterId(masterArr(13, 0))
                Catch ex As Exception

                End Try



                'insert file
                Try


                    If selectedfile IsNot Nothing Then
                        SKCopy2Project.SKCopy2ProjectCommandExtension.place2Inventor(selectedfile)
                    End If

                Catch ex As Exception

                End Try




            End If

        Catch ex As Exception

        End Try
        masterArr = Nothing
        drwArr = Nothing
        derivedArr = Nothing
    End Sub
    Private Sub ComboBox1_SelectedIndexChanged(sender As Object, e As EventArgs) Handles ComboBox1.SelectedIndexChanged
        getAreaCodes()
        getYard()

    End Sub

    Private Sub getAreaCodes()
        Me.Cursor = Cursors.WaitCursor
        ' SuspendLayout()
        'find correct excel dokument

        Try





            Dim path As String = My.Computer.FileSystem.SpecialDirectories.MyDocuments & "\Vault\System\MPS\LinkIt_Shared\Data\ShipSeries"
            Dim FileLocation As System.IO.DirectoryInfo = New System.IO.DirectoryInfo(path)
            Dim fi As System.IO.FileInfo() = FileLocation.GetFiles(ComboBox1.Text & ".*")
            ''Dim conn As System.Data.OleDb.OleDbConnection = New System.Data.OleDb.OleDbConnection("Provider=Microsoft.ACE.OLEDB.12.0;Data Source=" + fi(0).FullName + ";Extended Properties=Excel 12.0;")
            ''conn.Open()
            ''Dim Dataset = New System.Data.DataSet
            ''Dim DataTable = conn.GetOleDbSchemaTable(OleDbSchemaGuid.Tables, Nothing)
            ''Dim sheetname As String = DataTable.Rows(1)("table_name").ToString
            ''Dim excelcomm As OleDbCommand = New OleDbCommand("select * from [" + sheetname + "]", conn)
            ''Dim adexcel As OleDbDataAdapter = New OleDbDataAdapter(excelcomm)
            ''adexcel.Fill(Dataset)
            ''Dim r As Integer = 0
            ''Dim c As Integer = 0
            ''ReDim sk_ship(Dataset.Tables(0).Rows.Count - 1, Dataset.Tables(0).Columns.Count - 1)

            '''first sk_ship
            ''For Each Row As DataRow In Dataset.Tables(0).Rows
            ''    c = 0
            ''    For Each Coll As DataColumn In Dataset.Tables(0).Columns
            ''        sk_ship(r, c) = Row(Coll.ColumnName).ToString()
            ''        c += 1
            ''    Next
            ''    r += 1
            ''Next

            '''then sk_Area
            ''Dim Dataset1 = New System.Data.DataSet
            ''sheetname = DataTable.Rows(0)("table_name").ToString
            ''excelcomm = New OleDbCommand("select * from [" + sheetname + "]", conn)
            ''adexcel = New OleDbDataAdapter(excelcomm)
            ''adexcel.Fill(Dataset1)
            ''r = 0
            ''c = 0
            ''ReDim SK_Area(Dataset1.Tables(0).Rows.Count - 1, Dataset1.Tables(0).Columns.Count - 1)
            ''For Each Row As DataRow In Dataset1.Tables(0).Rows
            ''    c = 0
            ''    For Each Coll As DataColumn In Dataset1.Tables(0).Columns
            ''        SK_Area(r, c) = Row(Coll.ColumnName).ToString()
            ''        c += 1
            ''    Next
            ''    r += 1
            ''Next

            ''conn.Close()
            Try
                Dim xlApp As Excel.Application
                Dim xlWorkBook As Excel.Workbook
                Dim xlWorkSheet As Excel.Worksheet

                xlApp = New Excel.Application
                xlWorkBook = xlApp.Workbooks.Open(fi(0).FullName, False, True)
                xlWorkSheet = xlWorkBook.Worksheets("Ship")
                Dim lastRow As Long = xlWorkSheet.UsedRange.Rows.Count

                If lastRow > 1 Then
                    lastRow = xlWorkSheet.UsedRange.Rows.Count

                    Dim rng As Excel.Range = xlWorkSheet.Range("A2:D" & lastRow)
                    sk_ship = CType(rng.Value, Object(,))
                    'create combolist

                    For i As Integer = 1 To sk_ship.GetLength(0) '(1)
                        ReDim Preserve SK_ShipCode(i - 1)
                        SK_ShipCode(i - 1) = sk_ship(i, 1)
                    Next
                    '   Dim col As New AutoCompleteStringCollection
                    makeCombolist(ComboBox2, SK_ShipCode)

                    'next page Area
                    xlWorkSheet = xlWorkBook.Worksheets("Area")
                    lastRow = 0
                    lastRow = xlWorkSheet.UsedRange.Rows.Count

                    rng = xlWorkSheet.Range("A2:E" & lastRow)
                    SK_Area = CType(rng.Value, Object(,))
                    '    Dim MyArray As Object(,) = CType(rng.Value, Object(,))
                    'create combolist list
                    For i As Integer = 1 To SK_Area.GetLength(0)
                        If SK_Area(i, 2) IsNot Nothing Then



                            ReDim Preserve sk_AreaCode(i - 1)
                            sk_AreaCode(i - 1) = SK_Area(i, 2)
                        End If
                    Next
                    '   col.Clear()

                    'autoCompleteCombolist(col, ComboBox3, sk_AreaCode)
                    ' Dim col As  AutoCompleteStringCollection
                    ' autoCompleteTextBox(TextBox6, sk_AreaCode)

                End If




                xlWorkBook.Close()
                xlApp.Quit()
            Catch ex As Exception

            End Try




            '''create combolist list
            ''For i As Integer = 0 To SK_Area.GetLength(0) - 1
            ''    ReDim Preserve sk_AreaCode(i)
            ''    sk_AreaCode(i) = SK_Area(i, 1)
            ''Next
            Dim col As New AutoCompleteStringCollection
            col.Clear()

            '  autoCompleteCombolist(col, ComboBox3, sk_AreaCode)
            autoCompleteTextBox(col, TextBox3, sk_AreaCode)

            ''End If

            ''xlWorkBook.Close()
            ''xlApp.Quit()
            ClearCombo(ComboBox1.TabIndex)





        Catch ex As Exception

        End Try
        Try
            '  ResumeLayout()
            Me.Cursor = Cursors.Arrow
            '  ComboBox2.DroppedDown = True
        Catch ex As Exception

        End Try
    End Sub
    Private Sub getYard()
        Try
            If ComboBox2.Items.Count > 1 Then ComboBox2.Items.Clear()
            ReDim SK_ShipCode(0)
            For i As Integer = 1 To sk_ship.GetLength(0)
                If sk_ship(i, 3) IsNot Nothing Then
                    ReDim Preserve SK_ShipCode(i - 1) 'this is YARD in this case
                    SK_ShipCode(i - 1) = sk_ship(i, 3)
                End If
            Next

            ComboBox2.Text = ""
            makeCombolist(ComboBox2, SK_ShipCode)
        Catch ex As Exception

        End Try
    End Sub
    Private Sub TextBox3_TextChanged(sender As Object, e As EventArgs) Handles TextBox3.TextChanged
        'find values for areaname (combobox3)

        Try
            ComboBox3.Items.Clear()
            ComboBox3.Text = ""
            TextBox5.Text = "" 'deck
            TextBox6.Text = "" 'fz
            For i As Integer = 1 To SK_Area.GetLength(0)
                If SK_Area(i, 2).ToString IsNot Nothing Then
                    If TextBox3.Text = SK_Area(i, 2).ToString Then
                        ComboBox3.Items.Add(SK_Area(i, 3))
                    End If
                End If
            Next

        Catch ex As Exception

        End Try

        Try
            If ComboBox3.Items.Count = 1 Then 'only one in list
                '   isundermod = True
                ComboBox3.Text = ComboBox3.Items(0)
                '   isundermod = False
            End If
        Catch ex As Exception

        End Try



        'find values for AreaName, Deck and firezone
        ''Try


        ''    For i As Integer = 1 To SK_Area.GetLength(0)
        ''        If TextBox3.Text = SK_Area(i, 2) Then
        ''            TextBox4.Text = SK_Area(i, 3).ToString.ToUpper
        ''            TextBox5.Text = SK_Area(i, 4).ToString.ToUpper
        ''            TextBox6.Text = SK_Area(i, 5).ToString.ToUpper

        ''            Exit For
        ''        Else
        ''            TextBox4.Text = ""
        ''            TextBox5.Text = ""
        ''            TextBox6.Text = ""
        ''        End If
        ''    Next
        ''Catch ex As Exception

        ''End Try

    End Sub

    Private Sub ComboBox3_SelectedIndexChanged(sender As Object, e As EventArgs) Handles ComboBox3.SelectedIndexChanged
        'update deck and zone



        For i As Integer = 1 To SK_Area.GetLength(0)
            If TextBox3.Text = SK_Area(i, 2) Then
                TextBox5.Text = SK_Area(i, 4).ToString.ToUpper
                TextBox6.Text = SK_Area(i, 5).ToString.ToUpper

                Exit For
            Else

                TextBox5.Text = ""
                TextBox6.Text = ""
            End If
        Next

    End Sub
    Private Function autoCompleteTextBox(ByVal col As AutoCompleteStringCollection, tBox As Windows.Forms.TextBox, mylist As String())
        If Not mylist Is Nothing Then
            '  cBox.Visible = False

            For Each str As String In mylist
                col.Add(str.ToString)
                ' tBox.DisplayMember = str.ToString
                ' cBox.ValueMember = str.ToString

            Next
            tBox.AutoCompleteSource = AutoCompleteSource.CustomSource
            tBox.AutoCompleteMode = AutoCompleteMode.SuggestAppend

            tBox.AutoCompleteCustomSource = col

            'cBox.Visible = true
        End If
    End Function
    Private Function ClearCombo(ByVal tabindexparent As Integer)
        'got through form components
        For Each ctrl As Control In Me.Controls
            If TypeOf ctrl Is ComboBox Then
                Dim cBox As ComboBox = ctrl
                'get tabindex from comp
                If tabindexparent < cBox.TabIndex Then
                    cBox.Text = ""
                End If

            ElseIf TypeOf ctrl Is Windows.Forms.TextBox Then
                Dim tBox As Windows.Forms.TextBox = ctrl
                'get tabindex from comp
                If tabindexparent < tBox.TabIndex Then
                    tBox.Text = ""

                End If
            End If
        Next


    End Function

    Public Function getFileIteration _
          (nameOfFile As String,
                  connection As _
             VDF.Vault.Currency.
         Connections.Connection) _
          As VDF.Vault.Currency.
           Entities.FileIteration

        Dim conditions As ACW.SrchCond()

        ReDim conditions(0)

        Dim lCode As Long = 1

        Dim Defs As ACW.PropDef() =
      connection.WebServiceManager.
                   PropertyService.
    GetPropertyDefinitionsByEntityClassId("FILE")

        Dim Prop As ACW.PropDef = Nothing

        For Each def As ACW.PropDef In Defs
            If def.DispName =
                      "File Name" Then
                Prop = def
            End If
        Next def

        Dim searchCondition As _
    ACW.SrchCond = New ACW.SrchCond()

        searchCondition.PropDefId =
                              Prop.Id

        searchCondition.PropTyp =
    ACW.PropertySearchType.SingleProperty
        searchCondition.SrchOper = lCode

        searchCondition.SrchTxt = nameOfFile

        conditions(0) = searchCondition

        ' search for files
        Dim FileList As List _
    (Of Autodesk.Connectivity.WebServices.File) =
        New List _
    (Of Autodesk.Connectivity.WebServices.File) '()
        Dim sBookmark As String = String.Empty
        Dim Status As ACW.SrchStatus = Nothing

        While (Status Is Nothing OrElse
             FileList.Count < Status.TotalHits)

            Dim files As Autodesk.Connectivity.
    WebServices.File() = connection.WebServiceManager.
    DocumentService.FindFilesBySearchConditions _
                                 (conditions,
                Nothing, Nothing, True, True,
                             sBookmark, Status)

            If (Not files Is Nothing) Then
                FileList.AddRange(files)
            End If
        End While

        Dim oFileIteration As _
            VDF.Vault.Currency.Entities.
                   FileIteration = Nothing
        For i As Integer =
                  0 To FileList.Count - 1
            If FileList(i).Name =
                          nameOfFile Then
                oFileIteration =
               New VDF.Vault.Currency.
    Entities.FileIteration(connection,
                            FileList(i))
            End If
        Next

        Return oFileIteration

    End Function
    Public Function GetFileAndAssociations(path As String, ByRef fileAssocParams As ArrayList) As Autodesk.Connectivity.WebServices.File
        Dim paths As String() = New String(0) {}

        paths(0) = path
        Dim files As Autodesk.Connectivity.WebServices.File() = m_conn.WebServiceManager.DocumentService.FindLatestFilesByPaths(paths)

        Dim assocArray As ACW.FileAssocArray() = m_conn.WebServiceManager.DocumentService.GetFileAssociationsByIds(New Long() {files(0).Id}, ACW.FileAssociationTypeEnum.None, False, ACW.FileAssociationTypeEnum.Dependency, True, False, True) ' tähän muutettiin kaikki depencyt

        Dim assemblyAssoc As ACW.FileAssocArray = assocArray(0)

        If assemblyAssoc IsNot Nothing Then
            For Each assoc As ACW.FileAssoc In assemblyAssoc.FileAssocs

                Dim param1 As New ACW.FileAssocParam()
                param1.CldFileId = assoc.CldFile.Id
                param1.RefId = assoc.RefId
                param1.Source = assoc.Source
                param1.Typ = assoc.Typ
                param1.ExpectedVaultPath = assoc.ExpectedVaultPath

                fileAssocParams.Add(param1)
            Next
        End If
        Return files(0)
    End Function
    Public Function AddMyAttachments _
                 (nameOfFileToAttachTo As String,
                       myFileIterationIds() As Long,
            myFolderIdOfNewFileIteration As Long,
            connection As _
    VDF.Vault.Currency.Connections.Connection) _
                                      As Boolean
        Dim oFileIteration As _
    VDF.Vault.Currency.Entities.FileIteration =
getFileIteration(nameOfFileToAttachTo, connection)



        If oFileIteration Is Nothing Then
            MessageBox.Show("Unable to get FileIteration")
            Return False
        End If

        ' Get settings
        Dim oSettings As _
        VDF.Vault.Settings.AcquireFilesSettings =
      New VDF.Vault.Settings.AcquireFilesSettings _
                                       (connection)

        ' Going to Check Out (not download file)
        oSettings.DefaultAcquisitionOption =
        VDF.Vault.Settings.AcquireFilesSettings.
                        AcquisitionOption.Checkout

        ' add the file to the settings
        oSettings.AddEntityToAcquire(oFileIteration)

        'Do the CheckOut
        Dim myAcquireVaultSettings As _
        VDF.Vault.Results.AcquireFilesResults
        myAcquireVaultSettings =
  connection.FileManager.AcquireFiles(oSettings)

        Dim oNewFileIteration As _
        VDF.Vault.Currency.Entities.FileIteration

        Dim myFileAcqRes As _
        VDF.Vault.Results.FileAcquisitionResult

        myFileAcqRes =
          myAcquireVaultSettings.FileResults(0)

        oNewFileIteration =
                   myFileAcqRes.NewFileIteration

        If oNewFileIteration.IsCheckedOut = True Then

            ' settings used in GetFileAssociationLites()
            Dim myFileRelationshipSettings As _
VDF.Vault.Settings.FileRelationshipGatheringSettings
            myFileRelationshipSettings =
New VDF.Vault.Settings.FileRelationshipGatheringSettings

            myFileRelationshipSettings.
                            IncludeAttachments = True
            myFileRelationshipSettings.
                               IncludeChildren = True
            myFileRelationshipSettings.
                                IncludeParents = True
            myFileRelationshipSettings.
                   IncludeRelatedDocumentation = True

            Dim myColOfFileAssocLite As _
            System.Collections.Generic.IEnumerable _
                   (Of ACW.FileAssocLite) = Nothing

            myColOfFileAssocLite =
    connection.FileManager.GetFileAssociationLites _
  (New Long() {oNewFileIteration.EntityIterationId},
                        myFileRelationshipSettings)

            ' Going to add new FileAssocParam
            ' objects to this list

            ' ArrayList to contain
            ' FileAssocParam objects
            Dim fileAssocParams As ArrayList =
                                New ArrayList

            ' Add FileAssocParam objects to the ArrayList
            ' using values from the collection
            ' of FileAssocLite in the collection
            ' returned from GetFileAssociationLites()
            If Not myColOfFileAssocLite Is Nothing Then
                Dim myFileAssocLite As ACW.FileAssocLite
                '    'Go through each FileAssoLite in the
                '   in the collection of FileAssocLite
                For Each myFileAssocLite In
                               myColOfFileAssocLite
                    ' This is a new FileAssocParam that
                    ' is going to be added to the List
                    ' getting the properties
                    Dim par As ACW.FileAssocParam =
                               New ACW.FileAssocParam()
                    par.CldFileId =
                         myFileAssocLite.CldFileId
                    par.RefId = myFileAssocLite.RefId
                    par.Source = myFileAssocLite.Source
                    par.Typ = myFileAssocLite.Typ
                    par.ExpectedVaultPath =
                   myFileAssocLite.ExpectedVaultPath
                    fileAssocParams.Add(par)
                Next
            End If


            ' Get the folder of the file
            ' we are associating
            Dim myDictionary As IDictionary _
       (Of Long, VDF.Vault.Currency.Entities.Folder)
            myDictionary =
         connection.FolderManager.GetFoldersByIds _
        (New Long() {myFolderIdOfNewFileIteration})

            ' Get the folder from the dictionary
            Dim keyPair As Generic.KeyValuePair _
     (Of Long, VDF.Vault.Currency.Entities.Folder)

            keyPair = myDictionary.First
            Dim myFldr As _
            VDF.Vault.Currency.Entities.Folder _
                                  = keyPair.Value

            ' Add the new association



            Dim newFileAssocPar As ACW.FileAssocParam = New ACW.FileAssocParam()

            Dim myFileAssocParamArray As ACW.FileAssocParam()

            Dim fileAssocParams1 As ArrayList =
                                New ArrayList

            For Each myfileiterationid As Long In myFileIterationIds


                newFileAssocPar.CldFileId =
                            myfileiterationid
                newFileAssocPar.RefId = Nothing
                newFileAssocPar.Source = Nothing
                newFileAssocPar.Typ =
                        ACW.AssociationType.Dependency
                newFileAssocPar.ExpectedVaultPath =
                                myFldr.FolderPath



                ' Add our new FileAssocParam
                fileAssocParams1.Add(newFileAssocPar)
                newFileAssocPar = New ACW.FileAssocParam()

            Next


            ' Populate this with the FileAssocParam
            ' objects that we create from properties'
            ' in the FileAssocArray and the new file
            myFileAssocParamArray = DirectCast(fileAssocParams1.ToArray _
          (GetType(ACW.FileAssocParam)), ACW.FileAssocParam())

            ' Use the overloaded method of CheckInFile
            ' that takes a stream This will allow
            ' checking in a file that has not been
            ' downloaded
            ' (by passing in a stream that is Nothing)

            Dim tbom As Autodesk.Connectivity.WebServices.BOM = m_conn.WebServiceManager.DocumentService.GetBOMByFileId(oFileIteration.EntityIterationId)


            Dim myStream As System.IO.Stream = Nothing
            connection.FileManager.CheckinFile _
            (oNewFileIteration, "Attachments added", False,
                                       Date.Now,
                          myFileAssocParamArray,
                          Nothing, False, Nothing,
                      ACW.FileClassification.None,
                                  False, myStream)

        Else
            MessageBox.Show("Unable to check out")
            Return False
        End If

        Return True

    End Function

    Private Function has316file(ByVal folder As String, fName As String) As ACW.File
        'this function tries to find existing file from existing folder and returns True is found
        Dim oFile As ACW.File = Nothing
        Try
            Dim filePropDefs As PropDef() =
            m_conn.WebServiceManager.PropertyService.GetPropertyDefinitionsByEntityClassId("FILE")
            Dim FolderPropDef As PropDef = filePropDefs.[Single](Function(n) n.SysName = "FolderPath")
            Dim fNamePropDef As PropDef = filePropDefs.[Single](Function(n) n.SysName = "ClientFileName")
            Dim oFolderSC As New SrchCond() With {
                .PropDefId = FolderPropDef.Id,
                .PropTyp = PropertySearchType.SingleProperty,
                .SrchOper = 3,
                .SrchRule = SearchRuleType.Must,
                .SrchTxt = folder
            }
            Dim ofNameSC As New SrchCond() With {
                .PropDefId = fNamePropDef.Id,
                .PropTyp = PropertySearchType.SingleProperty,
                .SrchOper = 3,
                .SrchRule = SearchRuleType.Must,
                .SrchTxt = fName
            }
            Dim bookmark As String = String.Empty
            Dim status As SrchStatus = Nothing

            Dim results As ACW.File() =
            m_conn.WebServiceManager.DocumentService.FindFilesBySearchConditions(
           New SrchCond() {oFolderSC, ofNameSC},
           Nothing, Nothing, False, True, bookmark,
              status)
            If results IsNot Nothing Then
                Dim result As ACW.File = results.First
                oFile = result


            End If



        Catch ex As Exception
            oFile = Nothing
        End Try


        Return oFile
    End Function





End Class
