﻿Imports Autodesk.Connectivity.WebServices
Imports Autodesk.Connectivity.WebServicesTools
Imports ACW = Autodesk.Connectivity.WebServices
Imports VDF = Autodesk.DataManagement.Client.Framework
Imports VDFVF = Autodesk.DataManagement.Client.Framework.Vault.Forms
Imports VDFVCP = Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties
Imports Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections
Imports Autodesk.Connectivity.Explorer.ExtensibilityTools
Imports Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities

Public Class sk_copyItemI
    Private m_conn As Connection = SKCopy2Project.SKCopy2ProjectCommandExtension.m_conn
    Private SKnumberingcheme As Integer
    Private projectfolderName As String
    Private projectfolderID As Long
    Private rowIndex As Integer = 0
    Private Sub sk_copyItemI_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            Dim settings As New VDF.Vault.Forms.Settings.LoginSettings
            settings.AutoLoginMode = VDFVF.Settings.LoginSettings.AutoLoginModeValues.RestoreAndExecute
            '   Dim ee As CommandItemEventArgs = SKCopy2Project.SKCopy2ProjectCommandExtension.ee
            '  m_conn = ee.Context.Application.Connection
            '  m_conn = VDF.Vault.Forms.Library.Login(settings)
            If m_conn Is Nothing Then
                MessageBox.Show("Invalid login")
                Exit Sub
            End If
            'let's get correct numberinscheme

            Dim mgr As WebServiceManager = m_conn.WebServiceManager
            Dim entityClassId As String = VDF.Vault.Currency.Entities.EntityClassIds.Files
            Dim fNumchemes As NumSchm() = mgr.NumberingService.GetNumberingSchemes(entityClassId, NumSchmType.ApplicationDefault)

            For Each fNumcheme As ACW.NumSchm In fNumchemes
                If fNumcheme.Name = "SK-Custom-Code" Then
                    SKnumberingcheme = fNumcheme.SchmID
                End If
            Next


        Catch ex As Exception
            MessageBox.Show(ex.Message & " Mek:LoginError")
        End Try




        ' m_conn = Autodesk.DataManagement.Client.Framework.Vault.Services.IVaultConnectionManagerService(getexistingconnection)
        'read folder from registry
        Dim regSK_Projectfolder As String = Reg_R("SK_Projectfolder_item_F", "$/")
        Dim regSK_toShip As String = Reg_R("SK_toShip_F", "")
        Dim regSK_ERP_PROJECT As String = Reg_R("SK_ERP_PROJECT_F", "")
        Dim regSK_PROJECT_NAME As String = Reg_R("SK_PROJECT_NAME_F", "")
        m_sourcePathTextBox.Text = regSK_Projectfolder
        TextBoxShipTo.Text = regSK_toShip
        TextBoxErpProject.Text = regSK_ERP_PROJECT
        TextBoxProjectName.Text = regSK_PROJECT_NAME

        Try
            If DataGridView1.Rows.Count > 0 Then
                Button4.Enabled = True
            End If
        Catch ex As Exception

        End Try




        If regSK_Projectfolder IsNot Nothing Then
            'lets get the folder id for project
            Dim projectFolder As VDF.Vault.Currency.Entities.Folder = findfolder(m_sourcePathTextBox.Text)

            Me.Text = "SK Copy Item, version " & Me.GetType.Assembly.GetName.Version.Major & "." & Me.GetType.Assembly.GetName.Version.Minor



        End If
    End Sub

    Private Sub Button2_Click(sender As Object, e As EventArgs) Handles Button2.Click
        Close()
    End Sub
    Private Sub DataGridView1_CellMouseUp_1(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) Handles DataGridView1.CellMouseUp
        If e.Button = MouseButtons.Right Then
            Me.DataGridView1.Rows(e.RowIndex).Selected = True
            Me.rowIndex = e.RowIndex
            Me.DataGridView1.CurrentCell = Me.DataGridView1.Rows(e.RowIndex).Cells(3)
            Me.ContextMenuStrip1.Show(Me.DataGridView1, e.Location)
            ContextMenuStrip1.Show(Cursor.Position)
        End If
    End Sub

    Private Sub ContextMenuStrip1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ContextMenuStrip1.Click
        If Not Me.DataGridView1.Rows(Me.rowIndex).IsNewRow Then
            Me.DataGridView1.Rows.RemoveAt(Me.rowIndex)
        End If
    End Sub
    Private Sub Button3_Click(sender As Object, e As EventArgs) Handles Button3.Click 'copy
        'creating copies
        Me.Cursor = Cursors.WaitCursor
        Button3.Enabled = False
        Try ' main
            'get the gridrow
            Try
                Reg_W("SK_Projectfolder_item_F", m_sourcePathTextBox.Text)
                Reg_W("SK_toShip_F", TextBoxShipTo.Text)
                Reg_W("SK_ERP_PROJECT_F", TextBoxErpProject.Text)
                Reg_W("SK_PROJECT_NAME_F", TextBoxProjectName.Text)

                Try


                    Dim projectFolder As VDF.Vault.Currency.Entities.Folder = findfolder(m_sourcePathTextBox.Text)
                    projectfolderID = projectFolder.Id
                    projectfolderName = m_sourcePathTextBox.Text
                    If projectfolderName.Split("/").Last.ToLower <> "furniture" Then
                        MessageBox.Show("Project Folder is not Furniture")
                        Exit Sub
                    End If
                    If TextBoxShipTo.Text = "" Then
                        MessageBox.Show("Check the Ship To Field")
                        Exit Sub
                    End If
                Catch ex As Exception
                    MessageBox.Show("Check the Project folder")
                    Exit Sub
                End Try
                If TextBoxShipTo.Text IsNot Nothing Then ' first if
                    Dim shipcodefrom As String = TextBoxShipFom.Text
                    Dim shipcodeto As String = TextBoxShipTo.Text


                    'traverse datagrid
                    For i As Integer = 0 To DataGridView1.Rows.Count - 1
                        Dim fileExists As Boolean = False
                        Dim ParentExists As Boolean = False
                        Dim BomRowExists As Boolean = False
                        Dim itemexists As Boolean = False
                        If DataGridView1.Rows(i).Cells(13).Value Like "Target Item Exists*" Then
                            itemexists = True
                        End If

                        Try
                            'do we have a new file already

                            If (DataGridView1.Rows(i).Cells(13).Value) <> "" Then
                                Dim exists As String() = (DataGridView1.Rows(i).Cells(13).Value).split("|")
                                For ii As Integer = 0 To exists.Length - 1
                                    If exists(ii).ToLower = "target file exists" Then
                                        fileExists = True
                                    End If
                                Next
                            End If
                            If (DataGridView1.Rows(i).Cells(4).Value) <> "" And DataGridView1.Rows(i).Cells(4).Value.contains("BOM") = False Then
                                ParentExists = True
                            End If

                            If (DataGridView1.Rows(i).Cells(4).Value) Like "BOM*" Then
                                BomRowExists = True
                            End If




                            If fileExists = False And ParentExists = False And DataGridView1.Rows(i).Cells(7).Value.contains(TextBoxShipFom.Text) Then ' copy file
                                Dim projectFolder As VDF.Vault.Currency.Entities.Folder = findfolder(m_sourcePathTextBox.Text)
                                Dim projectfolderID As Long = projectFolder.Id
                                Dim projectfolderName As String = projectFolder.FullName
                                Dim oMasterId As Long = DataGridView1.Rows(i).Cells(5).Value.ToString
                                Dim projectNewFolder As String = DataGridView1.Rows(i).Cells(11).Value.ToString
                                Dim state As String = DataGridView1.Rows(i).Cells(8).Value.ToString
                                Dim returnValue As String = SK_CopyItemF_Module.CopyFile(oMasterId, projectfolderID, projectfolderName, projectNewFolder, TextBoxShipFom.Text, TextBoxShipTo.Text, state, TextBoxErpProject.Text, TextBoxProjectName.Text)
                                DataGridView1.Rows(i).Cells(13).Value = "Target File " + returnValue
                                Me.Refresh()

                            End If
                        Catch ex As Exception

                        End Try 'file copy ends.
                        'then create new item if not exist
                        ''Dim itemExists As Boolean = False
                        Try



                            'add files to promote
                            Dim fileName As String = Nothing
                            Dim newFileName As String = Nothing

                            Dim Newitem As Item = Nothing
                            Dim fileId As Long = Nothing
                            Try
                                'first get id of new model file

                                If DataGridView1.Rows(i).Cells(7).Value.ToString <> "" Then
                                    fileName = DataGridView1.Rows(i).Cells(7).Value.ToString
                                    newFileName = fileName.Replace(shipcodefrom, shipcodeto)

                                    If newFileName IsNot Nothing And newFileName.Contains(shipcodeto) Then
                                        fileId = getFileID(newFileName, m_conn.WebServiceManager)
                                    End If

                                    If fileId <> Nothing Then
                                        Dim itemSvc As ItemService = m_conn.WebServiceManager.ItemService
                                        Dim fileids As Long() = fileId.ToSingleArray
                                        itemSvc.AddFilesToPromote(fileids, ItemAssignAll.Default, True)
                                        Try


                                            Dim timestamp As DateTime
                                            Dim promoteOrder As GetPromoteOrderResults = itemSvc.GetPromoteComponentOrder(timestamp)
                                            itemSvc.PromoteComponents(timestamp, promoteOrder.PrimaryArray)
                                            Dim itemsAndFiles As ItemsAndFiles = itemSvc.GetPromoteComponentsResults(timestamp)
                                            Dim items As Item() = Nothing
                                            items = itemsAndFiles.ItemRevArray
                                            itemSvc.UpdateAndCommitItems(items)
                                        Catch ex As Exception

                                            '    ''cannot promote!
                                            ''If ex.Message = "1362" Or ex.Message = "1306" Then
                                            ''    Err.Clear()
                                            ''    Try
                                            ''        'add new item and add file to it
                                            ''        Dim itemName As String = Nothing
                                            ''        Dim newitemName As String = Nothing
                                            ''        Dim item As ACW.Item = Nothing
                                            ''        itemName = DataGridView1.Rows(i).Cells(2).Value.ToString
                                            ''        newitemName = itemName.Replace(shipcodefrom, shipcodeto)
                                            ''        item = m_conn.WebServiceManager.ItemService.GetLatestItemByItemNumber(itemName)
                                            ''        Dim catId As Long = item.Cat.CatId
                                            ''        Dim title As String = item.Title
                                            ''        Dim desc As String = item.Detail
                                            ''        ' Dim itemSvc As ItemService = m_conn.WebServiceManager.ItemService
                                            ''        Newitem = itemSvc.AddItemRevision(catId)
                                            ''        Newitem.Title = title
                                            ''        Newitem.Detail = desc
                                            ''        Dim numSchm As ACW.NumSchm = Nothing
                                            ''        Dim entityClassId As String = VDF.Vault.Currency.Entities.EntityClassIds.Items
                                            ''        Dim numSchemes As ACW.NumSchm() = m_conn.WebServiceManager.NumberingService.GetNumberingSchemes(entityClassId, NumSchmType.ApplicationDefault) 'm_conn.WebServiceManager.ItemService.GetNumberingSchemesByType(NumSchmType.Activated)
                                            ''        For Each numSchm In numSchemes
                                            ''            If numSchm.Name = "Mapped" Then
                                            ''                Exit For
                                            ''            End If
                                            ''        Next
                                            ''        ' Dim restrictionbs As ACW.ProductRestric()
                                            ''        Dim fieldInputs As StringArray() = New StringArray(0) {}
                                            ''        Dim sarray As StringArray = New StringArray()
                                            ''        sarray.Items = New String() {newitemName}
                                            ''        fieldInputs(0) = sarray
                                            ''        Dim restrictions As ProductRestric()

                                            ''        Dim itemNum As ItemNum() = itemSvc.AddItemNumbers(New Long() {Newitem.MasterId}, New Long() {numSchm.SchmID}, fieldInputs, restrictions)
                                            ''        Newitem.ItemNum = itemNum(0).ItemNum1
                                            ''        itemSvc.UpdateAndCommitItems(New Item() {Newitem})





                                            ''    Catch ex

                                            ''    End Try
                                            ''End If


                                        End Try

                                    End If
                                ElseIf ParentExists = True Then 'add new item  bom row


                                    Dim parentitem As ACW.Item = Nothing
                                    Dim parentitemName As String = DataGridView1.Rows(i).Cells(2).Value.ToString
                                    Dim parentnewitemName As String = parentitemName.Replace(shipcodefrom, shipcodeto)
                                    'get resent item as bomrow
                                    Dim ChilditemName As String = Nothing
                                    Dim ChildnewitemName As String = Nothing
                                    Dim Childitem As ACW.Item = Nothing
                                    ChilditemName = DataGridView1.Rows(i).Cells(4).Value.ToString
                                    ChildnewitemName = ChilditemName.Replace(shipcodefrom, shipcodeto)

                                    Childitem = m_conn.WebServiceManager.ItemService.GetLatestItemByItemNumber(ChildnewitemName)

                                    Try 'get existing item

                                        parentitem = m_conn.WebServiceManager.ItemService.GetLatestItemByItemNumber(parentnewitemName)
                                    Catch ex As Exception
                                        'not exist
                                    End Try
                                    Try
                                        If parentitem Is Nothing Then
                                            'create new Item
                                            'get category from child
                                            Dim catId As Long = Childitem.Cat.CatId

                                            'title from child
                                            Dim title As String = Childitem.Title
                                            Dim desc As String = Childitem.Detail
                                            Dim itemSvc As ItemService = m_conn.WebServiceManager.ItemService
                                            Dim item As Item = itemSvc.AddItemRevision(catId)
                                            item.Title = title
                                            item.Detail = desc
                                            Dim numSchm As ACW.NumSchm = Nothing
                                            Dim entityClassId As String = VDF.Vault.Currency.Entities.EntityClassIds.Items
                                            Dim numSchemes As ACW.NumSchm() = m_conn.WebServiceManager.NumberingService.GetNumberingSchemes(entityClassId, NumSchmType.ApplicationDefault) 'm_conn.WebServiceManager.ItemService.GetNumberingSchemesByType(NumSchmType.Activated)
                                            For Each numSchm In numSchemes
                                                If numSchm.Name = "Mapped" Then
                                                    Exit For
                                                End If
                                            Next
                                            Dim restrictionbs As ACW.ProductRestric()
                                            Dim fieldInputs As StringArray() = New StringArray(0) {}
                                            Dim sarray As StringArray = New StringArray()
                                            sarray.Items = New String() {parentnewitemName}
                                            fieldInputs(0) = sarray
                                            Dim restrictions As ProductRestric()

                                            Dim itemNum As ItemNum() = itemSvc.AddItemNumbers(New Long() {item.MasterId}, New Long() {numSchm.SchmID}, fieldInputs, restrictions)
                                            item.ItemNum = itemNum(0).ItemNum1
                                            itemSvc.UpdateAndCommitItems(New Item() {item})

                                            'itemSvc.UpdateAndCommitItems(New Item() {item})
                                            ' m_conn.WebServiceManager.ItemService.UpdateAndCommitItems(New Item() {item})
                                            parentitem = m_conn.WebServiceManager.ItemService.GetLatestItemByItemNumber(parentnewitemName)


                                        End If
                                    Catch ex As Exception

                                    End Try
                                    Try 'try to add bom row
                                        If parentitem IsNot Nothing And Childitem IsNot Nothing Then
                                            'edit parent
                                            Dim editItem As ACW.Item = m_conn.WebServiceManager.ItemService.EditItems({parentitem.RevId}).First()
                                            Dim Bom As ItemBOM = m_conn.WebServiceManager.ItemService.GetItemBOMByItemIdAndDate(editItem.Id, Now, BOMTyp.Tip, BOMViewEditOptions.OmitParents)
                                            Dim isInBom As Boolean = False
                                            Dim itemSvc As ItemService = m_conn.WebServiceManager.ItemService
                                            If Bom.ItemAssocArray IsNot Nothing Then


                                                For Each itm As ItemAssoc In Bom.ItemAssocArray
                                                    If itm.CldItemMasterID = Childitem.MasterId Then
                                                        isInBom = True
                                                        Exit For

                                                    End If
                                                Next
                                            End If
                                            If isInBom = False Then
                                                skAddBomRow.AddBOMRow_WB(editItem, Childitem)
                                            End If
                                            itemSvc.UpdateAndCommitItems(editItem.ToSingleArray)
                                        End If
                                    Catch ex As Exception

                                    End Try
                                End If
                                If BomRowExists = True Then
                                    'add child row to parent item
                                    Try
                                        Dim parentitem As ACW.Item = Nothing
                                        Dim parentitemName As String = Nothing
                                        Dim parentnewitemName As String = Nothing
                                        parentitemName = DataGridView1.Rows(i).Cells(4).Value.ToString.Replace("BOM Row in: ", "")
                                        parentnewitemName = parentitemName.Replace(shipcodefrom, shipcodeto)
                                        Dim childitem As ACW.Item = Nothing
                                        Dim childitemName As String = Nothing
                                        Dim childnewitemName As String = Nothing
                                        childitemName = DataGridView1.Rows(i).Cells(2).Value.ToString
                                        childnewitemName = childitemName.Replace(shipcodefrom, shipcodeto)
                                        childitem = m_conn.WebServiceManager.ItemService.GetLatestItemByItemNumber(childnewitemName)
                                        parentitem = m_conn.WebServiceManager.ItemService.GetLatestItemByItemNumber(parentnewitemName)
                                        Try 'try to add bom row
                                            If parentitem IsNot Nothing And childitem IsNot Nothing Then
                                                'edit parent
                                                Dim editItem As ACW.Item = m_conn.WebServiceManager.ItemService.EditItems({parentitem.RevId}).First()
                                                Dim Bom As ItemBOM = m_conn.WebServiceManager.ItemService.GetItemBOMByItemIdAndDate(editItem.Id, Now, BOMTyp.Tip, BOMViewEditOptions.OmitParents)
                                                Dim isInBom As Boolean = False
                                                Dim itemSvc As ItemService = m_conn.WebServiceManager.ItemService
                                                If Bom.ItemAssocArray IsNot Nothing Then


                                                    For Each itm As ItemAssoc In Bom.ItemAssocArray
                                                        If itm.CldItemMasterID = childitem.MasterId Then
                                                            isInBom = True
                                                            Exit For

                                                        End If
                                                    Next
                                                End If
                                                If isInBom = False Then
                                                    skAddBomRow.AddBOMRow_WB(editItem, childitem)
                                                End If
                                                itemSvc.UpdateAndCommitItems(editItem.ToSingleArray)
                                            End If
                                        Catch ex As Exception

                                        End Try



                                    Catch ex As Exception

                                    End Try



                                End If



                            Catch ex As Exception

                            End Try
                            'edit item and update SEED and SEED_STATE 'if newItem <> nothing then add fileid to item

                            If DataGridView1.Rows(i).Cells(2).Value.ToString.Contains(shipcodefrom) = True Then




                                Try
                                    Dim itemName As String = Nothing
                                    Dim newitemName As String = Nothing
                                    Dim itemstate As String = DataGridView1.Rows(i).Cells(3).Value.ToString
                                    itemName = DataGridView1.Rows(i).Cells(2).Value.ToString
                                    newitemName = itemName.Replace(shipcodefrom, shipcodeto)
                                    Dim newTitle As String = DataGridView1.Rows(i).Cells(14).Value.ToString

                                    Dim itm As ACW.Item = m_conn.WebServiceManager.ItemService.GetLatestItemByItemNumber(newitemName)

                                    Dim editItem As ACW.Item = m_conn.WebServiceManager.ItemService.EditItems({itm.RevId}).First()
                                    ''If Newitem IsNot Nothing Then
                                    ''    'add file to item
                                    ''    m_conn.WebServiceManager.ItemService.AssignComponentToItem(editItem.RevId, fileId)
                                    ''End If


                                    'get values from existing properties

                                    Dim AREA As String = getpropertyByitemNumber(itemName, "AREA")
                                    Dim AREA_NAME As String = getpropertyByitemNumber(itemName, "AREA_NAME")
                                    Dim CUST_APPR_WEEK As String = ""
                                    Dim CUST_APPR_YEAR As String = ""
                                    If itemexists Then
                                        CUST_APPR_WEEK = getpropertyByitemNumber(newitemName, "CUST_APPR_WEEK")
                                        CUST_APPR_YEAR = getpropertyByitemNumber(newitemName, "CUST_APPR_YEAR")
                                    End If

                                    Dim DECK As String = getpropertyByitemNumber(itemName, "DECK")
                                    Dim FIREZONE As String = getpropertyByitemNumber(itemName, "FIREZONE")
                                    Dim FI_Designer As String = getpropertyByitemNumber(itemName, "FI_Designer")
                                    Dim MANUFACTURER As String = getpropertyByitemNumber(itemName, "MANUFACTURER")
                                    Dim SHIP_SERIE As String = getpropertyByitemNumber(itemName, "SHIP_SERIE")
                                    Dim YARD As String = getpropertyByitemNumber(itemName, "YARD")
                                    Dim ERP_PROJECT As String = TextBoxErpProject.Text
                                    Dim SKP_DRW_DEMAND_YEAR As String = ""
                                    Dim SKP_DRW_DEMAND_WEEK As String = ""
                                    Dim SKP_PROD_DOC_DEMAND_YEAR As String = ""
                                    Dim SKP_PROD_DOC_DEMAND_WEEK As String = ""
                                    Dim SKP_DES_NAME As String = ""
                                    Dim SKP_MUST_BE_READY As String = ""

                                    Dim oPropefs As ACW.PropDef() = m_conn.WebServiceManager.PropertyService.GetPropertyDefinitionsByEntityClassId("ITEM")
                                    Dim propAndVals As New Dictionary(Of Autodesk.Connectivity.WebServices.PropDef, Object)
                                    Dim propInstParams As List(Of PropInstParam) = New List(Of PropInstParam)()
                                    For Each oPropdef As ACW.PropDef In oPropefs
                                        If oPropdef.DispName = "SEED_STATE" Then
                                            Dim propInstPar As PropInstParam = New PropInstParam()
                                            propInstPar.PropDefId = oPropdef.Id
                                            propInstPar.Val = itemstate
                                            propInstParams.Add(propInstPar)
                                        ElseIf oPropdef.DispName = "SEED" Then
                                            Dim propInstPar As PropInstParam = New PropInstParam()
                                            propInstPar.PropDefId = oPropdef.Id
                                            propInstPar.Val = itemName
                                            propInstParams.Add(propInstPar)
                                        ElseIf oPropdef.DispName = "SHIP" And ParentExists = True Then
                                            Dim propInstPar As PropInstParam = New PropInstParam()
                                            propInstPar.PropDefId = oPropdef.Id
                                            propInstPar.Val = TextBoxShipTo.Text.ToString
                                            propInstParams.Add(propInstPar)

                                        ElseIf oPropdef.DispName = "SK_ITEM" Then
                                            Dim propInstPar As PropInstParam = New PropInstParam()
                                            propInstPar.PropDefId = oPropdef.Id
                                            propInstPar.Val = newitemName.Replace((TextBoxShipTo.Text.ToString & "-"), "")
                                            propInstParams.Add(propInstPar)
                                        ElseIf oPropdef.DispName = "Title" Then
                                            Dim propInstPar As PropInstParam = New PropInstParam()
                                            propInstPar.PropDefId = oPropdef.Id
                                            propInstPar.Val = newTitle
                                            propInstParams.Add(propInstPar)
                                        ElseIf oPropdef.DispName = "AREA" Then
                                            Dim propInstPar As PropInstParam = New PropInstParam()
                                            propInstPar.PropDefId = oPropdef.Id
                                            propInstPar.Val = AREA
                                            propInstParams.Add(propInstPar)
                                        ElseIf oPropdef.DispName = "AREA_NAME" Then
                                            Dim propInstPar As PropInstParam = New PropInstParam()
                                            propInstPar.PropDefId = oPropdef.Id
                                            propInstPar.Val = AREA_NAME
                                            propInstParams.Add(propInstPar)
                                        ElseIf oPropdef.DispName = "CUST_APPR_WEEK" Then
                                            Dim propInstPar As PropInstParam = New PropInstParam()
                                            propInstPar.PropDefId = oPropdef.Id
                                            propInstPar.Val = CUST_APPR_WEEK
                                            propInstParams.Add(propInstPar)
                                        ElseIf oPropdef.DispName = "CUST_APPR_YEAR" Then
                                            Dim propInstPar As PropInstParam = New PropInstParam()
                                            propInstPar.PropDefId = oPropdef.Id
                                            propInstPar.Val = CUST_APPR_YEAR
                                            propInstParams.Add(propInstPar)
                                        ElseIf oPropdef.DispName = "DECK" Then
                                            Dim propInstPar As PropInstParam = New PropInstParam()
                                            propInstPar.PropDefId = oPropdef.Id
                                            propInstPar.Val = DECK
                                            propInstParams.Add(propInstPar)
                                        ElseIf oPropdef.DispName = "FIREZONE" Then
                                            Dim propInstPar As PropInstParam = New PropInstParam()
                                            propInstPar.PropDefId = oPropdef.Id
                                            propInstPar.Val = FIREZONE
                                            propInstParams.Add(propInstPar)
                                        ElseIf oPropdef.DispName = "FI_Designer" Then
                                            Dim propInstPar As PropInstParam = New PropInstParam()
                                            propInstPar.PropDefId = oPropdef.Id
                                            propInstPar.Val = FI_Designer
                                            propInstParams.Add(propInstPar)
                                        ElseIf oPropdef.DispName = "MANUFACTURER" Then
                                            Dim propInstPar As PropInstParam = New PropInstParam()
                                            propInstPar.PropDefId = oPropdef.Id
                                            propInstPar.Val = MANUFACTURER
                                            propInstParams.Add(propInstPar)
                                        ElseIf oPropdef.DispName = "SHIP_SERIE" Then
                                            Dim propInstPar As PropInstParam = New PropInstParam()
                                            propInstPar.PropDefId = oPropdef.Id
                                            propInstPar.Val = SHIP_SERIE
                                            propInstParams.Add(propInstPar)
                                        ElseIf oPropdef.DispName = "YARD" Then
                                            Dim propInstPar As PropInstParam = New PropInstParam()
                                            propInstPar.PropDefId = oPropdef.Id
                                            propInstPar.Val = YARD
                                            propInstParams.Add(propInstPar)
                                        ElseIf oPropdef.DispName = "ERP_PROJECT" Then
                                            Dim propInstPar As PropInstParam = New PropInstParam()
                                            propInstPar.PropDefId = oPropdef.Id
                                            propInstPar.Val = ERP_PROJECT
                                            propInstParams.Add(propInstPar)
                                        ElseIf oPropdef.DispName = "SKP_DRW_DEMAND_YEAR" Then
                                            Dim propInstPar As PropInstParam = New PropInstParam()
                                            propInstPar.PropDefId = oPropdef.Id
                                            propInstPar.Val = SKP_DRW_DEMAND_YEAR
                                            propInstParams.Add(propInstPar)
                                        ElseIf oPropdef.DispName = "SKP_DRW_DEMAND_WEEK" Then
                                            Dim propInstPar As PropInstParam = New PropInstParam()
                                            propInstPar.PropDefId = oPropdef.Id
                                            propInstPar.Val = SKP_DRW_DEMAND_WEEK
                                            propInstParams.Add(propInstPar)
                                        ElseIf oPropdef.DispName = "SKP_PROD_DOC_DEMAND_YEAR" Then
                                            Dim propInstPar As PropInstParam = New PropInstParam()
                                            propInstPar.PropDefId = oPropdef.Id
                                            propInstPar.Val = SKP_PROD_DOC_DEMAND_YEAR
                                            propInstParams.Add(propInstPar)
                                        ElseIf oPropdef.DispName = "SKP_PROD_DOC_DEMAND_WEEK" Then
                                            Dim propInstPar As PropInstParam = New PropInstParam()
                                            propInstPar.PropDefId = oPropdef.Id
                                            propInstPar.Val = SKP_PROD_DOC_DEMAND_WEEK
                                            propInstParams.Add(propInstPar)
                                        ElseIf oPropdef.DispName = "SKP_DES_NAME" Then
                                            Dim propInstPar As PropInstParam = New PropInstParam()
                                            propInstPar.PropDefId = oPropdef.Id
                                            propInstPar.Val = SKP_DES_NAME
                                            propInstParams.Add(propInstPar)
                                        ElseIf oPropdef.DispName = "SKP_MUST_BE_READY" Then
                                            Dim propInstPar As PropInstParam = New PropInstParam()
                                            propInstPar.PropDefId = oPropdef.Id
                                            propInstPar.Val = SKP_MUST_BE_READY
                                            propInstParams.Add(propInstPar)


                                        End If
                                    Next

                                    Dim propInstParamsArray As PropInstParamArray = New PropInstParamArray()
                                    propInstParamsArray.Items = propInstParams.ToArray()
                                    Dim propInstParamsArrays As PropInstParamArray() = New PropInstParamArray(0) {}
                                    propInstParamsArrays(0) = propInstParamsArray
                                    Dim items As ACW.Item() = m_conn.WebServiceManager.ItemService.UpdateItemProperties(editItem.RevId.ToSingleArray, propInstParamsArrays)
                                    items(0).Title = newTitle
                                    m_conn.WebServiceManager.ItemService.UpdateAndCommitItems(items)

                                    If DataGridView1.Rows(i).Cells(13).Value = "" Then
                                        DataGridView1.Rows(i).Cells(13).Value = "Item Promoted"
                                    Else
                                        DataGridView1.Rows(i).Cells(13).Value = DataGridView1.Rows(i).Cells(13).Value & "|" & "Item Promoted"
                                    End If

                                    If BomRowExists = True Then

                                        'try to copy new item or add existing item.
                                        Try
                                            'if item num  contains ship code
                                            If DataGridView1.Rows(i).Cells(2).Value.ToString.Contains(shipcodefrom) = True Then
                                                Dim parentitem As ACW.Item = Nothing
                                                Dim parentitemName As String = DataGridView1.Rows(i).Cells(2).Value.ToString
                                                Dim parentnewitemName As String = parentitemName.Replace(shipcodefrom, shipcodeto)

                                            End If

                                        Catch ex As Exception

                                        End Try
                                    End If

                                Catch ex As Exception

                                End Try
                            End If
                            '  Else
                            'link possible copied files to existing item 


                            'End If


                        Catch ex As Exception

                        End Try





                    Next 'gridviewrow

                End If 'first if
            Catch ex As Exception

            End Try
        Catch ex As Exception

        End Try ' main
        Me.Cursor = Cursors.Default
        Button3.Enabled = True
    End Sub

    Private Sub Button4_Click(sender As Object, e As EventArgs) Handles Button4.Click 'precheck
        'go through Items if exist and files if exists
        Me.Cursor = Cursors.WaitCursor
        Try
            Reg_W("SK_Projectfolder_item_F", m_sourcePathTextBox.Text)
            Reg_W("SK_toShip_F", TextBoxShipTo.Text)
            Reg_W("SK_ERP_PROJECT_F", TextBoxErpProject.Text)
            Reg_W("SK_PROJECT_NAME_F", TextBoxProjectName.Text)

            Try


                Dim projectFolder As VDF.Vault.Currency.Entities.Folder = findfolder(m_sourcePathTextBox.Text)
                projectfolderID = projectFolder.Id
                projectfolderName = m_sourcePathTextBox.Text
                If projectfolderName.Split("/").Last.ToLower <> "furniture" Then
                    MessageBox.Show("Project Folder is not Furniture")
                    Exit Sub
                End If
                If TextBoxShipTo.Text = "" Then
                    MessageBox.Show("Check the Ship To Field")
                    Exit Sub
                End If
            Catch ex As Exception
                MessageBox.Show("Check the Project folder")
                Exit Sub
            End Try



            If TextBoxShipTo.Text IsNot Nothing Then ' first if
                'for each row in datagrid
                Try
                    Dim shipcodefrom As String = TextBoxShipFom.Text
                    Dim shipcodeto As String = TextBoxShipTo.Text

                    For i As Integer = 0 To DataGridView1.Rows.Count - 1

                        Dim itemName As String = DataGridView1.Rows(i).Cells(2).Value.ToString
                        Dim newItemName As String = itemName.Replace(shipcodefrom, shipcodeto)
                        Dim fileName As String = Nothing
                        Dim newFileName As String = Nothing
                        If DataGridView1.Rows(i).Cells(7).Value.ToString <> "" Then
                            fileName = DataGridView1.Rows(i).Cells(7).Value.ToString
                            newFileName = fileName.Replace(shipcodefrom, shipcodeto)
                        Else
                            newFileName = ""
                        End If

                        Dim state As String = DataGridView1.Rows(i).Cells(3).Value.ToString
                        Dim returnValue As String = checkTheSame(newItemName, newFileName, m_conn.WebServiceManager)
                        DataGridView1.Rows(i).Cells(13).Value = returnValue
                        Me.Refresh()
                    Next
                Catch ex As Exception

                End Try


            End If 'final if
        Catch ex As Exception

        End Try

        Button3.Enabled = True
        Me.Cursor = Cursors.Default
    End Sub

    Function getFileID(ByVal NewFileName As String, mgr As WebServiceManager) As Long
        Dim returnVal As Long = Nothing
        Try
            'then find possible file
            If NewFileName <> "" Then
                Dim filePropDefs As PropDef() =
                mgr.PropertyService.GetPropertyDefinitionsByEntityClassId("FILE")
                Dim NamePropdef As PropDef =
                filePropDefs.[Single](Function(n) n.SysName = "ClientFileName")
                Dim Fname As New SrchCond() With {
                .PropDefId = NamePropdef.Id,
       .PropTyp = PropertySearchType.SingleProperty,
       .SrchOper = 3,
       .SrchRule = SearchRuleType.Must,
       .SrchTxt = NewFileName
}
                Dim bookmark As String = String.Empty
                Dim status As SrchStatus = Nothing
                Dim totalResults As New List(Of File)()
                'While status Is Nothing OrElse totalResults.Count < status.TotalHits
                Dim results As File() =
                    mgr.DocumentService.FindFilesBySearchConditions(
          New SrchCond() {Fname},
           Nothing, Nothing, False, True, bookmark,
              status)
                If results IsNot Nothing Then
                    returnVal = results.First.Id
                End If
                'End While




            End If
        Catch ex As Exception

        End Try
        Return returnVal
    End Function
    Function checkTheSame(ByVal newItemName As String, ByVal newFileName As String, mgr As WebServiceManager) As String
        Dim returnval As String = Nothing

        Try
            ' first find item
            Dim selecteditem As ACW.Item = Nothing
            selecteditem = mgr.ItemService.GetLatestItemByItemNumber(newItemName)
            If selecteditem IsNot Nothing Then

                returnval = "Target Item Exists"
            End If
        Catch ex As Exception
        End Try

        Try
            'then find possible file
            If newFileName <> "" Then
                Dim filePropDefs As PropDef() =
                mgr.PropertyService.GetPropertyDefinitionsByEntityClassId("FILE")
                Dim NamePropdef As PropDef =
                filePropDefs.[Single](Function(n) n.SysName = "ClientFileName")
                Dim Fname As New SrchCond() With {
                .PropDefId = NamePropdef.Id,
       .PropTyp = PropertySearchType.SingleProperty,
       .SrchOper = 3,
       .SrchRule = SearchRuleType.Must,
       .SrchTxt = newFileName
}
                Dim bookmark As String = String.Empty
                Dim status As SrchStatus = Nothing
                Dim totalResults As New List(Of File)()
                While status Is Nothing OrElse totalResults.Count < status.TotalHits
                    Dim results As File() =
                    mgr.DocumentService.FindFilesBySearchConditions(
          New SrchCond() {Fname},
           Nothing, Nothing, False, True, bookmark,
              status)
                    If results IsNot Nothing Then
                        totalResults.AddRange(results)
                        If returnval = Nothing Then
                            returnval = "Target File Exists"
                        Else

                            returnval = returnval + "|" + "Target File Exists"

                        End If
                    Else
                        Exit While
                    End If
                End While




            End If
        Catch ex As Exception

        End Try

        Return returnval
    End Function
    Public Class FolderFilter
        Inherits VDFVF.Settings.SelectEntitySettings.EntityFilter

        Public Sub New(ByVal displayName As String)
            MyBase.New(displayName)
        End Sub

        Public Overrides Function CanDisplayEntity(ByVal entity As Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity) As Boolean
            Return entity.EntityClass.Id = "FLDR"
        End Function
    End Class
    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        ''Dim settings = New VDFVF.Settings.SelectEntitySettings()
        ''settings.PersistenceKey = "MekSystems.SKCopy2ProjectApp.MyDialog"
        ''settings.MultipleSelect = False
        ''settings.ActionableEntityClassIds.Add(VDF.Vault.Currency.Entities.EntityClassIds.Folder)
        Dim settings = New VDFVF.Settings.SelectEntitySettings()
        ''settings.PersistenceKey = "MekSystems.SKCopy2ProjectApp.MyDialog"
        ''settings.MultipleSelect = False
        '''settings.ActionableEntityClassIds.Add(VDF.Vault.Currency.Entities.EntityClassIds.Folder)
        settings.ShowFolderView = True
        ''settings.SelectionTextLabel = "Folder"
        'settings.ActionableEntityClassIds.Remove(VDF.Vault.Currency.Entities.EntityClassIds.Files)

        settings.ActionButtonEnablementRule = VDFVF.Settings.SelectEntitySettings.ActionButtonEnablementRules.MustExist
        settings.ActionButtonNavigatesContainers = False
        settings.DialogCaption = "Select folder to move into"
        settings.MultipleSelect = False
        settings.ShowHiddenChildren = False
        settings.SelectionTextLabel = "Folder"
        Dim filter As FolderFilter = New FolderFilter("Folders")
        Dim filters As VDFVF.Settings.SelectEntitySettings.EntityFilter() = {filter}
        settings.ConfigureFilters("Folders", filters, filter)
        Dim folderRes As VDFVF.Results.SelectEntityResults = Autodesk.DataManagement.Client.Framework.Vault.Forms.Library.SelectEntity(m_conn, settings)
        Dim mgr As WebServiceManager = m_conn.WebServiceManager

        '   m_conn = SKCopy2Project.SKCopy2ProjectCommandExtension.ee
        ' run the dialog
        'Dim results = VDFVF.Library.SelectEntity(m_conn, settings)

        ' fill the textbox with the selected file path
        If folderRes IsNot Nothing Then
            Dim selected = folderRes.SelectedEntities.SingleOrDefault()
            If selected Is Nothing Then
                Return
            End If
            Dim selectedFolder = TryCast(selected, VDF.Vault.Currency.Entities.Folder)
            If selectedFolder Is Nothing Then
                Return
            End If
            ' m_conn.FileManager.LoadParentFolders(selectedFolder.ToSingleArray())
            m_sourcePathTextBox.Text = selectedFolder.FullName
        End If







        Reg_W("SK_Projectfolder_item_F", m_sourcePathTextBox.Text)
        'lets get the folder id for project
        Dim projectFolder As VDF.Vault.Currency.Entities.Folder = findfolder(m_sourcePathTextBox.Text)
        projectfolderID = projectFolder.Id
        projectfolderName = m_sourcePathTextBox.Text
    End Sub

    Public Function getpropertyByitemNumber(ByVal oNumber As String, ByVal oProperty As String) As String
        Dim oPropValue As String = Nothing
        Try
            Dim item As Item = m_conn.WebServiceManager.ItemService.GetLatestItemByItemNumber(oNumber)
            Dim itemRevision As New ItemRevision(m_conn, item)
            Dim oProps As VDFVCP.PropertyDefinitionDictionary = Nothing
            oProps = m_conn.PropertyManager.GetPropertyDefinitions("ITEM", Nothing, Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionFilter.IncludeAll)
            ' Dim oProprefs As VDFVCP.PropertyDefinition() = oProps.Values

            For Each oPropdef As VDFVCP.PropertyDefinition In oProps.Values
                If oPropdef.DisplayName = oProperty Then
                    oPropValue = m_conn.PropertyManager.GetPropertyValue(itemRevision, oPropdef, Nothing)
                    Exit For
                End If
            Next



        Catch ex As Exception

        End Try
        If oPropValue IsNot Nothing Then
            Return oPropValue
        Else
            Return ""
        End If

    End Function




End Class