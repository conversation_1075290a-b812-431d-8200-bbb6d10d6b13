﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|AnyCPU'">
    <StartAction>Program</StartAction>
    <StartProgram>C:\Program Files\Autodesk\Vault Client 2022\Explorer\Connectivity.VaultPro.exe</StartProgram>
    <StartWorkingDirectory>C:\Program Files\Autodesk\Vault Client 2022\Explorer</StartWorkingDirectory>
  </PropertyGroup>
  <PropertyGroup>
    <ReferencePath>C:\Users\<USER>\OneDrive\Asiakkaat\SeaKing\Project\2022DLLS\</ReferencePath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <StartProgram>C:\Program Files\Autodesk\Vault Client 2022\Explorer\Connectivity.VaultPro.exe</StartProgram>
    <StartWorkingDirectory>C:\Program Files\Autodesk\Vault Client 2022\Explorer</StartWorkingDirectory>
    <StartAction>Program</StartAction>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <StartAction>Program</StartAction>
    <StartProgram>C:\Program Files\Autodesk\Vault Client 2022\Explorer\Connectivity.VaultPro.exe</StartProgram>
    <StartWorkingDirectory>C:\Program Files\Autodesk\Vault Client 2022\Explorer</StartWorkingDirectory>
  </PropertyGroup>
</Project>