﻿Imports Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections
Imports ACW = Autodesk.Connectivity.WebServices
Imports Autodesk.Connectivity.WebServicesTools
Imports VDF = Autodesk.DataManagement.Client.Framework
Imports Autodesk.Connectivity.Explorer.ExtensibilityTools



Module SK_CopyItemF_Module

    Private m_conn As Connection = SKCopy2Project.SKCopy2ProjectCommandExtension.m_conn
    Private rownumb As Integer = 17
    Public masterArr(rownumb, 0) As String
    Public drwArr(,) As String
    Public derivedArr(,) As String
    Private numbofModels As Integer = Nothing
    Private numbofDeriveds As Integer = Nothing
    Private numbofDrawings As Integer = Nothing

    Public Function CopyFile(ByVal oMasterID As Long, ByVal oTargetFolderId As Long,
                             ByVal oTargetFolderFullName As String, ByVal oTargetFolderNewFolders As String,
                             ByVal oldShipCode As String, ByVal newShipCode As String, ByVal oState As String, ByVal erp_project As String, ByVal project_name As String) As String
        Dim mgr As WebServiceManager = m_conn.WebServiceManager
        Dim mArrlength As Integer
        Dim dArrlength As Integer
        Dim fids(,) As Long = Nothing
        Dim fassocArr As ACW.FileAssocArray
        Dim di As Integer = 0

        Dim returnValue As String = Nothing


        Try
            Dim selectedFile As ACW.File = mgr.DocumentService.GetLatestFileByMasterId(oMasterID)

            'check that there is not similar file in Vault
            Try

                Dim newname As String = selectedFile.Name.Replace(oldShipCode, newShipCode)
                Dim docsvc As ACW.DocumentService = Nothing
                docsvc = m_conn.WebServiceManager.DocumentService

                Dim propSvc As ACW.PropertyService = m_conn.WebServiceManager.PropertyService
                Dim props1 As ACW.PropDef() = propSvc.FindPropertyDefinitionsBySystemNames("FILE", New String() {"ClientFileName"})
                Dim srcSort As New Autodesk.Connectivity.WebServices.SrchSort()



                'create bookmark
                Dim bookmark As String = [String].Empty
                'status
                Dim srcStatus As Autodesk.Connectivity.WebServices.SrchStatus = Nothing




                Dim SrcCond1 As New ACW.SrchCond()
                SrcCond1.PropDefId = Convert.ToInt64(props1(0).Id)
                SrcCond1.PropTyp = ACW.PropertySearchType.SingleProperty
                SrcCond1.SrchRule = ACW.SearchRuleType.Must
                SrcCond1.SrchOper = Convert.ToInt32(3)
                SrcCond1.SrchTxt = newname.Trim


                Dim ModelFiles As ACW.File() = docsvc.FindFilesBySearchConditions(New Autodesk.Connectivity.WebServices.SrchCond() {SrcCond1}, New Autodesk.Connectivity.WebServices.SrchSort() {srcSort}, Nothing, True, True, bookmark, srcStatus) '  itemSvc.GetAllLatestItems()

                If ModelFiles IsNot Nothing Then
                    'MessageBox.Show("There is similar file in Vault already: " & newname)
                    returnValue = "File exists"
                    Return returnValue
                    Exit Function
                End If
            Catch ex As Exception

            End Try



            Dim folder As ACW.Folder = m_conn.WebServiceManager.DocumentService.GetFolderById(selectedFile.FolderId)
            If selectedFile.Name.Split(".").Last.ToLower = "iam" Then ' iam
                Try
                    fassocArr = m_conn.WebServiceManager.DocumentService.GetLatestFileAssociationsByMasterIds(selectedFile.MasterId.ToSingleArray, ACW.FileAssociationTypeEnum.None, False, ACW.FileAssociationTypeEnum.Dependency, True, False, False, False).First

                    'create the row 0 to masterarr
                    ReDim Preserve masterArr(rownumb, 0)
                    'Main assy
                    masterArr(0, 0) = folder.Id  'folderid
                    masterArr(1, 0) = folder.FullName 'fullpath
                    masterArr(2, 0) = m_conn.WorkingFoldersManager.GetWorkingFolder(folder.FullName.ToString).ToString
                    masterArr(3, 0) = Split(selectedFile.Name, ".").Last 'ext
                    masterArr(4, 0) = selectedFile.Name 'name
                    masterArr(5, 0) = "PAR_ITEM" 'Par*
                    masterArr(6, 0) = selectedFile.Id ' ID
                    masterArr(7, 0) = "" 'old parentid I think this should move down after new ID is created
                    masterArr(14, 0) = selectedFile.MasterId 'old masterid
                    masterArr(17, 0) = "" 'SK_Item

                Catch ex As Exception
                    MessageBox.Show(ex.Message & " Mek:MasterArr")
                End Try


                'let us generate the New folders, New names for components which will be copied.

                Try
                    masterArr(9, 0) = masterArr(4, 0).Replace(oldShipCode, newShipCode)
                    'project folders
                    Try
                        If oTargetFolderNewFolders = "" Then
                            masterArr(10, 0) = oTargetFolderId
                            masterArr(11, 0) = oTargetFolderFullName
                        Else 'try to create new folders
                            Dim newFolders As String() = oTargetFolderNewFolders.Split("/")
                            For i As Integer = 0 To newFolders.Count - 1
                                Dim newFolder As ACW.Folder = Nothing
                                Try
                                    newFolder = mgr.DocumentService.AddFolder(newFolders(i), oTargetFolderId, False)
                                    oTargetFolderId = newFolder.Id
                                    oTargetFolderFullName = newFolder.FullName
                                Catch ex As Exception
                                    'folder exist
                                    Try
                                        Dim projectFolder As VDF.Vault.Currency.Entities.Folder = findfolder(oTargetFolderFullName & "/" & newFolders(i))
                                        oTargetFolderId = projectFolder.Id
                                        oTargetFolderFullName = projectFolder.FullName
                                    Catch ex

                                    End Try
                                End Try
                                ' If New

                            Next
                            masterArr(10, 0) = oTargetFolderId
                            masterArr(11, 0) = oTargetFolderFullName
                        End If


                    Catch ex As Exception

                    End Try


                Catch ex As Exception

                End Try

                'then drawings
                'let us create drwarr for drawings going through fids(masterid)
                Try

                    fassocArr = m_conn.WebServiceManager.DocumentService.GetLatestFileAssociationsByMasterIds(Int64.Parse(masterArr(14, 0)).ToSingleArray,
                               ACW.FileAssociationTypeEnum.None, False, ACW.FileAssociationTypeEnum.None, False, True, False, False).First
                    If fassocArr.FileAssocs IsNot Nothing Then
                        For Each fassoc In fassocArr.FileAssocs
                            Dim f As ACW.File = fassoc.ParFile

                            'let's first check that parfile name and f name are the same.
                            Dim mfName As String = Split(fassoc.CldFile.Name, ".").First
                            Dim dfname As String = Split(f.Name, ".").First
                            If Not mfName = dfname Then
                                Continue For
                            End If
                            ReDim Preserve drwArr(rownumb, di)
                            drwArr(0, 0) = f.FolderId
                            folder = m_conn.WebServiceManager.DocumentService.GetFolderById(f.FolderId) 'folder info of file
                            drwArr(1, 0) = folder.FullName 'fullpath
                            drwArr(2, 0) = m_conn.WorkingFoldersManager.GetWorkingFolder(folder.FullName.ToString).ToString
                            drwArr(3, 0) = Split(f.Name, ".").Last 'ext
                            drwArr(4, 0) = f.Name 'name
                            drwArr(6, 0) = f.Id ' id
                            drwArr(7, 0) = fassoc.Id 'cld id (model)
                            drwArr(14, 0) = f.MasterId 'old masterid

                            drwArr(17, 0) = ""


                            'new name from model
                            For mi = 0 To mArrlength
                                If fassoc.CldFile.Name = masterArr(4, 0) Then
                                    drwArr(9, 0) = Split(masterArr(9, 0), ".").First & ".dwg"
                                    drwArr(10, 0) = masterArr(10, 0)
                                    drwArr(11, 0) = masterArr(11, 0)
                                    Exit For

                                End If
                            Next


                        Next 'fassoc
                    End If
                Catch ex As Exception
                    MessageBox.Show(ex.Message & " Mek:drwArr")
                End Try



                'copying


                Try
                    Dim i As Integer = 0
                    'first model
                    Try


                        Try
                            Dim downloadTicket As ACW.ByteArray = m_conn.WebServiceManager.DocumentService.GetDownloadTicketsByFileIds(Int64.Parse(masterArr(6, 0)).ToSingleArray).First


                            Dim results As ACW.PropWriteResults = Nothing

                            Dim uploadTicket As Byte()
                            'downloadticket
                            'get bom for the file
                            ' Dim tbom As Autodesk.Connectivity.WebServices.BOM = m_conn.WebServiceManager.DocumentService.GetBOMByFileId(Int64.Parse(masterArr(6, 0)))
                            'get newfilename and newfolderid
                            Dim newName As String = Nothing
                            Dim newFolderID As Long = Nothing
                            Dim copiedfrom As String = Nothing
                            Dim oldMasterid As Long = Nothing
                            Dim erptype As String = "PAR_ITEM"
                            Dim seednumber As String = Nothing
                            Dim seedState As String = oState
                            Dim parfile As ACW.File = m_conn.WebServiceManager.DocumentService.FindFilesByIds(Int64.Parse(masterArr(6, 0)).ToSingleArray).First
                            '
                            newName = masterArr(9, 0)
                            newFolderID = masterArr(10, 0)
                            copiedfrom = masterArr(4, 0)
                            oldMasterid = Int64.Parse(masterArr(14, 0))
                            erptype = masterArr(5, 0)
                            seednumber = copiedfrom
                            seedState = oState

                            Dim fileassocparamslist As New ArrayList
                            Dim fileassocparams As ACW.FileAssocParam() = Nothing
                            Try

                                fassocArr = m_conn.WebServiceManager.DocumentService.GetLatestFileAssociationsByMasterIds(oldMasterid.ToSingleArray, ACW.FileAssociationTypeEnum.None, False, ACW.FileAssociationTypeEnum.Dependency, False, False, False, False).First

                                Dim ii As Integer = 0
                                For Each assoc As ACW.FileAssoc In fassocArr.FileAssocs
                                    'For assocI As Integer = 1 To mArrlength
                                    'If f.MasterId = assoc.CldFile.MasterId Then ' Int64.Parse(masterArr(13, assocI)) Then
                                    Dim param As New ACW.FileAssocParam
                                    Dim param1 As New ACW.FileAssocParam()
                                    param.CldFileId = assoc.CldFile.Id
                                    param1.CldFileId = assoc.CldFile.Id
                                    param.RefId = assoc.RefId
                                    param1.RefId = assoc.RefId
                                    param.Source = assoc.Source
                                    param1.Source = assoc.Source
                                    param.Typ = assoc.Typ
                                    param1.Typ = assoc.Typ

                                    folder = m_conn.WebServiceManager.DocumentService.GetFolderById(assoc.CldFile.FolderId)
                                    param.ExpectedVaultPath = folder.FullName & "/" & assoc.CldFile.Name
                                    param1.ExpectedVaultPath = folder.FullName & "/" & assoc.CldFile.Name
                                    ReDim Preserve fileassocparams(ii)
                                    fileassocparams(ii) = param
                                    fileassocparamslist.Add(param1)
                                    ii += 1
                                Next
                                'newcopy

                            Catch ex As Exception

                            End Try

                            '****************properties******************
                            Dim oPropWriterequests As New ACW.PropWriteRequests
                            Dim propWrites() As ACW.PropWriteReq = Nothing
                            Try
                                Dim fileProps As ACW.CtntSrcPropDef() = m_conn.WebServiceManager.FilestoreService.GetContentSourcePropertyDefinitions(downloadTicket.Bytes, True)
                                Dim propSet = fileProps.Where(Function(n) n.Typ = ACW.DataType.[String])
                                Dim pNumber As String = newName
                                pNumber = Strings.Left(pNumber, pNumber.Length - 4)


                                Dim propsetI As Integer = 0





                                Try
                                    For Each prop As ACW.CtntSrcPropDef In propSet
                                        'Part Number
                                        Dim erpcodeval As String = pNumber.ToUpper
                                        If prop.DispName = "Part Number" Then
                                            Dim propWrite As New ACW.PropWriteReq() With {
                                .CanCreate = False,
                                .Moniker = prop.Moniker,
                                .Val = erpcodeval
                            }
                                            ReDim Preserve propWrites(propsetI)
                                            propWrites(propsetI) = propWrite
                                            propsetI += 1
                                        ElseIf prop.DispName = "ERP_PROJECT" Then
                                            Dim propWrite As New ACW.PropWriteReq() With {
                                                .CanCreate = False,
                                                .Moniker = prop.Moniker,
                                                .Val = erp_project
                                            }
                                            ReDim Preserve propWrites(propsetI)
                                            propWrites(propsetI) = propWrite
                                            propsetI += 1
                                        ElseIf prop.DispName = "PROJECT_NAME" Then
                                            Dim propWrite As New ACW.PropWriteReq() With {
                                                .CanCreate = False,
                                                .Moniker = prop.Moniker,
                                                .Val = project_name
                                            }
                                            ReDim Preserve propWrites(propsetI)
                                            propWrites(propsetI) = propWrite
                                            propsetI += 1
                                        ElseIf prop.DispName = "SHIP" Then
                                            Dim propWrite As New ACW.PropWriteReq() With {
                                                .CanCreate = False,
                                                .Moniker = prop.Moniker,
                                                .Val = newShipCode
                                            }
                                            ReDim Preserve propWrites(propsetI)
                                            propWrites(propsetI) = propWrite
                                            propsetI += 1
                                        ElseIf prop.DispName = "SEED" Then
                                            Dim propWrite As New ACW.PropWriteReq() With {
                                                .CanCreate = False,
                                                .Moniker = prop.Moniker,
                                                .Val = copiedfrom
                                            }
                                            ReDim Preserve propWrites(propsetI)
                                            propWrites(propsetI) = propWrite
                                            propsetI += 1

                                        End If
                                    Next 'prop
                                Catch ex As Exception

                                End Try
                            Catch ex As Exception
                                MessageBox.Show(ex.Message & " Mek:mPropUpdate")
                            End Try
                            If propWrites IsNot Nothing Then
                                oPropWriterequests.Requests = propWrites
                            End If

                            '****************properties end******************
                            Dim newfile As ACW.File = Nothing
                            ' Dim tempI As Integer
                            Try
                                'now the file should be ready for copying
                                Dim ext As String = newName.Split(".").Last
                                uploadTicket = m_conn.WebServiceManager.FilestoreService.CopyFile(downloadTicket.Bytes, ext, True, oPropWriterequests, results)
                                newfile = m_conn.WebServiceManager.DocumentService.AddUploadedFile(newFolderID, newName, "New copy from " & copiedfrom, DateTime.Now, fileassocparams, Nothing,
                                         parfile.FileClass, parfile.Hidden, uploadTicket.ToByteArray())
                                'then let's add new file info to masterarr
                                'compare existing mids(i) to masterarr(6,tempi)

                                ' For tempI = 0 To mArrlength

                                ' If mIDs(i) = Int64.Parse(masterArr(6, tempI)) Then


                                masterArr(8, 0) = newfile.Id 'new id


                                folder = m_conn.WebServiceManager.DocumentService.GetFolderById(newfile.FolderId)
                                masterArr(11, 0) = folder.FullName
                                masterArr(12, 0) = m_conn.WorkingFoldersManager.GetWorkingFolder(folder.FullName.ToString).ToString
                                masterArr(13, 0) = newfile.MasterId
                                'Exit For


                                ' End If
                                'Next
                            Catch ex As Exception
                                MessageBox.Show(ex.Message & " Mek:mCopyAndmasterArrUpdate")
                            End Try

                            Try



                                If newfile IsNot Nothing Then
                                    Dim VaultExplorerUtil As IExplorerUtil
                                    Try

                                        VaultExplorerUtil = ExplorerLoader.LoadExplorerUtil(m_conn.Server, m_conn.Vault, m_conn.UserID, m_conn.Ticket)
                                        Dim oPropefs As ACW.PropDef() = mgr.PropertyService.GetPropertyDefinitionsByEntityClassId("FILE")
                                        For Each oPropdef As ACW.PropDef In oPropefs
                                            If oPropdef.DispName = "SEED_STATE" Then
                                                Dim propAndVals As New Dictionary(Of Autodesk.Connectivity.WebServices.PropDef, Object)
                                                propAndVals.Add(oPropdef, oState)
                                                VaultExplorerUtil.UpdateFileProperties(newfile, propAndVals)
                                                VaultExplorerUtil = Nothing

                                                '  mgr.DocumentService.UpdateFileProperties(parfile.MasterId.ToSingleArray, propAndVals.ToSingleArray)
                                            End If
                                        Next
                                    Catch ex As Exception
                                        VaultExplorerUtil = Nothing
                                    End Try
                                End If
                                'let's download file and update local file references
                                If newfile IsNot Nothing Then


                                    Dim fileIteration As VDF.Vault.Currency.Entities.FileIteration = New VDF.Vault.Currency.Entities.FileIteration(m_conn, newfile)
                                    'tbom2.CompArray = oComparray


                                    '  downloadticket = m_conn.WebServiceManager.DocumentService.GetDownloadTicketsByMasterIds(newfile.MasterId.ToSingleArray()).First()
                                    Dim settings As VDF.Vault.Settings.AcquireFilesSettings = New VDF.Vault.Settings.AcquireFilesSettings(m_conn)
                                    settings.CheckoutComment = "File is CheckedOut By UpdatingReferences"
                                    settings.LocalPath = Nothing
                                    settings.OptionsRelationshipGathering.FileRelationshipSettings.IncludeChildren = False 'vaihdettu
                                    settings.OptionsRelationshipGathering.FileRelationshipSettings.RecurseChildren = False
                                    settings.OptionsRelationshipGathering.FileRelationshipSettings.IncludeLibraryContents = False ' vaihdettu
                                    settings.OptionsRelationshipGathering.FileRelationshipSettings.VersionGatheringOption = Autodesk.DataManagement.Client.Framework.Vault.Currency.VersionGatheringOption.Latest
                                    settings.DefaultAcquisitionOption = VDF.Vault.Settings.AcquireFilesSettings.AcquisitionOption.Download
                                    settings.AddFileToAcquire(fileIteration, VDF.Vault.Settings.AcquireFilesSettings.AcquisitionOption.Download)
                                    Dim fResults As VDF.Vault.Results.AcquireFilesResults = m_conn.FileManager.AcquireFiles(settings)


                                    For Each acquireFilesResult As VDF.Vault.Results.FileAcquisitionResult In fResults.FileResults
                                        Dim assocs As ACW.FileAssocArray() = m_conn.WebServiceManager.DocumentService.GetFileAssociationsByIds(New Long() {acquireFilesResult.File.EntityIterationId}, ACW.FileAssociationTypeEnum.None, False, ACW.FileAssociationTypeEnum.Dependency, False, False, False)
                                        If assocs.First().FileAssocs Is Nothing Then
                                            Continue For
                                        End If

                                        Dim fileAssocs = assocs.First().FileAssocs.Where(Function(fa) fa.ParFile.MasterId = acquireFilesResult.File.EntityMasterId)
                                        Dim refs = New List(Of VDF.Vault.Currency.FileSystem.FileReference)()
                                        For Each fileAssoc As ACW.FileAssoc In fileAssocs
                                            Dim fileCld = fResults.FileResults.FirstOrDefault(Function(f) f.File.EntityMasterId = fileAssoc.CldFile.MasterId)
                                            If fileCld Is Nothing Then
                                                Continue For
                                            End If
                                            Dim reference = New VDF.Vault.Currency.FileSystem.FileReference(fileAssoc.RefId, fileCld.LocalPath, fileAssoc.Source)
                                            refs.Add(reference)
                                        Next
                                        Dim settings1 As VDF.Vault.Settings.AcquireFilesSettings = New VDF.Vault.Settings.AcquireFilesSettings(m_conn)
                                        settings1.AddFileToAcquire(fileIteration, VDF.Vault.Settings.AcquireFilesSettings.AcquisitionOption.Checkout)

                                        Dim f1Results As VDF.Vault.Results.AcquireFilesResults = m_conn.FileManager.AcquireFiles(settings1)


                                        Dim updateReferenceModel = New VDF.Vault.Models.UpdateFileReferencesModel()
                                        updateReferenceModel.SetTargetFilePath(acquireFilesResult.File, acquireFilesResult.LocalPath)
                                        updateReferenceModel.ForceUpdateOfTargetFilePaths = True

                                        updateReferenceModel.UpdateRefsInLocalFile(acquireFilesResult.File, acquireFilesResult.LocalPath, refs)

                                        updateReferenceModel.UpdateVaultStatus = True


                                        Try
                                            '    uploadTicket = m_conn.WebServiceManager.FilestoreService.CopyFile(downloadticket.Bytes, True, Nothing, results)
                                            Dim fldrPath As VDF.Currency.FolderPathAbsolute = New VDF.Currency.FolderPathAbsolute(masterArr(12, 0))
                                            Dim filePath As String = System.IO.Path.Combine(fldrPath.ToString(), fileIteration.EntityName)
                                            Dim filePathAbs As VDF.Currency.FilePathAbsolute = New VDF.Currency.FilePathAbsolute(filePath)
                                            m_conn.FileManager.CheckinFile(fileIteration, "Model paths updated in Assembly", False, fileassocparams,
                                                                                                              Nothing, False, Nothing,
                                                                                    ACW.FileClassification.None, False, filePathAbs)


                                        Catch ex As Exception
                                            MessageBox.Show(ex.Message & " Mek:mUpdate")
                                        End Try

                                    Next


                                End If
                            Catch ex As Exception
                                MessageBox.Show(ex.Message & " Mek:mDownload")
                            End Try






                        Catch ex As Exception
                            MessageBox.Show(ex.Message & " Mek:CopyingMainTry")
                        End Try

                        ''****************************models are now copied and updated let us look for the drawings*****************************
                        'ids to download
                        Dim dIDs As Long() = Nothing
                        If drwArr IsNot Nothing Then
                            Try
                                dArrlength = drwArr.GetLength(1) - 1
                                For di = 0 To dArrlength
                                    ReDim Preserve dIDs(di)
                                    dIDs(di) = drwArr(6, di)
                                Next
                                'now drawing IDs are collected
                                Dim downloadTickets() As ACW.ByteArray = m_conn.WebServiceManager.DocumentService.GetDownloadTicketsByFileIds(dIDs)
                                Dim results As ACW.PropWriteResults = Nothing
                                Dim downloadticket As ACW.ByteArray = Nothing
                                i = 0
                                For Each downloadticket In downloadTickets
                                    Dim newName As String = Nothing
                                    Dim newFolderID As Long = Nothing
                                    Dim copiedfrom As String = Nothing
                                    Dim oldMasterid As Long = Nothing
                                    Dim parfile As ACW.File = m_conn.WebServiceManager.DocumentService.FindFilesByIds(dIDs(i).ToSingleArray()).First
                                    Dim tempI As Integer
                                    For tempI = 0 To dArrlength 'change m ->d
                                        If dIDs(i) = Int64.Parse(drwArr(6, tempI)) Then
                                            newName = drwArr(9, tempI)
                                            newFolderID = drwArr(10, tempI)
                                            copiedfrom = drwArr(4, tempI)
                                            oldMasterid = Int64.Parse(drwArr(14, tempI))
                                            Exit For 'note if there is second similar instance in MasterArr, it has to be solved later on.

                                        End If
                                    Next

                                    'lets get the linked models (there can be several models connected)

                                    Dim param As New ACW.FileAssocParam


                                    Dim fileassocparamsdrwstr(,) As String = Nothing
                                    Dim fi As Integer = 0
                                    fassocArr = m_conn.WebServiceManager.DocumentService.GetLatestFileAssociationsByMasterIds(oldMasterid.ToSingleArray, ACW.FileAssociationTypeEnum.None, False, ACW.FileAssociationTypeEnum.Dependency, False, False, False, False).First
                                    For Each Assoc As ACW.FileAssoc In fassocArr.FileAssocs
                                        'lets check is the file updated

                                        For assocI As Integer = 0 To mArrlength
                                            Dim assocfound As Boolean = False
                                            If Not masterArr(8, assocI) Is Nothing Then
                                                If Assoc.CldFile.Id = Int64.Parse(masterArr(8, assocI)) Then
                                                    '  ReDim Preserve fileassocparamsdrw(fi)
                                                    ReDim Preserve fileassocparamsdrwstr(4, fi)
                                                    ' param.CldFileId = Assoc.CldFile.Id
                                                    fileassocparamsdrwstr(0, fi) = Assoc.CldFile.Id
                                                    ' param.RefId = Assoc.RefId
                                                    fileassocparamsdrwstr(1, fi) = Assoc.RefId
                                                    '  param.Source = Assoc.Source
                                                    fileassocparamsdrwstr(2, fi) = Assoc.Source
                                                    ' param.Typ = Assoc.Typ
                                                    fileassocparamsdrwstr(3, fi) = Assoc.Typ
                                                    folder = m_conn.WebServiceManager.DocumentService.GetFolderById(Assoc.CldFile.FolderId)
                                                    ' param.ExpectedVaultPath = Assoc.ExpectedVaultPath
                                                    fileassocparamsdrwstr(4, fi) = Assoc.ExpectedVaultPath
                                                    '  fileassocparam = param
                                                    assocfound = True
                                                ElseIf assocfound = False Then ' new assoc lets' find new ID

                                                    If Assoc.CldFile.Id = Int64.Parse(masterArr(6, assocI)) Then
                                                        '  ReDim Preserve fileassocparamsdrw(fi)
                                                        ReDim Preserve fileassocparamsdrwstr(4, fi)
                                                        If Not masterArr(8, assocI) Is Nothing Then
                                                            '     param.CldFileId = Int64.Parse(masterArr(8, assocI))
                                                            fileassocparamsdrwstr(0, fi) = Int64.Parse(masterArr(8, assocI))
                                                        End If
                                                        '  param.RefId = Assoc.RefId
                                                        fileassocparamsdrwstr(1, fi) = Assoc.RefId
                                                        '   param.Source = Assoc.Source
                                                        fileassocparamsdrwstr(2, fi) = Assoc.Source
                                                        '  param.Typ = Assoc.Typ
                                                        fileassocparamsdrwstr(3, fi) = Assoc.Typ
                                                        folder = m_conn.WebServiceManager.DocumentService.GetFolderById(Int64.Parse(masterArr(10, assocI)))
                                                        ' param.ExpectedVaultPath = masterArr(11, assocI) & "/" & masterArr(9, assocI)
                                                        fileassocparamsdrwstr(4, fi) = masterArr(11, assocI) & "/" & masterArr(9, assocI)
                                                        '  fileassocparam = param

                                                        assocfound = True

                                                    ElseIf assocfound = False Then



                                                    End If
                                                End If
                                            End If
                                        Next

                                        fi += 1
                                    Next
                                    'now we have fileassocparams and  fileassocparamsdrwstr
                                    fi = 0
                                    Dim fileassocparams2 As ACW.FileAssocParam()
                                    For fi = 0 To fileassocparamsdrwstr.Length / 5 - 1
                                        Dim param1 As New ACW.FileAssocParam
                                        param1.CldFileId = Int64.Parse(fileassocparamsdrwstr(0, fi))
                                        param1.RefId = Int64.Parse(fileassocparamsdrwstr(1, fi))
                                        param1.Source = fileassocparamsdrwstr(2, fi)
                                        param1.Typ = Int64.Parse(fileassocparamsdrwstr(3, fi))
                                        param1.ExpectedVaultPath = fileassocparamsdrwstr(4, fi)
                                        ReDim Preserve fileassocparams2(fi)
                                        fileassocparams2(fi) = param1
                                    Next
                                    '***************Properties**************
                                    Dim fileProps As ACW.CtntSrcPropDef() = m_conn.WebServiceManager.FilestoreService.GetContentSourcePropertyDefinitions(downloadticket.Bytes, True)
                                    Dim propSet = fileProps.Where(Function(n) n.Typ = ACW.DataType.[String])
                                    Dim pNumber As String = drwArr(9, tempI)
                                    pNumber = Strings.Left(pNumber, pNumber.Length - 4)
                                    '  Dim checkedOutFile As ACW.File = m_conn.WebServiceManager.DocumentService.CheckoutFile(fileiteration.EntityIterationId, CheckoutFileOptions.Master, My.Computer.Name.ToString, "c:\temp", "test create new version", downloadticket)
                                    Dim oPropWriterequests As New ACW.PropWriteRequests
                                    Dim propWrites() As ACW.PropWriteReq = Nothing
                                    Dim propsetI As Integer = 0
                                    Try

                                        For Each prop As ACW.CtntSrcPropDef In propSet
                                            'Part Number
                                            Dim erpcodeval As String = pNumber.ToUpper
                                            If prop.DispName = "Part Number" Then
                                                Dim propWrite As New ACW.PropWriteReq() With {
                                    .CanCreate = False,
                                    .Moniker = prop.Moniker,
                                    .Val = pNumber.ToUpper
                                }
                                                ReDim Preserve propWrites(propsetI)
                                                propWrites(propsetI) = propWrite
                                                propsetI += 1
                                            ElseIf prop.DispName = "ERP_PROJECT" Then
                                                Dim propWrite As New ACW.PropWriteReq() With {
                                                    .CanCreate = False,
                                                    .Moniker = prop.Moniker,
                                                    .Val = erp_project
                                                }
                                                ReDim Preserve propWrites(propsetI)
                                                propWrites(propsetI) = propWrite
                                                propsetI += 1
                                            ElseIf prop.DispName = "PROJECT_NAME" Then
                                                Dim propWrite As New ACW.PropWriteReq() With {
                                                    .CanCreate = False,
                                                    .Moniker = prop.Moniker,
                                                    .Val = project_name
                                                }
                                                ReDim Preserve propWrites(propsetI)
                                                propWrites(propsetI) = propWrite
                                                propsetI += 1
                                            ElseIf prop.DispName = "SHIP" Then
                                                Dim propWrite As New ACW.PropWriteReq() With {
                                                    .CanCreate = False,
                                                    .Moniker = prop.Moniker,
                                                    .Val = newShipCode
                                                }
                                                ReDim Preserve propWrites(propsetI)
                                                propWrites(propsetI) = propWrite
                                                propsetI += 1
                                            ElseIf prop.DispName = "SEED" Then
                                                Dim propWrite As New ACW.PropWriteReq() With {
                                                    .CanCreate = False,
                                                    .Moniker = prop.Moniker,
                                                    .Val = copiedfrom
                                                }
                                                ReDim Preserve propWrites(propsetI)
                                                propWrites(propsetI) = propWrite
                                                propsetI += 1
                                            ElseIf prop.DispName = "SEED_STATE" Then
                                                Dim propWrite As New ACW.PropWriteReq() With {
                                                    .CanCreate = True,
                                                    .Moniker = prop.Moniker,
                                                    .Val = oState
                                                }
                                                ReDim Preserve propWrites(propsetI)
                                                propWrites(propsetI) = propWrite
                                                propsetI += 1
                                            End If
                                        Next
                                    Catch ex As Exception
                                        MessageBox.Show(ex.Message & " Mek:drwPropertiesUpdate")
                                    End Try
                                    If propWrites IsNot Nothing Then
                                        oPropWriterequests.Requests = propWrites
                                    End If
                                    '***************Properties end**********
                                    Dim newfile As ACW.File
                                    Try


                                        'let's make a copy
                                        Dim ext As String = newName.Split(".").Last
                                        Dim uploadTicket As Byte() = m_conn.WebServiceManager.FilestoreService.CopyFile(downloadticket.Bytes, ext, True, oPropWriterequests, results)
                                        newfile = m_conn.WebServiceManager.DocumentService.AddUploadedFile(newFolderID, newName, "New copy from " & copiedfrom, DateTime.Now, fileassocparams2, Nothing,
                                                 parfile.FileClass, parfile.Hidden, uploadTicket.ToByteArray())



                                        'then let's add new file info to drwrarr
                                        'compare existing mids(i) to dwrarr(6,tempi)





                                        For tempI = 0 To dArrlength

                                            If dIDs(i) = Int64.Parse(drwArr(6, tempI)) Then

                                                drwArr(8, tempI) = newfile.Id 'new id
                                                folder = m_conn.WebServiceManager.DocumentService.GetFolderById(newfile.FolderId)
                                                drwArr(11, tempI) = folder.FullName
                                                drwArr(12, tempI) = m_conn.WorkingFoldersManager.GetWorkingFolder(folder.FullName.ToString).ToString
                                                drwArr(13, tempI) = newfile.MasterId
                                                Exit For
                                            End If
                                        Next

                                    Catch ex As Exception
                                        MessageBox.Show(ex.Message & " Mek:drwCopydone")
                                        returnValue = "Error"
                                    End Try

                                    'Update local refs
                                    'let's download file and update local file references

                                    Try
                                        If newfile IsNot Nothing Then



                                            Dim fileIteration As VDF.Vault.Currency.Entities.FileIteration = New VDF.Vault.Currency.Entities.FileIteration(m_conn, newfile)
                                            Dim settings As VDF.Vault.Settings.AcquireFilesSettings = New VDF.Vault.Settings.AcquireFilesSettings(m_conn)
                                            settings.CheckoutComment = "File is CheckedOut By UpdatingReferences"
                                            settings.LocalPath = Nothing
                                            settings.OptionsRelationshipGathering.FileRelationshipSettings.IncludeRelatedDocumentation = False
                                            settings.OptionsRelationshipGathering.FileRelationshipSettings.IncludeChildren = True
                                            settings.OptionsRelationshipGathering.FileRelationshipSettings.RecurseChildren = False ' vaihdettu
                                            settings.OptionsRelationshipGathering.FileRelationshipSettings.IncludeParents = False ' vaihdettu
                                            settings.OptionsRelationshipGathering.FileRelationshipSettings.IncludeLibraryContents = True
                                            settings.OptionsRelationshipGathering.FileRelationshipSettings.VersionGatheringOption = Autodesk.DataManagement.Client.Framework.Vault.Currency.VersionGatheringOption.Latest
                                            settings.DefaultAcquisitionOption = VDF.Vault.Settings.AcquireFilesSettings.AcquisitionOption.Download
                                            settings.AddFileToAcquire(fileIteration, VDF.Vault.Settings.AcquireFilesSettings.AcquisitionOption.Download)
                                            Dim fResults As VDF.Vault.Results.AcquireFilesResults = m_conn.FileManager.AcquireFiles(settings)


                                            For Each acquireFilesResult As VDF.Vault.Results.FileAcquisitionResult In fResults.FileResults
                                                Dim assocs As ACW.FileAssocArray() = m_conn.WebServiceManager.DocumentService.GetFileAssociationsByIds(New Long() {acquireFilesResult.File.EntityIterationId},
                                             ACW.FileAssociationTypeEnum.None, False, ACW.FileAssociationTypeEnum.Dependency, False, False, False)
                                                If assocs.First().FileAssocs Is Nothing Then
                                                    Continue For
                                                End If
                                                Dim fileIter As _
    VDF.Vault.Currency.Entities.FileIteration = New VDF.Vault.Currency.Entities.FileIteration(m_conn, acquireFilesResult.File)
                                                ''strmyERPTYPE = m_conn.PropertyManager.GetPropertyValue(fileIter, myERPTYPE, Nothing)

dwgcheck:
                                                If Split(fileIter.EntityName, ".").Last <> "dwg" Then
                                                    Continue For
                                                End If

                                                Dim fileAssocs = assocs.First().FileAssocs.Where(Function(fa) fa.ParFile.MasterId = acquireFilesResult.File.EntityMasterId)
                                                Dim refs = New List(Of VDF.Vault.Currency.FileSystem.FileReference)()
                                                For Each fileAssoc As ACW.FileAssoc In fileAssocs
                                                    Dim fileCld = fResults.FileResults.FirstOrDefault(Function(f) f.File.EntityMasterId = fileAssoc.CldFile.MasterId)
                                                    If fileCld Is Nothing Then
                                                        Continue For
                                                    End If
                                                    Dim reference = New VDF.Vault.Currency.FileSystem.FileReference(fileAssoc.RefId, fileCld.LocalPath, fileAssoc.Source)
                                                    refs.Add(reference)
                                                Next
                                                Dim settings1 As VDF.Vault.Settings.AcquireFilesSettings = New VDF.Vault.Settings.AcquireFilesSettings(m_conn)
                                                settings1.AddFileToAcquire(fileIteration, VDF.Vault.Settings.AcquireFilesSettings.AcquisitionOption.Checkout)
                                                Dim f1Results As VDF.Vault.Results.AcquireFilesResults = m_conn.FileManager.AcquireFiles(settings1)



                                                Dim updateReferenceModel = New VDF.Vault.Models.UpdateFileReferencesModel()
                                                updateReferenceModel.SetTargetFilePath(acquireFilesResult.File, acquireFilesResult.LocalPath)
                                                updateReferenceModel.ForceUpdateOfTargetFilePaths = True
                                                updateReferenceModel.UpdateRefsInLocalFile(acquireFilesResult.File, acquireFilesResult.LocalPath, refs)
                                                updateReferenceModel.UpdateVaultStatus = True

                                            Next
                                        End If
                                    Catch ex As Exception
                                        MessageBox.Show(ex.Message & " Mek:drwDownloadAndUpdate")
                                        returnValue = "Error"
                                    End Try

                                    Try

                                        Dim fileIteration As VDF.Vault.Currency.Entities.FileIteration = New VDF.Vault.Currency.Entities.FileIteration(m_conn, newfile)
                                        '    uploadTicket = m_conn.WebServiceManager.FilestoreService.CopyFile(downloadticket.Bytes, True, Nothing, results)
                                        Dim fldrPath As VDF.Currency.FolderPathAbsolute = New VDF.Currency.FolderPathAbsolute(drwArr(12, tempI))
                                        Dim filePath As String = System.IO.Path.Combine(fldrPath.ToString(), fileIteration.EntityName)
                                        Dim filePathAbs As VDF.Currency.FilePathAbsolute = New VDF.Currency.FilePathAbsolute(filePath)
                                        m_conn.FileManager.CheckinFile(fileIteration, "Model paths updated in drawing", False, fileassocparams2,
                                                                                                                                                  Nothing, False, Nothing,
                                                                                                                        ACW.FileClassification.DesignDocument, False, filePathAbs)



                                    Catch ex As Exception
                                        MessageBox.Show(ex.Message & " Mek:drwUpdate")
                                        returnValue = "Error"
                                    End Try


                                    i += 1
                                Next ' downloadtickect

                            Catch ex As Exception
                                MessageBox.Show(ex.Message & " Mek:DrwMain")
                                fids = Nothing
                                dIDs = Nothing
                                returnValue = "Error"
                            End Try
                        End If

                    Catch ex As Exception
                    End Try
                Catch ex As Exception
                End Try


            ElseIf selectedFile.Name.Split(".").Last.ToLower = "dgn" Then 'dgn
                Try
                    fassocArr = m_conn.WebServiceManager.DocumentService.GetLatestFileAssociationsByMasterIds(selectedFile.MasterId.ToSingleArray, ACW.FileAssociationTypeEnum.None, False, ACW.FileAssociationTypeEnum.Dependency, True, False, False, False).First

                    'create the row 0 to masterarr
                    ReDim Preserve masterArr(rownumb, 0)
                    'Main assy
                    masterArr(0, 0) = folder.Id  'folderid
                    masterArr(1, 0) = folder.FullName 'fullpath
                    masterArr(2, 0) = m_conn.WorkingFoldersManager.GetWorkingFolder(folder.FullName.ToString).ToString
                    masterArr(3, 0) = Split(selectedFile.Name, ".").Last 'ext
                    masterArr(4, 0) = selectedFile.Name 'name
                    masterArr(5, 0) = "PAR_ITEM" 'Par*
                    masterArr(6, 0) = selectedFile.Id ' ID
                    masterArr(7, 0) = "" 'old parentid I think this should move down after new ID is created
                    masterArr(14, 0) = selectedFile.MasterId 'old masterid
                    masterArr(17, 0) = "" 'SK_Item

                Catch ex As Exception
                    MessageBox.Show(ex.Message & " Mek:MasterArr")
                End Try

                'let us generate the New folders, New names for components which will be copied.

                Try
                    masterArr(9, 0) = masterArr(4, 0).Replace(oldShipCode, newShipCode)
                    'project folders
                    Try
                        If oTargetFolderNewFolders = "" Then
                            masterArr(10, 0) = oTargetFolderId
                            masterArr(11, 0) = oTargetFolderFullName
                        Else 'try to create new folders
                            Dim newFolders As String() = oTargetFolderNewFolders.Split("/")
                            For i As Integer = 0 To newFolders.Count - 1
                                Dim newFolder As ACW.Folder = Nothing
                                Try
                                    newFolder = mgr.DocumentService.AddFolder(newFolders(i), oTargetFolderId, False)
                                    oTargetFolderId = newFolder.Id
                                    oTargetFolderFullName = newFolder.FullName
                                Catch ex As Exception
                                    'folder exist
                                    Try
                                        Dim projectFolder As VDF.Vault.Currency.Entities.Folder = findfolder(oTargetFolderFullName & "/" & newFolders(i))
                                        oTargetFolderId = projectFolder.Id
                                        oTargetFolderFullName = projectFolder.FullName
                                    Catch ex

                                    End Try
                                End Try
                                ' If New

                            Next
                            masterArr(10, 0) = oTargetFolderId
                            masterArr(11, 0) = oTargetFolderFullName
                        End If


                    Catch ex As Exception

                    End Try


                Catch ex As Exception

                End Try

                'Copy FIle
                Try
                    Dim downloadTicket As ACW.ByteArray = m_conn.WebServiceManager.DocumentService.GetDownloadTicketsByFileIds(Int64.Parse(masterArr(6, 0)).ToSingleArray).First
                    Dim results As ACW.PropWriteResults = Nothing
                    Dim uploadTicket As Byte()
                    Dim newName As String = Nothing
                    Dim newFolderID As Long = Nothing
                    Dim copiedfrom As String = Nothing
                    Dim oldMasterid As Long = Nothing
                    Dim erptype As String = "PAR_ITEM"
                    Dim seednumber As String = Nothing
                    Dim seedState As String = oState
                    Dim parfile As ACW.File = m_conn.WebServiceManager.DocumentService.FindFilesByIds(Int64.Parse(masterArr(6, 0)).ToSingleArray).First
                    '
                    newName = masterArr(9, 0)
                    newFolderID = masterArr(10, 0)
                    copiedfrom = masterArr(4, 0)
                    oldMasterid = Int64.Parse(masterArr(14, 0))
                    erptype = masterArr(5, 0)
                    seednumber = copiedfrom
                    seedState = oState

                    '****************properties******************
                    Dim oPropWriterequests As New ACW.PropWriteRequests
                    Dim propWrites() As ACW.PropWriteReq = Nothing
                    Try
                        Dim fileProps As ACW.CtntSrcPropDef() = m_conn.WebServiceManager.FilestoreService.GetContentSourcePropertyDefinitions(downloadTicket.Bytes, True)
                        Dim propSet = fileProps.Where(Function(n) n.Typ = ACW.DataType.[String])
                        Dim pNumber As String = newName
                        pNumber = Strings.Left(pNumber, pNumber.Length - 4)


                        Dim propsetI As Integer = 0





                        Try
                            For Each prop As ACW.CtntSrcPropDef In propSet
                                'Part Number
                                Dim erpcodeval As String = pNumber.ToUpper
                                If prop.DispName = "Part Number" Then
                                    Dim propWrite As New ACW.PropWriteReq() With {
                                .CanCreate = False,
                                .Moniker = prop.Moniker,
                                .Val = erpcodeval
                            }
                                    ReDim Preserve propWrites(propsetI)
                                    propWrites(propsetI) = propWrite
                                    propsetI += 1
                                ElseIf prop.DispName = "ERP_PROJECT" Then
                                    Dim propWrite As New ACW.PropWriteReq() With {
                                                .CanCreate = False,
                                                .Moniker = prop.Moniker,
                                                .Val = erp_project
                                            }
                                    ReDim Preserve propWrites(propsetI)
                                    propWrites(propsetI) = propWrite
                                    propsetI += 1
                                ElseIf prop.DispName = "PROJECT_NAME" Then
                                    Dim propWrite As New ACW.PropWriteReq() With {
                                                .CanCreate = False,
                                                .Moniker = prop.Moniker,
                                                .Val = project_name
                                            }
                                    ReDim Preserve propWrites(propsetI)
                                    propWrites(propsetI) = propWrite
                                    propsetI += 1
                                ElseIf prop.DispName = "SHIP" Then
                                    Dim propWrite As New ACW.PropWriteReq() With {
                                                .CanCreate = False,
                                                .Moniker = prop.Moniker,
                                                .Val = newShipCode
                                            }
                                    ReDim Preserve propWrites(propsetI)
                                    propWrites(propsetI) = propWrite
                                    propsetI += 1
                                ElseIf prop.DispName = "SEED" Then
                                    Dim propWrite As New ACW.PropWriteReq() With {
                                                .CanCreate = False,
                                                .Moniker = prop.Moniker,
                                                .Val = copiedfrom
                                            }
                                    ReDim Preserve propWrites(propsetI)
                                    propWrites(propsetI) = propWrite
                                    propsetI += 1
                                ElseIf prop.DispName = "SEED_STATE" Then
                                    Dim propWrite As New ACW.PropWriteReq() With {
                                                .CanCreate = False,
                                                .Moniker = prop.Moniker,
                                                .Val = seedState
                                            }
                                    ReDim Preserve propWrites(propsetI)
                                    propWrites(propsetI) = propWrite
                                    propsetI += 1
                                End If
                            Next 'prop
                            If propWrites IsNot Nothing Then
                                oPropWriterequests.Requests = propWrites
                            End If
                        Catch ex As Exception

                        End Try
                    Catch ex As Exception
                        MessageBox.Show(ex.Message & " Mek:mPropUpdateDGN")
                    End Try

                    '****************properties end******************
                    Dim newfile As ACW.File
                    Try
                        'now the file should be ready for copying
                        Dim ext As String = newName.Split(".").Last
                        uploadTicket = m_conn.WebServiceManager.FilestoreService.CopyFile(downloadTicket.Bytes, ext, True, oPropWriterequests, results)
                        newfile = m_conn.WebServiceManager.DocumentService.AddUploadedFile(newFolderID, newName, "New copy from " & copiedfrom, DateTime.Now, Nothing, Nothing,
                                 parfile.FileClass, parfile.Hidden, uploadTicket.ToByteArray())
                        'then let's add new file info to masterarr
                        'compare existing mids(i) to masterarr(6,tempi)

                        ' For tempI = 0 To mArrlength

                        ' If mIDs(i) = Int64.Parse(masterArr(6, tempI)) Then


                        masterArr(8, 0) = newfile.Id 'new id


                        folder = m_conn.WebServiceManager.DocumentService.GetFolderById(newfile.FolderId)
                        masterArr(11, 0) = folder.FullName
                        masterArr(12, 0) = m_conn.WorkingFoldersManager.GetWorkingFolder(folder.FullName.ToString).ToString
                        masterArr(13, 0) = newfile.MasterId
                        'Exit For


                        ' End If
                        'Next
                    Catch ex As Exception
                        MessageBox.Show(ex.Message & " Mek:mCopyAndmasterArrUpdateDGN")
                    End Try
                    If newfile IsNot Nothing Then
                        ''Dim fileIteration As VDF.Vault.Currency.Entities.FileIteration = New VDF.Vault.Currency.Entities.FileIteration(m_conn, newfile)
                        ''Try

                        ''    Dim settings As VDF.Vault.Settings.AcquireFilesSettings = New VDF.Vault.Settings.AcquireFilesSettings(m_conn)
                        ''    settings.CheckoutComment = "File is CheckedOut For SEED_STATE"
                        ''    settings.LocalPath = Nothing
                        ''    settings.OptionsRelationshipGathering.FileRelationshipSettings.VersionGatheringOption = Autodesk.DataManagement.Client.Framework.Vault.Currency.VersionGatheringOption.Latest
                        ''    settings.DefaultAcquisitionOption = VDF.Vault.Settings.AcquireFilesSettings.AcquisitionOption.Download
                        ''    settings.AddFileToAcquire(fileIteration, VDF.Vault.Settings.AcquireFilesSettings.AcquisitionOption.Download)
                        ''    Dim fResults As VDF.Vault.Results.AcquireFilesResults = m_conn.FileManager.AcquireFiles(settings)
                        ''Catch ex As Exception

                        ''End Try


                        'try to update the SEED_STATE PRoperty
                        'Seed_state Proeprty in file
                        Dim VaultExplorerUtil As IExplorerUtil
                        Try

                            VaultExplorerUtil = ExplorerLoader.LoadExplorerUtil(m_conn.Server, m_conn.Vault, m_conn.UserID, m_conn.Ticket)
                            Dim oPropefs As ACW.PropDef() = mgr.PropertyService.GetPropertyDefinitionsByEntityClassId("FILE")
                            For Each oPropdef As ACW.PropDef In oPropefs
                                If oPropdef.DispName = "SEED_STATE" Then
                                    Dim propAndVals As New Dictionary(Of Autodesk.Connectivity.WebServices.PropDef, Object)
                                    propAndVals.Add(oPropdef, oState)
                                    VaultExplorerUtil.UpdateFileProperties(newfile, propAndVals)


                                    '  mgr.DocumentService.UpdateFileProperties(parfile.MasterId.ToSingleArray, propAndVals.ToSingleArray)
                                ElseIf oPropdef.DispName = "SEED" Then
                                    Dim propAndVals As New Dictionary(Of Autodesk.Connectivity.WebServices.PropDef, Object)
                                    propAndVals.Add(oPropdef, masterArr(4, 0))
                                    VaultExplorerUtil.UpdateFileProperties(newfile, propAndVals)


                                End If
                            Next
                            VaultExplorerUtil = Nothing
                        Catch ex As Exception
                            VaultExplorerUtil = Nothing
                        End Try
                        ''Dim fldrPath As VDF.Currency.FolderPathAbsolute = New VDF.Currency.FolderPathAbsolute(masterArr(12, 0))
                        ''Dim filePath As String = System.IO.Path.Combine(fldrPath.ToString(), fileIteration.EntityName)
                        ''Dim filePathAbs As VDF.Currency.FilePathAbsolute = New VDF.Currency.FilePathAbsolute(filePath)
                        ''m_conn.FileManager.CheckinFile(fileIteration, "SEED_STATE updated", False, Nothing,
                        ''                                                                                      Nothing, False, Nothing,
                        ''                                                            ACW.FileClassification.None, False, filePathAbs)

                    End If

                Catch ex As Exception

                End Try



            End If
            masterArr = Nothing
            drwArr = Nothing
            returnValue = "Copied"
        Catch ex As Exception
            masterArr = Nothing
            drwArr = Nothing
            returnValue = "Error"
        End Try
        Return returnValue
    End Function
    Function findfolder(ByVal folderstring As String) As VDF.Vault.Currency.Entities.Folder
        Dim myFldrCol As System.Collections.Generic.List _
           (Of VDF.Vault.Currency.Entities.Folder)
        myFldrCol = m_conn.FolderManager.
    GetChildFolders(m_conn.FolderManager.RootFolder,
                                           True, False)


        ' Get the folder to add the new file to change
        ' the FullName test to a Folder in your vault
        Dim myFolder As _
        VDF.Vault.Currency.Entities.Folder = Nothing

        For Each Flder As _
        VDF.Vault.Currency.Entities.Folder In myFldrCol
            '    If Flder.NumberOfChildren > 0 Then
            ' myFolder = findsubfolder(folderstring, Flder)
            '   Else

            If Flder.FullName = folderstring Then
                myFolder = Flder
                Exit For
            End If
            '  End If
        Next
        Return myFolder
    End Function
End Module
