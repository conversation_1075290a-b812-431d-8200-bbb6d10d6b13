﻿'=====================================================================
'  
'  This file is part of the Autodesk Vault API Code Samples.
'
'  Copyright (C) Autodesk Inc.  All rights reserved.
'
'THIS CODE AND INFORMATION ARE PROVIDED "AS IS" WITHOUT WARRANTY OF ANY
'KIND, EITHER EXPRESSED OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE
'IMPLIED WARRANTIES OF MERCHANTABILITY AND/OR FITNESS FOR A
'PARTICULAR PURPOSE.
'=====================================================================


Imports System.Collections.Generic
Imports System.IO
Imports System.Linq
Imports System.Text

Imports Autodesk.Connectivity.WebServices
Imports Autodesk.Connectivity.WebServicesTools
Imports Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections
Imports Autodesk.DataManagement.Client.Framework.Vault.Settings
Imports Autodesk.DataManagement.Client.Framework.Vault.Results
Imports VDF = Autodesk.DataManagement.Client.Framework


Friend Module ExtensionMethods
    Sub New()
    End Sub

    <System.Runtime.CompilerServices.Extension>
    Friend Function ToSingleArray(Of T)(obj As T) As T()
        Return New T() {obj}
    End Function

    <System.Runtime.CompilerServices.Extension>
    Friend Function ToByteArray(inputArray As Byte()) As ByteArray
        Return New ByteArray() With {
            .Bytes = inputArray
        }
    End Function

    <System.Runtime.CompilerServices.Extension>
    Friend Function IsNullOrEmpty(Of T)(collection As IEnumerable(Of T)) As Boolean
        Return collection Is Nothing OrElse collection.Count() = 0
    End Function

    <System.Runtime.CompilerServices.Extension>
    Friend Function ShallowCopy(Of T)(origList As List(Of T)) As List(Of T)
        Dim newList As New List(Of T)()
        newList.AddRange(origList)
        Return newList
    End Function

    <System.Runtime.CompilerServices.Extension>
    Friend Function ToVDFPath(localPath As String) As VDF.Currency.FilePathAbsolute
        Return New VDF.Currency.FilePathAbsolute(localPath)
    End Function
End Module
