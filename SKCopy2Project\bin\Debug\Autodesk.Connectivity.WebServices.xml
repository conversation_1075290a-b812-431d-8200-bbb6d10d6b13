﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Autodesk.Connectivity.WebServices</name>
  </assembly>
  <members>
    <member name="T:Autodesk.Connectivity.WebServices.IWebService">
      <summary>The iterface that all web service objects implement.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.IWebServiceCommandEvents`1">
      <summary>Base interface for Web Service Command Event collections.</summary>
      <typeparam>The event args type.</typeparam>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.IWebServiceExtension">
      <summary>The main entry point for extensions which register for Web Service Command events.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.IWebService.AllowInvokeEvents">
      <summary>Tells if invoke events should fire. This value is true by default.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.IWebService.ClientCertificates">
      <summary>A collection of certificates for making web service calls over an SSL connection.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.IWebService.SecurityHeader">
      <summary>The security header.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.IWebService.Timeout">
      <summary>The time to wait, in milliseconds, for an HTTP response.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.IWebService.Url">
      <summary>The URL for making web service calls.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.IWebService.WebServiceManager">
      <summary>The WebServiceManager object that created the service. Will be null if the service was not created by a WebServiceManager.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.IWebService.PostInvokeEvents">
      <summary>Subscribe to this event to get a notification after every web service call.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.IWebService.PreInvokeEvents">
      <summary>Subscribe to this event to get a notification before every web service call.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.IWebServiceCommandEvents`1.GetRestrictions">
      <summary>This is called first. It allows handlers to add restrictions, which block the web service call.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.IWebServiceCommandEvents`1.Post">
      <summary>This event is called after the web service function is called.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.IWebServiceCommandEvents`1.Pre">
      <summary>This event is called after GetRestrictions and before the web service function is called.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.IWebServiceExtension.OnLoad">
      <summary>This is called when the Vault framework starts up. This is where extensions register for Web Service Command Events.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.AllowedMappingDirection">
      <summary>The allowed property mapping direction values.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.AllowNoBehavior">
      <summary>Tells if the "None" behavior is allowed</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.AssociationType">
      <summary>The type of association.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.AssocPropTyp">
      <summary>The type of association that the property applies to.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.AuthTyp">
      <summary>The mechanism used to authenticate the user.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.BehaviorAssignmentType">
      <summary>Different ways that behaviors or categories can be assigned.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.BOMEditAction">
      <summary>Actions for how a BOM row is being updated</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.BOMStructureEnum">
      <summary>Indicates the BOMStructure value of a Component.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.BOMStructureOverrideEnum">
      <summary>Indicates whether the BOMStructure has been overridden.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.BOMTyp">
      <summary>An enumeration for different Item BOM Types.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.BOMViewEditOptions">
      <summary>An enumeration of options for viewing and/or editing Item BOM information.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.BreakDesignVisualizationLinkCommandList">
      <summary>A list of client operations which should break design visualization links.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.BumpRevisionEnum">
      <summary>Tells how a revision should be bumped.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ChangeOrderMarkupFolderConfig">
      <summary>Configuration options for Change Order markup folders.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ChangeType">
      <summary>The type of change being performed.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.CheckoutFileOptions">
      <summary>They type of ID being used for checking out a file.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.Classification">
      <summary>Tells if the property is user defined within the source.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.CompatStatus">
      <summary>The status of a compatibility check.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ComponentTypeEnum">
      <summary>Indicates the type of component.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.Compression">
      <summary>The type of compression for binary data transfer.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.CondOpers">
      <summary>Condition operators.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.CSPDefTypes">
      <summary>They types of content source definitions. This is a bitfield enum, so multiple values can exist on a single variable.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.DatabaseType">
      <summary>The database type</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.DataType">
      <summary>The data type.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.DesignVisualizationAttachmentStatus">
      <summary>The status attachents to design visualization files.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.DocRestricCode">
      <summary>See page on Restriction Codes for a listing of codes and their meanings.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.EnablementConfig">
      <summary>Information about which features are enabled.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.EnforceChildStateEnum">
      <summary>How lifecycle states should be enforced in child entities. Values can be added together.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.EnforceContentStateEnum">
      <summary>How the lifecycle state should be enforced in linked Entities. Values can be added together.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.EntPersistOpt">
      <summary>The type of persistant ID.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.EventStatus">
      <summary>The status of the command.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FieldType">
      <summary>An enumeration for the type of Item numbering scheme field. Each type corresponds to a class.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileAssocAlg">
      <summary>The algoritm to use when traversing file association trees.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileAssociationTypeEnum">
      <summary>File association options.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileClassification">
      <summary>An enumeration of file classifications.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileElementDataType">
      <summary>The data type of a file element property</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileFormat">
      <summary>An enumeration of file types.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileHistoryTypeOptions">
      <summary>Options for viewing file history.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileLinkTypeEnum">
      <summary>Item File Link Types</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileStatus">
      <summary>An enumeration of FileStatus values.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FilterType">
      <summary>The type of filter.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.IndexingStatus">
      <summary>The status of the indexing engine.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ItemAssignAll">
      <summary>Child item assignment settings</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ItemAssignBhv">
      <summary>The child item assignment behavior options</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ItemAutogroupBhv">
      <summary>Specifies the autogrouping behavior of Item BOMs</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ItemBOMFileAssocTyp">
      <summary>The type of File association.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ItemConflictTyp">
      <summary>An enumeration of the types of item conflicts</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ItemFileLnkTyp">
      <summary>An enumeration of Item-File Link types.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ItemFileLnkTypOpt">
      <summary>An enumeration of Item-File Link types. This is a flag enumeration, which means they can be joined together and passed as a single parameter.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ItemHistoryTyp">
      <summary>Enumeration of item history types</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ItemSubComponentLinkType">
      <summary>An enumeration of item subcomponent link types</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ItemToFileSecurityModeEnum">
      <summary>Item File Link security mode</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.JobStatus">
      <summary>The status of a job.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.JobSyncPropEnum">
      <summary>Tells if the state property needs to be synchronized with the file property.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.LicenseBehavior">
      <summary>The license behavior</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.LicenseType">
      <summary>The license type</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.LicenseUsage">
      <summary>The usage dictated by the license.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.MappingDirection">
      <summary>The direction of a mapping.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.MappingType">
      <summary>Tells if the property value gets updated on each version.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.NumSchmType">
      <summary>An enumeration of Numbering Scheme types.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ProductRestricCode">
      <summary>An enumeration of restrictions for Item operations. See page on Restriction Codes for more information.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PropConstrTyp">
      <summary>Property constraint types.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PropEquivFailTyp">
      <summary>The equivalency failure type.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PropertySearchType">
      <summary>Indicates the scope of the search.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PropertyTypeEnum">
      <summary>Indicates the data type of the property.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PropRestricCode">
      <summary>The cause of the restriction.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PropWriteStatus">
      <summary>For internal use only</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ProvStatus">
      <summary>For internal use only</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PrpgType">
      <summary>The types of ACL propagation.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ReplicationState">
      <summary>The type of replication.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ResolutionMethod">
      <summary>An enumeration of resolutions.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.RestrictionStatus">
      <summary>An enumeration containing the status of a restriction.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.RestrictLifecycleChange">
      <summary>An enumeration representing if lifecycle state changes are restricted to change orders</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.RestrictPurgeOption">
      <summary>Options for what file versions should not be deleted during a purge operation.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.SchemeTypeEnum">
      <summary>An enumeration of BOM Scheme Types</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.SearchOperator">
      <summary>The operator to use for a search condition.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.SearchRule">
      <summary>The rule for how search conditions interact with each other.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.SearchRuleType">
      <summary>Tells if the condition must or may be true. In other words, is the condition an AND or an OR type respectively.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.SecRestricCode">
      <summary>See the Restriction Codes page for more information.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.WmarkLoc">
      <summary>The Watermark location.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.WmarkSize">
      <summary>The watermark size.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.WmarkType">
      <summary>The watermark type.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.XRefTypeEnum">
      <summary>Indicates whether the Component is internal or external in relation to the design file.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.AccessPermis">
      <summary>A boolean value for a certain permission</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.AccessPermisGrp">
      <summary>A grouping of access permissions.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ACE">
      <summary>Access Control Entry. Represents the access rights of a specific user or group.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ACL">
      <summary>Access Control List. A grouping of Access Control Entries (ACEs).</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.Activity">
      <summary>A Change Order activity to perform.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ActivityHist">
      <summary>The Activity History for a Change Order.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.AddChangeOrderCommandEventArgs">
      <summary>Information about the add Change Order operation</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.AddFileCommandEventArgs">
      <summary>Information about the add File operation</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.AddFolderCommandEventArgs">
      <summary>Information about the add Folder operation</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.AddItemCommandEventArgs">
      <summary>Information about the add Item operation</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.AdminService">
      <summary>Contains methods for manipulating users and groups.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.AssocPropDef">
      <summary>A property definition on an association.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.AssocPropDefInfo">
      <summary>An association property definition with extended information.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.AssocPropItem">
      <summary>A property on an association.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.Attmt">
      <summary>An attachment to a File.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.AttmtConflict">
      <summary>Information on a conflict with an Item attachment.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.AuthService">
      <summary>A service for authenticating to the Vault server.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.AutogenField">
      <summary>A numeric scheme field that automatically increments itself. Implementation of NumingSchmField.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.BehaviorService">
      <summary>Contains methods for manipulating behaviors.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.Bhv">
      <summary>A behavior.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.BhvCfg">
      <summary>A collection of behaviors.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.BhvDelRestric">
      <summary>A restriction on deleting a behavior.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.BlkThres">
      <summary>The threshold for processing bulk file operations.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.BOM">
      <summary>Bill of Materials data.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.BOMComp">
      <summary>A part, assembly, or virtual component in the BOM.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.BOMCompareOccDiff">
      <summary>A difference between two Item BOMs.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.BOMCompAttr">
      <summary>Components within the BOM can be attributed with additional information using BOMCompAttr objects.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.BOMCompRefDesig">
      <summary>Associates a BOM Component with a Reference Designator.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.BOMInst">
      <summary>Parent-child relationship described by the BOM.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.BOMProp">
      <summary>A property definition used to attribute components within the BOM.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.BOMRefDesig">
      <summary>A BOM reference designator. Adds additional information to a component or components.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.BOMRefDesigAttr">
      <summary>BOM Designator Attribute. Assigns properties to reference designators.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.BOMSchm">
      <summary>Defines a number of rules and properties which control the BOM representation.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.BOMSchmOccur">
      <summary>An occurrence of a component in a BOM Scheme.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ByteArray">
      <summary>An array of bytes.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.Cat">
      <summary>A category.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.CatCfg">
      <summary>A category and its related behaviors.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.CategoryService">
      <summary>Contains methods for manipulating categories.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.CatRuleSet">
      <summary>The set of rules for a category.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.CatRuleSetCfg">
      <summary>A collection of rule sets.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ChangeOrder">
      <summary>A Change Order.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ChangeOrderEntAssoc">
      <summary>An association between a Change Order and a set of Entities.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ChangeOrderFile">
      <summary>A Change Order and its associated File.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ChangeOrderGrp">
      <summary>A Change Order and associated objects.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ChangeOrderItem">
      <summary>A Change Order and its associated Item.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ChangeOrderLite">
      <summary>A change order. Contains less information than the standard ChangeOrder class.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ChangeOrderNumSchm">
      <summary>A description of a change order numbering scheme.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ChangeOrderParticipant">
      <summary>A user for a Change Order.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ChangeOrderService">
      <summary>Contains methods for creating and manipulating change orders.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.CheckinFileCommandEventArgs">
      <summary>Information about the checkin File operation</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.CheckoutFileCommandEventArgs">
      <summary>Information about the checkout File operation</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.CodeWord">
      <summary>A string code and description.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.CommitChangeOrderCommandEventArgs">
      <summary>Information about the commit Change Order operation</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.CommitItemCommandEventArgs">
      <summary>Information about the commit Item operation</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ComplexField">
      <summary>An section of text with variable values and a fixed size. Implementation of NumingSchmField.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.CompProp">
      <summary>A component property.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.CompressionHeader">
      <summary>Information on they type of compression to use for binary data transfer.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.CtntSrc">
      <summary>The Content Source. In other words, the point of origin for a piece of data. Usually this is divided up into applications.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.CtntSrcPropDef">
      <summary>A property definition within a file.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.CustEnt">
      <summary>A custom entity.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.CustEntDef">
      <summary>The definition or type of custom entity.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.CustomEntityService">
      <summary>A collection of methods related to the Custom Entity entity type.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.DbOwn">
      <summary>Database ownership information. Database ownership covers anything that is not entity based.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.DeleteChangeOrderCommandEventArgs">
      <summary>Information about the delete Change Order operation</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.DeleteFileCommandEventArgs">
      <summary>Information about the delete File operation</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.DeleteFolderCommandEventArgs">
      <summary>Information about the delete Folder operation</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.DeleteItemCommandEventArgs">
      <summary>Information about the delete Item operation</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.DelimField">
      <summary>A delimiter. Implementation of NumSchmField.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.DocRestric">
      <summary>A restriction on an operation.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.DocRestricArray">
      <summary>An array of Document Restrictions</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.DocumentService">
      <summary>Contains methods for manipulating files and folders within a vault.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.DocumentServiceExtensions">
      <summary>Contains more methods for manipulating files and folders within a vault.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.DownloadFileCommandEventArgs">
      <summary>Information about the download File operation</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.EditChangeOrderCommandEventArgs">
      <summary>Information about the edit Change Order operation</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.EditItemCommandEventArgs">
      <summary>Information about the edit Item operation</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.Email">
      <summary>An email.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.Ent">
      <summary>A generic Entity object.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.EntACL">
      <summary>An Entity and its associated ACLs</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.EntCat">
      <summary>The Category on an Entity.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.EntClassAssoc">
      <summary>The entity class associated with a property definition.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.EntClassBehAssoc">
      <summary>An association between a behavior and an entity class.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.EntClassCfg">
      <summary>Information about an entity class.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.EntClassCtntSrcPropCfg">
      <summary>The content source properties for a specific entity class.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.EntClassPropCfg">
      <summary>The supported property features within an entity class.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.EntLfCyc">
      <summary>The lifecycle information for an Entity.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.EntOwn">
      <summary>Ownership information on an entity.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.EntsAndACLs">
      <summary>A collection of Entity IDs and associated ACL objects.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.Facet">
      <summary>A property value and hit count.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FacetSet">
      <summary>A set of property values.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FacetSetFilter">
      <summary>The filter to define a set of property definitions.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.Facility">
      <summary>A feature of an ADMS install.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.File">
      <summary>Contains information relating to a file in the Vault.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileArray">
      <summary>An array of files.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileAssoc">
      <summary>Contains a parent/child relationship between two files.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileAssocArray">
      <summary>An array of FileAssoc objects containing parent/child relationship for two files.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileAssocLite">
      <summary>An association between two files. The files are represented by IDs instead of full File objects.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileAssocParam">
      <summary>Information about a file association.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileAssocParamArray">
      <summary>An array of file association parameters.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileAssocRestric">
      <summary>A restriction on setting up a dependency between two files.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileCat">
      <summary>Category information for a file master.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileDelRestric">
      <summary>A restriction on deleting a file.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileDelRestricArray">
      <summary>An array of delete restrictions.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileDelRestricEx">
      <summary>A restriction thrown when deleting a specific version of a File.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileDelStatus">
      <summary>A File object and information on its delete status.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileElement">
      <summary>An element within a file that can be indexed and searched.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileElementProp">
      <summary>A property value on a file element.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileElementPropDef">
      <summary>A property definition for an element within a file.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileElementSrchClause">
      <summary>A set of search conditions on file elements.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileElementSrchCondition">
      <summary>A search condition for locating a set of file elements.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileFolder">
      <summary>A File and Folder object.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileLfCyc">
      <summary>Life cycle information for a file.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileNameAndURL">
      <summary>A file in a shared location.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileNmngDescr">
      <summary>A generated file name.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileNmngSchm">
      <summary>The file naming scheme.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FilePath">
      <summary>A File and Path object.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FilePathArray">
      <summary>An array of FilePath objects.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileRenameRestric">
      <summary>A restriction on setting up a dependency between two FileVersions.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileRenameRestricArray">
      <summary>An array of rename restrictions.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileRev">
      <summary>Information about the revision of a file.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FilestoreService">
      <summary>A service for uploading and downloading binary file data.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FilestoreVaultService">
      <summary>Information on the Knowledge Vaults.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FileTransferHeader">
      <summary>Information about a file transfer.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FixedTxtField">
      <summary>A block of static text. Implementation of NumSchmField.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.Folder">
      <summary>Contains information about a folder in the vault.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FolderArray">
      <summary>An array of folders.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FolderCat">
      <summary>The Category on a Folder.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FolderDelRestric">
      <summary>A restriction on deleting a Folder.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FolderDelRestricArray">
      <summary>An array of folder delete restrictions.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ForumService">
      <summary>Contains methods for posting messages.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.FreeTxtField">
      <summary>An section of text with variable values and size. Implementation of NumSchmField.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.GetPromoteOrderResults">
      <summary>Contains formation about the promote order when promoting or updating components</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.Group">
      <summary>Information about a collection of Users and/or Groups.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.GroupInfo">
      <summary>Extended information about a Group.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.IdentificationService">
      <summary>This service provides locations of the Data server and Filestore server.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.IdPair">
      <summary>A generic mapping from one ID value to another.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.InformationService">
      <summary>Contains methods to determine information about the server, such as the version and product level.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.Item">
      <summary>A version of an Item Revision</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ItemAssoc">
      <summary>A dependency between two Items</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ItemAssocParam">
      <summary>Item association parameters to use when updating Item BOM associations</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ItemAssocProp">
      <summary>Represents an Item Association Property (BOM Property)</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ItemAssocPropArray">
      <summary>Represents an array of ItemAssocProp</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ItemAttmt">
      <summary>A collection of attachments to an Item.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ItemBOM">
      <summary>Contains BOM related links between Items</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ItemBOMAndDiff">
      <summary>A comparison between BOMs.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ItemBOMCompareDiff">
      <summary>A single difference between two BOMs.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ItemBOMFileAssoc">
      <summary>An association from an Item BOM to a File.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ItemBOMOcc">
      <summary>An occurrence of an Item in the Item BOM.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ItemCat">
      <summary>Item category information</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ItemConflict">
      <summary>Information on a conflict during an Item operation.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ItemDelStatus">
      <summary>Information about a purge of Item versions.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ItemFileAssoc">
      <summary>A link between an Item and an assigned File.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ItemNum">
      <summary>A record of an Item number.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ItemRollbackLifeCycleStateCommandEventArgs">
      <summary>Information about the rollback Item operation</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ItemsAndFiles">
      <summary>A list of Item Revisions with links to Vault files.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ItemService">
      <summary>Contains methods for manipulating items.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.Job">
      <summary>A task that needs to be performed.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.JobParam">
      <summary>A parameter to hold meta data for a job.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.JobService">
      <summary>Contains methods for manipulating the job queue.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.KnowledgeLibraryHeader">
      <summary>Internal use only.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.KnowledgeVault">
      <summary>Contains information on a KnowledgeVault.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.KnowledgeVaultService">
      <summary>Contains methods for getting information about the vaults on the server.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.Label">
      <summary>A label acts as an identifier or marker for a point in time in the workflow.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.LfCycDef">
      <summary>The definition for a Life Cycle state.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.LfCycState">
      <summary>A Life Cycle State</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.LfCycTrans">
      <summary>A transition from one Life Cycle State to another.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.License">
      <summary>License information.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.LifeCycleService">
      <summary>
        <para>Contains methods related to the lifecycle behavior.</para>
      </summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.Lnk">
      <summary>An object in a Folder that points to another Entity.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.LongArray">
      <summary>An array of long integer values.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.Map">
      <summary>A mapping from one set of property IDs to another.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.MapPair">
      <summary>A property mapping.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.MoveFileCommandEventArgs">
      <summary>Information about the move File operation</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.MoveFolderCommandEventArgs">
      <summary>Information about the move Folder operation</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.Msg">
      <summary>A posted Message to a Forum.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.MsgGroup">
      <summary>A forum message and its attached files.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.NameValuePair">
      <summary>Represents a Name/Value pair construct</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.NumSchm">
      <summary>A description of a Numbering Scheme for an Item.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.NumSchmField">
      <summary>A field in the Numbering Scheme.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PackageService">
      <summary>Contains methods for importing and exporting item data.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.Permis">
      <summary>Represents a piece of Vault functionality that a user may or may not be able to use.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PkgAttmt">
      <summary>An attachment to a File.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PkgBOM">
      <summary>Information for an entry in the Bill of Materials (BOM)</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PkgItem">
      <summary>Information about an Item.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PkgItemsAndBOM">
      <summary>Item and BOM information for EDM system.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PkgProp">
      <summary>A user defined property.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PredefListField">
      <summary>A field that can only contain specific values. Implementation of NumSchmField.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.Preview">
      <summary>This information includes both item's intrinsic properties and user defined properties</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.Product">
      <summary>An installed piece of Autodesk Data Management server software.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ProductRestric">
      <summary>A restriction on an operation.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PromoteItemCommandEventArgs">
      <summary>Information about when an Item is created or updated from File data.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PropCompFail">
      <summary>A property compliance failure.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PropConstr">
      <summary>A property constraint.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PropConstrFail">
      <summary>A property constraint falure.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PropConstrFailure">
      <summary>Information on a violation of a property constraint.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PropDef">
      <summary>Contains information about the properties available in the Vault.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PropDefCond">
      <summary>A condition on a property definition</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PropDefInfo">
      <summary>A property definition object with related information.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PropEquivFail">
      <summary>A property equivalence failure. This occurrs when the value within the file does not match the mapped property.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PropertyService">
      <summary>Contains methods for manipulating properties on Entities.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PropInst">
      <summary>Contains information about the properties available in the Vault.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PropInstParam">
      <summary>Property instance parameters used for updating property values</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PropInstParamArray">
      <summary>Container for a collection of property instance parameters</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PropRestric">
      <summary>A restriction on a property operation.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PropWriteReq">
      <summary>For internal use only</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PropWriteResult">
      <summary>For internal use only</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.PropWriteResults">
      <summary>For internal use only</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ReplicationService">
      <summary>Contains methods for transfering ownerhsip between workgroups.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.Resolution">
      <summary>Information about a conflict resolution.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.RevDef">
      <summary>A revision definition.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.RevDefInfo">
      <summary>A collection of revision defintion objects and their corresponding sequences.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.RevisionService">
      <summary>Contains methods for manipulating revision values and schemes for Entities.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.RevLabel">
      <summary>A revision label.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.RevSeq">
      <summary>A revision sequence.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.Role">
      <summary>Contains information about roles assigned to a Vault user.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.Routing">
      <summary>Information regarding a Routing.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.RoutingMemb">
      <summary>A Routing member and their roles.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.RoutingMembAndRoles">
      <summary>A list of all Routing Members and all Roles available for a Routing.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.RoutingRoleDef">
      <summary>The definition of a Routing Role.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.RoutingUserRoles">
      <summary>A mapping between a User and Routing Roles.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.RuleSet">
      <summary>A set of property conditions.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.SecRestric">
      <summary>A security restriction.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.SecurityHeader">
      <summary>Security information about the current login.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.SecurityService">
      <summary>Contains methods for logging into and out of vaults and setting security on specific Entities.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ServerCfg">
      <summary>Various pieces of configuration information.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.ServerIdentities">
      <summary>The locations of the data server and filestore server.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.SiteCompat">
      <summary>Information regarding site compatibility.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.SrchCond">
      <summary>Represents a condition that must be met if a file is to be returned by a search.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.SrchSort">
      <summary>Sorting information when doing a search.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.SrchStatus">
      <summary>The status of the search.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.StateACE">
      <summary>An access control entry for a life cycle state..</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.StateACL">
      <summary>An access control list for a life cycle state.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.StringArray">
      <summary>An array of string values.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.TransACE">
      <summary>An access control entry for a life cycle state transition.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.TripleSysNamePair">
      <summary>A property triplicate value and the corresponding system name.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.UnitOfMeasure">
      <summary>A Unit of Measure.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.UpdateChangeOrderLifeCycleStateCommandEventArgs">
      <summary>Information about the update lifecycle Change Order operation</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.UpdateCustomEntityLifeCycleStateCommandEventArgs">
      <summary>Information about the update lifecycle customer entity operation<!--DXMETADATA end --></summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.UpdateFileLifeCycleStateCommandEventArgs">
      <summary>Information about the update lifecycle File operation</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.UpdateItemLifeCycleStateCommandEventArgs">
      <summary>Information about the update lifecycle Item operation</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.User">
      <summary>Object containing information about a Vault user.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.UserInfo">
      <summary>Contains a user's user and role information.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.WebServiceCommandEventArgs">
      <summary>The base class for Web Service Command event arguments.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.WebServiceCommandEvents`1">
      <summary>Base class for Web Service Command Event collections.</summary>
      <typeparam>The event args type.</typeparam>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.WebServiceInvokeEventArgs">
      <summary>Information on the web service method call.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.WinAuthService">
      <summary>Contains methods for logging into and out of vaults using Windows credentials.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.Wkgrp">
      <summary>A workgroup.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.WkgrpLabelField">
      <summary>A field that contains information about the workgroup that the client is currently logged into. Implementation of NumSchmField.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.Wmark">
      <summary>Information about a Watermark.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.Workflow">
      <summary>A workflow is a collection of states and activities that a Change Order can pass along.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.WorkflowInfo">
      <summary>Extended information for a Workflow.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.WorkflowState">
      <summary>A Workflow State.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServices.XferStatus">
      <summary>Transfer status.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AccessPermis.Id">
      <summary>A numeric value representing they type of operation. There are currently 3 types. 1=AllowRead, 2=AllowWrite, 3=AllowDelete</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AccessPermis.Val">
      <summary>True means that the operation is allowed. False means that the operation is not allowed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AccessPermisGrp.Descr">
      <summary>The description.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AccessPermisGrp.Id">
      <summary>The unique identifier for this object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AccessPermisGrp.Name">
      <summary>The display name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AccessPermisGrp.PermisArray">
      <summary>An array of permissions. It makes most sense to have 1 permission per access type.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ACE.PermisArray">
      <summary>An array of access permissions.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ACE.UserGrpId">
      <summary>The user or group ID who has the permissions.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ACL.ACEArray">
      <summary>A list of ACEs</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ACL.Id">
      <summary>A unique identifier for the ACL.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Activity.DispName">
      <summary>The display name of the activity.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Activity.Id">
      <summary>The Vault ID for the object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Activity.Name">
      <summary>A unique identifier string.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ActivityHist.DispName">
      <summary>The display name for the Activity.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ActivityHist.ExecTime">
      <summary>The date and time that the Activity was completed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ActivityHist.Id">
      <summary>The ID of the Activity that was executed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ActivityHist.StateDispName">
      <summary>The display name of the state that the Change Order was in when the Activity was invoked.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ActivityHist.StateId">
      <summary>The ID of the state that the Change Order was in when the Activity was invoked.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ActivityHist.UserId">
      <summary>The ID of the user who invoked the Activity.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ActivityHist.UserName">
      <summary>The name of the user who invoked the Activity.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AddChangeOrderCommandEventArgs.AddRestriction(Autodesk.Connectivity.Extensibility.Framework.ExtensionRestriction)">
      <summary>Adds a restriction, which blocks the command. This function should only be called during the GetRestrictions event.</summary>
      <param>The reason that the command cannot run.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AddChangeOrderCommandEventArgs.Guid">
      <summary>The unique identifier for the event.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AddChangeOrderCommandEventArgs.ReturnValue">
      <summary>The resulting Change Order.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AddChangeOrderCommandEventArgs.Status">
      <summary>The status of the command. Used in the Post event to see of the command was successful.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.AddChangeOrderCommandEventArgs.ApproveDeadline">
      <summary>The due date that the Change Order has to be approved by.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.AddChangeOrderCommandEventArgs.AssocProperties">
      <summary>An array of properties on the link between the ChangeOrder and linked entitity. The 'From ID' is the Change Order ID and the 'To ID' is the linked entity ID.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.AddChangeOrderCommandEventArgs.AttachmentIds">
      <summary>Files to be attached to the Change Order.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.AddChangeOrderCommandEventArgs.ChangeOrderNumber">
      <summary>A unique change order number.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.AddChangeOrderCommandEventArgs.Comments">
      <summary>A list of comments for the ChangeOrder.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.AddChangeOrderCommandEventArgs.Description">
      <summary>Change Order's description.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.AddChangeOrderCommandEventArgs.FileMasterIds">
      <summary>The Files to be tracked by the Change Order.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.AddChangeOrderCommandEventArgs.ItemMasterIds">
      <summary>The Items to be tracked by the Change Order.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.AddChangeOrderCommandEventArgs.NotifyEmails">
      <summary>An array of emails to send out upon completion.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.AddChangeOrderCommandEventArgs.Properties">
      <summary>Properties on the ChangeOrder.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.AddChangeOrderCommandEventArgs.RoutingId">
      <summary>The ID of the Routing.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.AddChangeOrderCommandEventArgs.Title">
      <summary>Change Order's title.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AddFileCommandEventArgs.AddRestriction(Autodesk.Connectivity.Extensibility.Framework.ExtensionRestriction)">
      <summary>Adds a restriction, which blocks the command. This function should only be called during the GetRestrictions event.</summary>
      <param>The reason that the command cannot run.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AddFileCommandEventArgs.Guid">
      <summary>The unique identifier for the event.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AddFileCommandEventArgs.ReturnValue">
      <summary>The added File.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AddFileCommandEventArgs.Status">
      <summary>The status of the command. Used in the Post event to see of the command was successful.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.AddFileCommandEventArgs.Associations">
      <summary>The attachments and dependencies.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.AddFileCommandEventArgs.Bom">
      <summary>The bill of materials to associate with the File.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.AddFileCommandEventArgs.Comment">
      <summary>The comment to be associated with version 1 of the File.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.AddFileCommandEventArgs.FileClassification">
      <summary>The classification of the File.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.AddFileCommandEventArgs.FileName">
      <summary>The File name.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.AddFileCommandEventArgs.FolderId">
      <summary>The Folder location of the new File.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.AddFileCommandEventArgs.Hidden">
      <summary>Metadata to indiciate if the client should display the File.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.AddFileCommandEventArgs.LastWrite">
      <summary>The last time the file had been modified on the local disk.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AddFolderCommandEventArgs.AddRestriction(Autodesk.Connectivity.Extensibility.Framework.ExtensionRestriction)">
      <summary>Adds a restriction, which blocks the command. This function should only be called during the GetRestrictions event.</summary>
      <param>The reason that the command cannot run.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AddFolderCommandEventArgs.Guid">
      <summary>The unique identifier for the event.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AddFolderCommandEventArgs.ReturnValue">
      <summary>The new Folder object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AddFolderCommandEventArgs.Status">
      <summary>The status of the command. Used in the Post event to see of the command was successful.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.AddFolderCommandEventArgs.FolderName">
      <summary>The name of the Folder.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.AddFolderCommandEventArgs.IsLibrary">
      <summary>Tells if the Folder is a library.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.AddFolderCommandEventArgs.ParentId">
      <summary>The Folder location where the new Folder is being added.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AddItemCommandEventArgs.AddRestriction(Autodesk.Connectivity.Extensibility.Framework.ExtensionRestriction)">
      <summary>Adds a restriction, which blocks the command. This function should only be called during the GetRestrictions event.</summary>
      <param>The reason that the command cannot run.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AddItemCommandEventArgs.Guid">
      <summary>The unique identifier for the event.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AddItemCommandEventArgs.ReturnValue">
      <summary>The new Item object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AddItemCommandEventArgs.Status">
      <summary>The status of the command. Used in the Post event to see of the command was successful.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.AddItemCommandEventArgs.CategoryId">
      <summary>The category for the Item.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.AddGroup(System.String,Autodesk.Connectivity.WebServices.AuthTyp,System.Boolean,System.String,System.Int64[],System.Int64[])">
      <summary>Creates a new Group of Users.</summary>
      <returns>The newly created group.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.AddGroupRole(System.Int64,System.Int64)">
      <summary>Adds a role to a group.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.AddGroupsToGroup(System.Int64[],System.Int64)">
      <summary>Adds a set of Groups to a parent Group.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.AddGroupToGroup(System.Int64,System.Int64)">
      <summary>Adds a Group to a parent Group.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.AddGroupToVault(System.Int64,System.Int64)">
      <summary>Gives a Group access to a Vault.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.AddUser(System.String,System.String,Autodesk.Connectivity.WebServices.AuthTyp,System.String,System.String,System.String,System.Boolean,System.Int64[],System.Int64[])">
      <summary>Adds a user.</summary>
      <returns>A User object with the newly created user data.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.AddUserRole(System.Int64,System.Int64)">
      <summary>Associates a user with a role.</summary>
      <returns>Nothing.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.AddUsersToGroup(System.Int64[],System.Int64)">
      <summary>Adds a set of Users to a Group.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.AddUserToGroup(System.Int64,System.Int64)">
      <summary>Adds a User to a Group.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.ClearCaches">
      <summary>Clears information cached by the server.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.DeleteGroupFromGroup(System.Int64,System.Int64)">
      <summary>Deletes a child Group from a Group.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.DeleteGroupFromVault(System.Int64,System.Int64)">
      <summary>Removes access to a Vault from a Group.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.DeleteGroupRole(System.Int64,System.Int64)">
      <summary>Removes a Role from a Group.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.DeleteGroupsFromGroup(System.Int64[],System.Int64)">
      <summary>Removes child Groups from a Group.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.DeleteUserFromGroup(System.Int64,System.Int64)">
      <summary>Deletes a User from a Group.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.DeleteUserRole(System.Int64,System.Int64)">
      <summary>Removes a role from a user.</summary>
      <returns>Nothing.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.DeleteUsersFromGroup(System.Int64[],System.Int64)">
      <summary>Deletes a set of Users from a Group.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.GetAllGroups">
      <summary>Gets a list of all Groups on the server.</summary>
      <returns>A list of all the Groups.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.GetAllRoles">
      <summary>Gets a list of all user roles.</summary>
      <returns>An array of all the roles recognized by the Vault.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.GetAllUsers">
      <summary>Gets a list of all users.</summary>
      <returns>An array listing all the users in the Vault.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.GetChildGroupIdsByGroupId(System.Int64)">
      <summary>Gets all the child groups for a given group</summary>
      <returns>The IDs for all the child groups recursive.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.GetCreateDomainUserOnLogin">
      <summary>Tells if the server is set up to automatically create domain users when they attempt to log in for the first time.</summary>
      <returns>If true, then the server will automatically create a domain user the first time they attempt to log in. If false, domain users must be created by and
administrator before logging in is possible.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.GetGroupById(System.Int64)">
      <summary>Gets a group by its ID.</summary>
      <returns>The group object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.GetGroupByName(System.String)">
      <summary>Gets a group by its name.</summary>
      <returns>The Group object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.GetGroupInfoByGroupId(System.Int64)">
      <summary>Gets a Group and related information.</summary>
      <returns>The Group object with extended information.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.GetGroupInfosByGroupIds(System.Int64[])">
      <summary>Gets a set Groups and related information.</summary>
      <returns>An array of Group objects with extended information.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.GetGroupsByIds(System.Int64[])">
      <summary>Gets a set of groups based on their IDs.</summary>
      <returns>An array of Group objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.GetGroupsByNames(System.String[])">
      <summary>Gets a list of groups.</summary>
      <returns>An array of Group objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.GetMemberGroupsByGroupId(System.Int64)">
      <summary>Gets the immediate child Groups for a given Group.</summary>
      <returns>An array of all immediate child Groups.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.GetMemberUsersByGroupId(System.Int64)">
      <summary>Gets all the immediate child Users of the given Group.</summary>
      <returns>An array of all immediate child Users.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.GetParentGroupIdsByGroupId(System.Int64)">
      <summary>Gets the IDs of all the groups that a user or group belongs to.</summary>
      <returns>The IDs for all the parent groups recursive.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.GetPermissionsByRoleId(System.Int64)">
      <summary>Gets the permissions for a role.</summary>
      <returns>An array listing permissions that the role has.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.GetPermissionsByUserId(System.Int64)">
      <summary>Gets the permissions for a user.</summary>
      <returns>An array listing the permissions the user has. This includes any permissions that are granted by being part of a group.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.GetRolesByUserId(System.Int64)">
      <summary>Gets the roles for a user.</summary>
      <returns>An array listing the roles the user has.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.GetServerConfiguration">
      <summary>Gets various peices of configuration information.</summary>
      <returns>Various pieces of configuration information.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.GetUserByUserId(System.Int64)">
      <summary>Gets the user object associated with the specified userId.</summary>
      <returns>A User object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.GetUserInfoByUserId(System.Int64)">
      <summary>Gets the user information for the specified user ID.</summary>
      <returns>An object containing the user information.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.GetUserInfosByUserIds(System.Int64[])">
      <summary>Gets a set of user information for a set of user IDs.</summary>
      <returns>An array of user information.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.SetCreateDomainUserOnLogin(System.Boolean)">
      <summary>Turns on or off the feature that allows domain users to be automatically created when a user first logs in.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.UpdateGroupInfo(System.Int64,System.String,Autodesk.Connectivity.WebServices.AuthTyp,System.String,System.Boolean,System.Int64[],System.Int64[],System.Int64[],System.Int64[])">
      <summary>Updates a group and its associations.</summary>
      <returns>The updated GroupInfo object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.UpdatePassword(System.Int64,System.String)">
      <summary>Changes the password for a user.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.UpdateUser(System.Int64,System.String,System.String,System.String)">
      <summary>Updates the basic data for a user.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AdminService.UpdateUserInfo(System.Int64,System.String,Autodesk.Connectivity.WebServices.AuthTyp,System.String,System.String,System.String,System.Boolean,System.Int64[],System.Int64[])">
      <summary>Updates a User and its associations.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AdminService.AllowInvokeEvents">
      <summary>Tells if invoke events should fire. This value is true by default.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AdminService.SecurityHeader">
      <summary>The security header. (from IWebService)</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AdminService.WebServiceManager">
      <summary>A reference to the WebServiceManager that created this object. Value may be null.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.AdminService.PostInvokeEvents">
      <summary>Subscribe to this event to get a notification after every web service call.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.AdminService.PreInvokeEvents">
      <summary>Subscribe to this event to get a notification before every web service call.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AssocPropDef.AssocPropTyp">
      <summary>The type of association the property applies to.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AssocPropDef.DfltVal">
      <summary>A default value. If a property of this type is added and a value is not specified, the default value is used.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AssocPropDef.DispName">
      <summary>The display name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AssocPropDef.Id">
      <summary>The unique identifier.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AssocPropDef.IsAct">
      <summary>If true, the property definition is active. If false, it is not active.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AssocPropDef.SysName">
      <summary>The system name. This value is unique.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AssocPropDef.Typ">
      <summary>The data type.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AssocPropDef.UsageCount">
      <summary>Deprecated - do not use.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AssocPropDefInfo.EntClassCtntSrcPropCfgArray">
      <summary>The content source mappings for each entity class.<!--DXMETADATA end --></summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AssocPropDefInfo.ListValArray">
      <summary>A list of legal values. If null then all values are allowed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AssocPropDefInfo.PropDef">
      <summary>The association property definition.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AssocPropItem.FromId">
      <summary>An ID of the 'from' Entity.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AssocPropItem.Id">
      <summary>The ID for the object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AssocPropItem.PropDefId">
      <summary>The property definition</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AssocPropItem.ToId">
      <summary>An ID of the 'to' Entity.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AssocPropItem.Val">
      <summary>The value of the property.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AssocPropItem.ValTyp">
      <summary>The data type of Val.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Attmt.FileId">
      <summary>The Vault ID of the File.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Attmt.Pin">
      <summary>If true, the attachment is pinned to a specific version of a File. If false, the attachment is not pinned, which means the Item is always updated to a specific
version of a File.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AttmtConflict.IsSame">
      <summary>If true, the attachment is the same as an existing attachment.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AuthService.GetLicense">
      <summary>Gets the license information for the server.<!--DXMETADATA end --></summary>
      <returns>An object containing license information.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AuthService.SignIn(System.String,System.String,System.String,System.String)">
      <summary>Authenticates to a specific Vault via username and password.<!--DXMETADATA end --></summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AuthService.SignIn2(System.String,System.String,System.String)">
      <summary>Authenticates to the server but not to a specific Vault database.<!--DXMETADATA end --></summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AuthService.SignInAltLicense(System.String,System.String,System.String,System.String,System.Byte[])">
      <summary>Reserved for future use<!--DXMETADATA end --></summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AuthService.SignInReadOnly(System.String,System.String,System.String,System.String)">
      <summary>Authenticates to a specific Vault with read only permissions.<!--DXMETADATA end --></summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.AuthService.SignOut">
      <summary>Clears out the authentication information.<!--DXMETADATA end --></summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AuthService.AllowInvokeEvents">
      <summary>Tells if invoke events should fire. This value is true by default.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AuthService.SecurityHeader">
      <summary>The security header. (from IWebService)</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AuthService.WebServiceManager">
      <summary>A reference to the WebServiceManager that created this object. Value may be null.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.AuthService.PostInvokeEvents">
      <summary>Subscribe to this event to get a notification after every web service call.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.AuthService.PreInvokeEvents">
      <summary>Subscribe to this event to get a notification before every web service call.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AutogenField.FieldTyp">
      <summary>The type of field. Inherited from NumSchmField.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AutogenField.From">
      <summary>The starting number.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AutogenField.Len">
      <summary>The number of characters this field can take up.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AutogenField.Name">
      <summary>The name of the field.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AutogenField.StepSize">
      <summary>The amount to increment for each new number.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AutogenField.To">
      <summary>The maximum number.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.AutogenField.ZeroPadding">
      <summary>If true, the field will pad the value with zeros to take up the entire length (ex. 0005). If false, the number will not be padded (ex. 5).</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.BehaviorService.GetBehaviorConfigurationsByNames(System.String,System.String[])">
      <summary>Gets a set of behavior configuration object based on their names.</summary>
      <returns>An array of corresponding behavrior configuration objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.BehaviorService.UpdateBehaviorAssignmentTypes(System.String,System.String,Autodesk.Connectivity.WebServices.AllowNoBehavior,System.Int64[],Autodesk.Connectivity.WebServices.BehaviorAssignmentType[],System.Boolean)">
      <summary>Updates the behavior classes for specific behavior objects.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BehaviorService.AllowInvokeEvents">
      <summary>Tells if invoke events should fire. This value is true by default.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BehaviorService.SecurityHeader">
      <summary>The security header. (from IWebService)</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BehaviorService.WebServiceManager">
      <summary>A reference to the WebServiceManager that created this object. Value may be null.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.BehaviorService.PostInvokeEvents">
      <summary>Subscribe to this event to get a notification after every web service call.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.BehaviorService.PreInvokeEvents">
      <summary>Subscribe to this event to get a notification before every web service call.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Bhv.AssignTyp">
      <summary>Information about how the behavior is assigned.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Bhv.Descr">
      <summary>The description.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Bhv.DisplayName">
      <summary>The display name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Bhv.EntClassIdArray">
      <summary>An array of entity classes associated with the behavior.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Bhv.Id">
      <summary>The unique identifier for the object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Bhv.Name">
      <summary>The system name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BhvCfg.AllowNone">
      <summary>The "None" behavior is allowed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BhvCfg.BhvArray">
      <summary>An array of behaviors.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BhvCfg.Name">
      <summary>The unique name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BhvDelRestric.Code">
      <summary>The restriction code.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BlkThres.MaxThres">
      <summary>The maximum number of files to be bulk processed in a single operations. Clients should not allow file operations above this threshold.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BlkThres.WarnThres">
      <summary>The warning threshold. For operations that do bulk operations on a number of files above this value, client applications should display a warning to users.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOM.CompArray">
      <summary>Array of BOM Components.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOM.CompAttrArray">
      <summary>Array of Component Attributes.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOM.CompRefDsgArray">
      <summary>Array of links between Components and Reference Designators.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOM.InstArray">
      <summary>Array of Instances of BOM Components.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOM.PropArray">
      <summary>Array of Properties.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOM.RefDsgArray">
      <summary>Array of Reference Designators</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOM.RefDsgAttrArray">
      <summary>Array of Reference Designator Attributes.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOM.SchmArray">
      <summary>Array of BOM Schemes.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOM.SchmOccArray">
      <summary>Array of Occurrences for the Schemes</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMComp.BaseQty">
      <summary>A numeric value quantifying a single instance of the Component. For Components with discreet quantities, “1” should always be used. For measured quantities, a
positive decimal value can be used. For example, a rod cut to 3.5 feet would have a BaseUOM of “foot” and a BaseQty of “3.5”.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMComp.BaseUOM">
      <summary>The Unit of Measure the Component is quantified with. For discreet quantities, “Each” should be used. For measured quantities, a length (inch, cm, foot, etc.)
volume (ounce, ml, liter, quart, etc.) or mass (gram, pound, kg, etc.) unit of measure should be used.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMComp.BOMStruct">
      <summary>An enumerated value representing the BOMStructure value of Component.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMComp.Cloaked">
      <summary>Indicates if a BOM component is cloaked</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMComp.CompTyp">
      <summary>An enumerated value specifying the type of component.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMComp.Id">
      <summary>A unique ID within the BOM for this component. The root component in each BOM must have an ID value of 1.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMComp.Name">
      <summary>The name of this component.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMComp.UniqueId">
      <summary>Unique data within the scope of the design file used to identify the Component. For Inventor files, the GUID used to identify the Component is used (or GUID
plus VirtualComponentDefinition._PropertySetsId for Virtual components). For DWG files, the HandleID associated with the Component is used.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMComp.XRefId">
      <summary>For Components with a XRefType of “external”, XRefID represents the FileID of the design file associated with that component. For “internal” Components, XRefID
should be set to -1.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMComp.XRefTyp">
      <summary>An enumerated value that specifies whether the Component is internal or external in relation to the design file.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMCompareOccDiff.DiffType">
      <summary>The type of difference. There will only be a single value in this context; values will NOT be added together.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMCompareOccDiff.OccId">
      <summary>The ID of the occurrence that contains the difference. The ID will be either the primary or secondary occurrence depending on the DiffType.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMCompareOccDiff.RootItemIterId">
      <summary>The Item ID at the root of the BOM. The ID will be either the primary or secondary occurrence depending on the DiffType.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMCompareOccDiff.Val1">
      <summary>Information about the difference. The content is dependant on the DiffType.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMCompareOccDiff.Val2">
      <summary>Information about the difference. The content is dependant on the DiffType.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMCompAttr.CompId">
      <summary>The Id of the component that this attribute applies to.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMCompAttr.Id">
      <summary>A unique ID for this component attribute.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMCompAttr.PropId">
      <summary>The Id of the property definition that this attribute applies to.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMCompAttr.Val">
      <summary>The value of the attribute.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMCompRefDesig.Cnt">
      <summary>A count of the instances that the designator applies to.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMCompRefDesig.CompId">
      <summary>The ID of the BOM Component.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMCompRefDesig.Id">
      <summary>A unique identifier for the object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMCompRefDesig.RefId">
      <summary>The ID of the Reference Designator.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMInst.BOMStructOverde">
      <summary>An enumerated value that indicates that the BOMStructure for this instance of the child component has been overridden. Default should be used on instances
without an override. Reference should be used to indicate, regardless of the BOMStructure defined on the component, the instance of the child component should
be considered as reference. In order to support skeleton assembly and related construction geometry workflows, an instance of a particular Component can be
overridden to Reference.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMInst.CldId">
      <summary>This component ID of the child part or assembly.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMInst.Id">
      <summary>The Id of this instance.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMInst.ParId">
      <summary>The component ID of the parent assembly.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMInst.Quant">
      <summary>The number of occurrences of the child within the parent.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMInst.QuantOverde">
      <summary>A non-negative value used to represent the BOM Quantity Override for the child Component within the parent. When this is provided the value overrides the
calculated total quantity. QuantOverde should only be set on an Instance element where BOMStructOverde is set to “Default”. If there is no Quantity Override,
the value should be set to "-1".</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMProp.DispName">
      <summary>The display name of the property.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMProp.Id">
      <summary>A unique ID for this property.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMProp.Moniker">
      <summary>The unique identifier for the property within the content source.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMProp.Name">
      <summary>The friendly name of the property.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMProp.PropId">
      <summary>The PropID of the property that identifies it within the Property Set.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMProp.PropSetId">
      <summary>The GUID PropertySetID for the Property Set this property is in.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMProp.Typ">
      <summary>An enumerated value indicating the data type of this property.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMRefDesig.Id">
      <summary>A unique identifier for the object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMRefDesig.Inst">
      <summary>The installation.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMRefDesig.Loc">
      <summary>The location.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMRefDesig.Tag">
      <summary>The tag.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMRefDesigAttr.Id">
      <summary>A unique ID.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMRefDesigAttr.PropId">
      <summary>The ID of the BOM Property.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMRefDesigAttr.RefId">
      <summary>The ID of the Reference Designator.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMRefDesigAttr.Val">
      <summary>The value of the BOM Property</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMSchm.Id">
      <summary>A unique ID for this scheme.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMSchm.InsepAsPrt">
      <summary>Indicates whether assemblies with a BOMStructure of "Inseperable" should be treated as a single part in a PartsOnly scheme. When true, the inseparable assembly
is treated as a single part and its children are ignored. When false, the children are included in the Parts Only view. In a “Structured” scheme, InsepAsPrt
should always be set to "false".</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMSchm.InsepPrmtsPrch">
      <summary>A value of true indicates that, in an assembly with a BOMStructure of "Inseperable", Components with a BOMStructure of "Purchased" are considered independent
of the parent assembly. A value of false indicates that purchased children should be considered inseparable children of inseparable assemblies. In a
"Structured" scheme, InsepPrmtsPrch should always be set to "false".</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMSchm.PrchAsPrt">
      <summary>Indicates whether assemblies with a BOMStructure of "Purchased" should be treated as a single part. In a "Structured" scheme, PrchAsPrt should always be set to
"false".</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMSchm.RootCompId">
      <summary>The root component of the scheme.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMSchm.SchmTyp">
      <summary>An enumerated value indicating the type of scheme. Only Structured is currently supported.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMSchmOccur.CompId">
      <summary>The BOM Component ID for this occurrence.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMSchmOccur.Depth">
      <summary>The number of levels away from the root of the scheme.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMSchmOccur.DtlId">
      <summary>A tag for the occurrence.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMSchmOccur.Id">
      <summary>A unique identifier</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMSchmOccur.Order">
      <summary>The order of the occurrence. 1 is the lowest order number and will be listed first.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMSchmOccur.ParOccurId">
      <summary>The parent BOMSchmOccur ID.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.BOMSchmOccur.SchmId">
      <summary>The ID of the BOM Scheme</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ByteArray.Bytes">
      <summary>The data being transferred.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Cat.Color">
      <summary>The color of the category. It is a 32-bit ARGB value.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Cat.Descr">
      <summary>The description of the category.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Cat.EntClassIdArray">
      <summary>An array of associated entity class IDs.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Cat.Id">
      <summary>The unique identifier for the object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Cat.Name">
      <summary>The name of the category.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Cat.SysName">
      <summary>The system name of the category.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CatCfg.BhvCfgArray">
      <summary>Related behavior objects.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CatCfg.Cat">
      <summary>The category object.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CategoryService.AddCategory(System.String,System.String,System.String,System.Int32,System.String,Autodesk.Connectivity.WebServices.BehaviorAssignmentType)">
      <summary>Adds a new Category</summary>
      <returns>The resulting Category object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CategoryService.ApplyCategoryRules(System.Int64[])">
      <summary>Applys category rules to a set of entities.</summary>
      <returns>The corresponding category ID for each entity based on the category rules. A 0 value means that the entity does not belong to a category.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CategoryService.CopyCategoryConfiguration(System.Int64,System.String,System.Int32,System.String,Autodesk.Connectivity.WebServices.BehaviorAssignmentType)">
      <summary>Creates a new categroy configuration using an existing one as a template.</summary>
      <returns>The new category object with related information.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CategoryService.DeleteCategory(System.Int64)">
      <summary>Deletes a category.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CategoryService.GetAllCategoryColors">
      <summary>Gets all category colors in the system.</summary>
      <returns>Gets all category colors in the system.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CategoryService.GetCategoriesByEntityClassId(System.String,System.Boolean)">
      <summary>Gets all the categories for a given entity class.</summary>
      <returns>An array of all categories for the entity class.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CategoryService.GetCategoryById(System.Int64)">
      <summary>Gets a category based on its ID.</summary>
      <returns>The matching category object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CategoryService.GetCategoryConfigurationById(System.Int64,System.String[])">
      <summary>Gets a category and related behaviours by a category ID.</summary>
      <returns>The matching category object and behaviors.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CategoryService.GetCategoryConfigurationsByBehaviorNames(System.String,System.Boolean,System.String[])">
      <summary>Gets a set of categories and their behaviors.</summary>
      <returns>An array of categories and their behaviors.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CategoryService.GetCategoryDeleteRestrictionsById(System.Int64)">
      <summary>Gets any delete restrictions related to a given category.</summary>
      <returns>Any related delete restrictions or null of there are no restrictions.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CategoryService.GetCategoryIdsByEntityMasterIds(System.String,System.Int64[])">
      <summary>Gets the categories for a set of entities.</summary>
      <returns>The corresponding category IDs.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CategoryService.GetCategoryRuleSetConfiguration(System.String)">
      <summary>Gets the rules that determine which category gets assigned to an entity.</summary>
      <returns>An object containing the category rule sets.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CategoryService.UpdateCategoriesBehaviorAssignmentTypes(System.String,System.Int64,System.Int64[],Autodesk.Connectivity.WebServices.BehaviorAssignmentType[])">
      <summary>Updates how a behavior relates to a set of categories.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CategoryService.UpdateCategoriesPropertyDefinitionAssignmentTypes(System.Int64,System.Int64[],Autodesk.Connectivity.WebServices.BehaviorAssignmentType[],Autodesk.Connectivity.WebServices.PropConstr[])">
      <summary>Updates how a property definition works with a set of categories.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CategoryService.UpdateCategory(System.Int64,System.String,System.Int32,System.String,Autodesk.Connectivity.WebServices.BehaviorAssignmentType)">
      <summary>Updates category information.</summary>
      <returns>The updated category object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CategoryService.UpdateCategoryBehaviorAssignmentTypes(System.Int64,System.String,Autodesk.Connectivity.WebServices.AllowNoBehavior,System.Int64[],Autodesk.Connectivity.WebServices.BehaviorAssignmentType[],System.Boolean)">
      <summary>Update the behaviors for a category.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CategoryService.UpdateCategoryRuleSetConfiguration(System.String,Autodesk.Connectivity.WebServices.CatRuleSetCfg)">
      <summary>Updates the rules that determine how files get associated to a category.</summary>
      <returns>The updated category rule set configuration object.</returns>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CategoryService.AllowInvokeEvents">
      <summary>Tells if invoke events should fire. This value is true by default.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CategoryService.SecurityHeader">
      <summary>The security header. (from IWebService)</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CategoryService.WebServiceManager">
      <summary>A reference to the WebServiceManager that created this object. Value may be null.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.CategoryService.PostInvokeEvents">
      <summary>Subscribe to this event to get a notification after every web service call.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.CategoryService.PreInvokeEvents">
      <summary>Subscribe to this event to get a notification before every web service call.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CatRuleSet.CatId">
      <summary>The category ID.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CatRuleSet.IsAct">
      <summary>If true, the rule set is active. If false, the rule set is not active.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CatRuleSet.RuleSet">
      <summary>The rule set object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CatRuleSetCfg.ApplyToNew">
      <summary>If true, the rule sets get applied to new files.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CatRuleSetCfg.RuleSetArray">
      <summary>A collection of categories and their rule sets.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrder.ActivityArray">
      <summary>A list of activities that the current user can perform on the Change Order in its current state.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrder.CreateDate">
      <summary>The date and time the Change Order was created.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrder.CreateUserId">
      <summary>The ID of the user that created the Change Order.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrder.CreateUserName">
      <summary>The name of the user that created the Change Order.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrder.Deadline">
      <summary>The date and time when the Change Order needs to be approved by.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrder.Descr">
      <summary>A description of the Change Order</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrder.Id">
      <summary>The unique identifier for the object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrder.LastModDate">
      <summary>The last time the change order was modified.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrder.Locked">
      <summary>Tells if the Change Order can be modified by the logged in user.<!--DXMETADATA end --></summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrder.Num">
      <summary>The Change Order number. Numbers are generated based on the Numbering Scheme.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrder.StateDispName">
      <summary>The display name of the state.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrder.StateEntered">
      <summary>The date and time that the state was set.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrder.StateId">
      <summary>The state ID.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrder.StateName">
      <summary>The name of the state.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrder.SubmitDate">
      <summary>The date and time that the Change Order was last submitted. This value is meaningless if SubmitUserId is -1.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrder.SubmitUserId">
      <summary>The ID of the user who submitted the Change Order. This value will be -1 if the Change Order has not been submitted.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrder.SubmitUserName">
      <summary>The name of the user who last submitted the Change Order. This value is meaningless if SubmitUserId is -1.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrder.Title">
      <summary>The title of the Change Order.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderEntAssoc.ChangeOrderId">
      <summary>The Change Order.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderEntAssoc.EntIdArray">
      <summary>The Entities that are associated with the Change Order.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderFile.ChangeOrder">
      <summary>A Change Order object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderFile.FileId">
      <summary>The ID of the associated File.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderGrp.AttmtIdArray">
      <summary>An array of file IDs indicating the attached files.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderGrp.ChangeOrder">
      <summary>A Change Order object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderGrp.FileIdArray">
      <summary>An array of file IDs indicating the files being tracked by the Change Order.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderGrp.ForumId">
      <summary>The associated Forum.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderGrp.ItemAllowEditArray">
      <summary>If true, the corresponding Item allows edits.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderGrp.ItemIdArray">
      <summary>An array of Items associated with the Change Order. If the Change Order is active, this value will be the latest Iteration. If the Change Order is inactive,
the value will be the latest version at the time the Change Order was inactivated.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderGrp.Routing">
      <summary>The Routing that the Change Order was created with.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderGrp.Workflow">
      <summary>Information about the Workflow.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderItem.ChangeOrder">
      <summary>A Change Order object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderItem.ItemId">
      <summary>The ID of the associated Item Revision.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderLite.Deadline">
      <summary>The date and time when the Change Order needs to be approved by.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderLite.Descr">
      <summary>A description of the Change Order.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderLite.Id">
      <summary>The unique identifier for the object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderLite.LastModDate">
      <summary>The last time the change order was modified.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderLite.Num">
      <summary>The Change Order number. Numbers are generated based on the Numbering Scheme.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderLite.Title">
      <summary>The title of the Change Order.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderNumSchm.Desc">
      <summary>A description of the scheme.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderNumSchm.FieldLen">
      <summary>The number of digits for the number. The number will always fill up the FieldLen (ex. for a length of 4, a number might be '0005').</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderNumSchm.Id">
      <summary>The unique ID for the object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderNumSchm.IsDflt">
      <summary>If true, this scheme is the default scheme. If false, this scheme is not the default.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderNumSchm.IsInUse">
      <summary>If true, the scheme is in use by at least one change order. If false, no change orders are using the scheme.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderNumSchm.Name">
      <summary>The unique name of the scheme.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderNumSchm.StartNum">
      <summary>The starting number for the scheme.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderNumSchm.WkgrpLabelIncl">
      <summary>If true, the label of the currently connected to workgroup is included in the number. If false, the workgroup label is not used.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderParticipant.Id">
      <summary>The User ID. See Admin Service.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderParticipant.Name">
      <summary>The User name.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.ActivateNumberingSchemes(System.Int64[])">
      <summary>Activates numbering schemes so that they can be used to generate Change Order numbers.</summary>
      <returns>A list of updated numbering schemes.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.AddChangeOrder(System.Int64,System.String,System.String,System.String,System.DateTime,System.Int64[],System.Int64[],System.Int64[],Autodesk.Connectivity.WebServices.PropInst[],Autodesk.Connectivity.WebServices.AssocPropItem[],Autodesk.Connectivity.WebServices.MsgGroup[],Autodesk.Connectivity.WebServices.Email[])">
      <summary>Create a new change order.</summary>
      <returns>The newly created Change Order.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.AddComment(System.Int64,Autodesk.Connectivity.WebServices.MsgGroup[],Autodesk.Connectivity.WebServices.Email[])">
      <summary>Adds comments or replies to a change order</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.AddMarkup(System.Int64,System.Int64[],Autodesk.Connectivity.WebServices.MsgGroup[],Autodesk.Connectivity.WebServices.Email[])">
      <summary>Adds a markup to the specified Change Order. This function can also include additional file attachments, deleted file attachments and any comments added as
part of the markup.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.AddNumberingScheme(System.String,System.String,System.Boolean,System.Int32,System.Int32,System.Boolean)">
      <summary>Adds a new Numbering Scheme for Change Orders.</summary>
      <returns>The newly added numbering scheme.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.AddRouting(System.Int64,System.String,System.Boolean,Autodesk.Connectivity.WebServices.RoutingUserRoles[])">
      <summary>Adds a new Routing.</summary>
      <returns>The newly created Routing object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.CancelChangeOrderActivity(System.Int64,System.Int64)">
      <summary>Cancel a change order that has not been completed.</summary>
      <returns>Returns the targeted change order.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.CheckMessageEmailEnabled">
      <summary>Gets the setting indicating if email events are enabled.</summary>
      <returns>True means that emails are enabled. False means that emails are disabled.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.CommitChangeOrderActivity(System.Int64,System.Int64,System.Int64,System.DateTime,Autodesk.Connectivity.WebServices.MsgGroup[],Autodesk.Connectivity.WebServices.Email[])">
      <summary>Finish a change order activity.</summary>
      <returns>Returns the targeted change order.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.CommitChangeOrderCloseActivity(System.Int64,System.Int64,System.Int64,System.DateTime,Autodesk.Connectivity.WebServices.MsgGroup[],Autodesk.Connectivity.WebServices.Email[])">
      <summary>Finish a Change Order activity. To be used when closing a Change Order.</summary>
      <returns>Returns the targeted ChangeOrder.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.DeactivateNumberingSchemes(System.Int64[])">
      <summary>Deactivates numbering schemes.</summary>
      <returns>The updated numbering scheme.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.DeleteChangeOrder(System.Int64)">
      <summary>Deletes a Change Order</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.DeleteChangeOrderPropertyDefinitions(System.Int64[])">
      <summary>Deletes a set of user-defined item property definitions.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.DeleteChangeOrders(System.Int64[])">
      <summary>Deletes a set of change orders.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.DeleteChangeOrdersUnconditional(System.Int64[])">
      <summary>Deletes a set of Change Orders, regardless of its state.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.DeleteNumberingScheme(System.Int64)">
      <summary>Deletes a Numbering Scheme.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.DeleteRouting(System.Int64)">
      <summary>Deletes a Routing.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.EditChangeOrder(System.Int64)">
      <summary>Reserves the Change Order to the current user so that it can be edited.</summary>
      <returns>Returns a new editable Change Order.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.FindChangeOrdersBySearchConditions(Autodesk.Connectivity.WebServices.SrchCond[],Autodesk.Connectivity.WebServices.SrchSort[],System.String@,Autodesk.Connectivity.WebServices.SrchStatus@)">
      <summary>Find change orders based on a set of property search conditions.</summary>
      <returns>An array of all matching change orders or null if there were no matches.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.GetActivityHistoryByChangeOrderId(System.Int64)">
      <summary>Retrieves a list of all activities that have occurred for a Change Order.</summary>
      <returns>An array of all ActivityHistory objects for selected change order.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.GetAllActiveWorkflows">
      <summary>Gets all of the active Workflows.</summary>
      <returns>An array of all active Workflows.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.GetAllChangeOrderAssociationPropertyDefinitions">
      <summary>Gets all property definitions related to Change Order associations.</summary>
      <returns>All property definitions related to Change Order associations.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.GetAllChangeOrderParticipants">
      <summary>Gets all Users that can participate in Change Order activities.</summary>
      <returns>All Participants on the server.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.GetAssociationsByChangeOrderIDs(System.Int64[],System.String)">
      <summary>Gets the Entities associated with a Change Order.</summary>
      <returns>The resulting collections of Change Order associations.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.GetChangeOrderAssociationProperties(System.Int64[],System.Int64[],System.Int64[])">
      <summary>Gets the properties for a Change Order association.</summary>
      <returns>The resulting properties on the Item Associations. The 'From ID' is the Change Order ID and the 'To ID' is the Item ID.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.GetChangeOrderByNumber(System.String)">
      <summary>Retrieves a Change Order object when given a Change Order number.</summary>
      <returns>The change order object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.GetChangeOrderFilesByFileMasterId(System.Int64)">
      <summary>Gets the Change Orders which are driving a given file.</summary>
      <returns>An array of associated change orders and related file ID.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.GetChangeOrderGroupByChangeOrderId(System.Int64)">
      <summary>Retrieves a Change Order and its associations.</summary>
      <returns>The Change Order and its associations.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.GetChangeOrderItemsByItemMasterId(System.Int64)">
      <summary>Retrieves a list of Change Orders associated with an Item.</summary>
      <returns>An array of all Change Orders and associated revisions for the specified Item.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.GetChangeOrderLiteWorklistForCurrentUser">
      <summary>Gets the worklist for the current user.</summary>
      <returns>An array of partial change order objects that make up the current user's worklist.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.GetChangeOrderMarkupFolderConfiguration">
      <summary>Gets the configuration of the Change Order markup folder.</summary>
      <returns>The configuration of the Change Order markup folder.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.GetChangeOrderMarkupFolderId">
      <summary>Gets the ID of the Folder which stores markup data.</summary>
      <returns>The ID of the markup Folder.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.GetChangeOrderNumberBySchemeId(System.Int64)">
      <summary>Creates a unique Change Order number based on a numbering scheme.</summary>
      <returns>The newly generated change order number.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.GetChangeOrdersByIds(System.Int64[])">
      <summary>Gets a set of change order objects by their IDs.</summary>
      <returns>The matching change order objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.GetChangeOrderWorklistForCurrentUser">
      <summary>Retrieve a list of all change orders with an Activity that the logged in user can perform.</summary>
      <returns>A list of all change orders with an Activity that the logged in user can perform.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.GetDefaultWorkflow">
      <summary>Gets the default Workflow.</summary>
      <returns>The default Workflow.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.GetDisallowedItemLifeCycleStatesForSubmitActivities">
      <summary>Gets the IDs of the lifecycle states that are configured to prevent change orders from moving out of work state</summary>
      <returns>array of lifecycle state IDs</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.GetDisallowedLifeCycleStatesForSubmitActivitiesEnabled(System.String)">
      <summary>Gets the value for the Change Order Vault Setting "Restrict Change Orders from moving out of Work state based on File and Item Lifecycle states"</summary>
      <returns>Whether or not moving out of work state is restricted for the given entity class</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.GetEmailBodyByChangeOrderId(System.Int64,System.String,System.String,System.Boolean)">
      <summary>Gets the email body for a change order.</summary>
      <returns>The email body text.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.GetJobTypesByChangeOrderStateTransitionIds(System.Int64[],System.Int64[])">
      <summary>Gets the custom jobs that get fired for a set of transitions.</summary>
      <returns>An array of arrays of strings telling the jobs fired during the transitions. The top level array matches the size of the input arrays.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.GetLifecycleRollbackTargetItem(System.Int64,System.Int64)">
      <summary>Gets the target item for rolling back an item's lifecycle state from within the context of a change order.</summary>
      <returns>the rollback target item</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.GetNumberingSchemesByType(Autodesk.Connectivity.WebServices.NumSchmType)">
      <summary>Retrieves all the Numbering Schemes for the given Numbering Scheme Type.</summary>
      <returns>An array of Numbering Scheme objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.GetRoutingMembersByChangeOrderId(System.Int64)">
      <summary>Retrieves Routing Members object for a given change order ID.</summary>
      <returns>A set of routing members and their roles.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.GetRoutingMembersByRoutingId(System.Int64)">
      <summary>Retrieves Routing Members object for a given Routing ID.</summary>
      <returns>Returns the routing members and their roles object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.GetRoutingRoleDefinitionsByWorkflowId(System.Int64)">
      <summary>Gets the Routing Role Definitions for a given Workflow.</summary>
      <returns>An array of the Routing Role Definitions for the Workflow.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.GetRoutingsByWorkflowId(System.Int64)">
      <summary>Gets the Routings for a given Workflow.</summary>
      <returns>The Routings for the Workflow.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.GetUnreleasedChildMasterIDsByItemMasterIDs(System.Int64,System.Int64[],System.Boolean)">
      <summary>Returns a list of child Item Master IDs which are the unreleased children of the specified Items with respect to the specified Change Order.</summary>
      <returns>The Item master IDs for the unreleased children.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.GetWorkflowInfo(System.Int64)">
      <summary>Gets extended information about a Workflow.</summary>
      <returns>Extended information about the Workflow.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.IsUserAllowedInRole(System.Int64,Autodesk.Connectivity.WebServices.RoutingUserRoles[])">
      <summary>Tells if there is at least one Requestor or Responsible Engineer with permissions to change item lifecycle states.</summary>
      <returns>True if at least one Requestor or Responsible Engineer has permissions to change item lifecycle states. False if all Requestors and Responsible Engineers
cannot change item lifecycle states.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.ItemRollbackLifeCycleState(System.Int64,System.Int64)">
      <summary>Execute an item rollback to the specified target item iteration ID in rolling back the Item's Lifecycle state from within the context of a change order.</summary>
      <returns>the new tip item</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.SetChangeOrderMarkupFolderConfiguration(Autodesk.Connectivity.WebServices.ChangeOrderMarkupFolderConfig)">
      <summary>Sets the configuration of the Change Order markup folder.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.SetChangeOrderMarkupFolderId(System.Int64)">
      <summary>Sets the Change Order markup folder.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.SetDefaultNumberingScheme(System.Int64)">
      <summary>Sets the default Numbering Scheme.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.SetDefaultRouting(System.Int64)">
      <summary>Sets the default Routing.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.SetDefaultWorkflow(System.Int64)">
      <summary>Sets the default Workflow.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.SetDisallowedItemLifeCycleStatesForSubmitActivities(System.Int64[])">
      <summary>Sets which lifecycle states for items and file will restrict a change order from moving out of a work state when the restrict setting is enabled</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.SetDisallowedLifeCycleStatesForSubmitActivitiesEnabled(System.String,System.Boolean)">
      <summary>Sets the enabled value for the Change Order Vault setting "Restrict Change Orders from moving out of Work state based on File and Item lifecycle states"</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.StartChangeOrderActivity(System.Int64,System.Int64,System.Int64,System.DateTime)">
      <summary>Start a change order activity.</summary>
      <returns>Returns the targeted change order.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.UndoEditChangeOrder(System.Int64)">
      <summary>Cancels the edits for a Change Order.</summary>
      <returns>Returns the undone Change Order object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.UpdateChangeOrder(System.Int64,System.String,System.String,System.String,System.DateTime,System.Int64[],System.Int64[],System.Int64[],System.Int64[],System.Int64[],System.Int64[],Autodesk.Connectivity.WebServices.PropInst[],System.Int64[],Autodesk.Connectivity.WebServices.MsgGroup[],Autodesk.Connectivity.WebServices.Email[],Autodesk.Connectivity.WebServices.AssocPropItem[],System.Int64[],System.Int64,Autodesk.Connectivity.WebServices.RoutingUserRoles[],Autodesk.Connectivity.WebServices.RoutingUserRoles[])">
      <summary>Updates a Change Order.</summary>
      <returns>The updated Change Order object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.UpdateChangeOrderProperties(System.Int64[],Autodesk.Connectivity.WebServices.PropInstParamArray[])">
      <summary>Update the property values on a Change Order.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.UpdateChangeOrderRoutingMembers(System.Int64,System.Int64,Autodesk.Connectivity.WebServices.RoutingUserRoles[],Autodesk.Connectivity.WebServices.RoutingUserRoles[])">
      <summary>Updates the users and roles for a Change Order Routing.</summary>
      <returns>Returns the resulting members and roles of the change order Routing.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.UpdateChangeOrderStateTransitionJobTypes(System.Int64,System.Int64,System.String[])">
      <summary>Updates the custom jobs fired for a lifecycle state transition.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.UpdateFileLifeCycleDefinitions(System.Int64,System.Int64[],System.Int64[],System.Int64[],System.String)">
      <summary>Updates file lifecycle definitions from within the context of a change order</summary>
      <returns>the updated file objects</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.UpdateFileLifeCycleStates(System.Int64,System.Int64[],System.Int64[],System.String)">
      <summary>Updates file lifecycle states from within the context of a change order</summary>
      <returns>the updated file objects</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.UpdateItemLifeCycleDefinitions(System.Int64,System.Int64[],System.Int64[],System.Int64[],System.String)">
      <summary>Updates item lifecycle definitions from within the context of a change order</summary>
      <returns>the updated item objects</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.UpdateItemLifeCycleStates(System.Int64,System.Int64[],System.Int64[],System.String)">
      <summary>Sets the life cycle state for a set of Items.</summary>
      <returns>the update item iterations</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.UpdateNumberingScheme(Autodesk.Connectivity.WebServices.ChangeOrderNumSchm)">
      <summary>Updates the data for a numbering scheme.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.UpdateRouting(System.Int64,System.Boolean,System.Boolean)">
      <summary>Updates a Routing object.</summary>
      <returns>The updated Routing object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ChangeOrderService.UpdateRoutingMembers(System.Int64,Autodesk.Connectivity.WebServices.RoutingUserRoles[],Autodesk.Connectivity.WebServices.RoutingUserRoles[])">
      <summary>Alters the users and roles for a Change Order Routing.</summary>
      <returns>Returns the resulting members and roles of the Routing.</returns>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderService.AddChangeOrderEvents">
      <summary>A collection of Web Service Command Events for add Change Order operations.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderService.AllowInvokeEvents">
      <summary>Tells if invoke events should fire. This value is true by default.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderService.CommitChangeOrderEvents">
      <summary>A collection of Web Service Command Events for commit Change Order operations.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderService.DeleteChangeOrderEvents">
      <summary>A collection of Web Service Command Events for delete Change Order operations.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderService.EditChangeOrderEvents">
      <summary>A collection of Web Service Command Events for edit Change Order operations.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderService.SecurityHeader">
      <summary>The security header. (from IWebService)</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderService.UpdateChangeOrderLifecycleStateEvents">
      <summary>A collection of Web Service Command Events for Change Order lifecycle change operations.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ChangeOrderService.WebServiceManager">
      <summary>A reference to the WebServiceManager that created this object. Value may be null.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.ChangeOrderService.PostInvokeEvents">
      <summary>Subscribe to this event to get a notification after every web service call.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.ChangeOrderService.PreInvokeEvents">
      <summary>Subscribe to this event to get a notification before every web service call.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CheckinFileCommandEventArgs.AddRestriction(Autodesk.Connectivity.Extensibility.Framework.ExtensionRestriction)">
      <summary>Adds a restriction, which blocks the command. This function should only be called during the GetRestrictions event.</summary>
      <param>The reason that the command cannot run.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CheckinFileCommandEventArgs.Guid">
      <summary>The unique identifier for the event.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CheckinFileCommandEventArgs.ReturnValue">
      <summary>The updated File object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CheckinFileCommandEventArgs.Status">
      <summary>The status of the command. Used in the Post event to see of the command was successful.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CheckinFileCommandEventArgs.Associations">
      <summary>Information about immediate child attachments and dependencies.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CheckinFileCommandEventArgs.Bom">
      <summary>A bill of materials to associate with this file. This value will be null if there is no BOM.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CheckinFileCommandEventArgs.Comment">
      <summary>Text data to be associated with the check-in.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CheckinFileCommandEventArgs.CopyBom">
      <summary>If this value is true and the 'bom' parameter is null, then the BOM from the previous version will be copied for this version.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CheckinFileCommandEventArgs.FileClassification">
      <summary>The classification for the file.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CheckinFileCommandEventArgs.FileMasterId">
      <summary>The file being checked in.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CheckinFileCommandEventArgs.Hidden">
      <summary>A flag to indicate if a file should be hidden.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CheckinFileCommandEventArgs.KeepCheckedOut">
      <summary>If set to true, the file will still be checked out to the user.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CheckinFileCommandEventArgs.LastWrite">
      <summary>The last time the file was modified on the client's machine.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CheckinFileCommandEventArgs.NewFileName">
      <summary>The new file name. A null value means the name stays the same.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CheckoutFileCommandEventArgs.AddRestriction(Autodesk.Connectivity.Extensibility.Framework.ExtensionRestriction)">
      <summary>Adds a restriction, which blocks the command. This function should only be called during the GetRestrictions event.</summary>
      <param>The reason that the command cannot run.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CheckoutFileCommandEventArgs.Guid">
      <summary>The unique identifier for the event.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CheckoutFileCommandEventArgs.ReturnValue">
      <summary>The updated File object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CheckoutFileCommandEventArgs.Status">
      <summary>The status of the command. Used in the Post event to see of the command was successful.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CheckoutFileCommandEventArgs.Comment">
      <summary>Associates a comment with the checked out File.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CheckoutFileCommandEventArgs.FileId">
      <summary>The File being checked out.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CheckoutFileCommandEventArgs.LocalPath">
      <summary>The location of the checked-out File on the client's machine.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CheckoutFileCommandEventArgs.Machine">
      <summary>The client's computer name.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CheckoutFileCommandEventArgs.Option">
      <summary>Specifies where the placeholder information is to be taken from.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CodeWord.Code">
      <summary>A string value.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CodeWord.Descr">
      <summary>A description of the value.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CommitChangeOrderCommandEventArgs.AddRestriction(Autodesk.Connectivity.Extensibility.Framework.ExtensionRestriction)">
      <summary>Adds a restriction, which blocks the command. This function should only be called during the GetRestrictions event.</summary>
      <param>The reason that the command cannot run.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CommitChangeOrderCommandEventArgs.Guid">
      <summary>The unique identifier for the event.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CommitChangeOrderCommandEventArgs.ReturnValue">
      <summary>The updated Change Order.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CommitChangeOrderCommandEventArgs.Status">
      <summary>The status of the command. Used in the Post event to see of the command was successful.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CommitChangeOrderCommandEventArgs.AddAssocProperties">
      <summary>Properties to add to the link between the Change Order and another entity. The 'From ID' is the Change Order ID and the 'To ID' is the ID of the linked Entity.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CommitChangeOrderCommandEventArgs.AddAttmtMasterIds">
      <summary>Additional Files to attach with the Change Order. This is set to null if there are no new attachments.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CommitChangeOrderCommandEventArgs.AddComments">
      <summary>Comments to add to the Change Order.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CommitChangeOrderCommandEventArgs.AddFileMasterIds">
      <summary>Additional Files to be tracked by the Change Order. This is set to null if there are no new associations.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CommitChangeOrderCommandEventArgs.AddItemMasterIds">
      <summary>Additional Items to associate with the Change Order. This is set to null if there are no new associations.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CommitChangeOrderCommandEventArgs.AddMembers">
      <summary>Members to add to the Routing for this Change Order.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CommitChangeOrderCommandEventArgs.AddProperties">
      <summary>Additional Properties to associate with the Change Order. This is set to null if there are no new associations.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CommitChangeOrderCommandEventArgs.ApproveDeadline">
      <summary>The date and time the Change Order needs to be approved by.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CommitChangeOrderCommandEventArgs.ChangeOrderId">
      <summary>The Change Order ID.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CommitChangeOrderCommandEventArgs.ChangeOrderNumber">
      <summary>The new Change Order number, or null if the number does not change.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CommitChangeOrderCommandEventArgs.DelAssocPropIds">
      <summary>Properties to delete from the Change Order.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CommitChangeOrderCommandEventArgs.DelAttmtMasterIds">
      <summary>File attachments to disassociate with the Change Order. This is set to null if no files need to be detached.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CommitChangeOrderCommandEventArgs.DelFileMasterIds">
      <summary>Files to no longer be tracked by the Change Order. This is set to null if no assocaitions need to be deleted.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CommitChangeOrderCommandEventArgs.DelItemMasterIds">
      <summary>Items to disassociate with the Change Order. This is set to null if no assocaitions need to be deleted.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CommitChangeOrderCommandEventArgs.DelMembers">
      <summary>Members to delete from the Routing for this Change Order.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CommitChangeOrderCommandEventArgs.DelPropDefIds">
      <summary>Properties to disassociate with the Change Order. Set this to null if no assocaitions need to be deleted.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CommitChangeOrderCommandEventArgs.Description">
      <summary>The descrition of the Change Order.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CommitChangeOrderCommandEventArgs.NotifyEmails">
      <summary>Emails to send out upon completion.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CommitChangeOrderCommandEventArgs.RoutingId">
      <summary>The ID of the Routing that is associated with the Change Order. A value of -1 means keep the same Routing.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CommitChangeOrderCommandEventArgs.Title">
      <summary>The title of the Change Order.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CommitItemCommandEventArgs.AddRestriction(Autodesk.Connectivity.Extensibility.Framework.ExtensionRestriction)">
      <summary>Adds a restriction, which blocks the command. This function should only be called during the GetRestrictions event.</summary>
      <param>The reason that the command cannot run.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CommitItemCommandEventArgs.Guid">
      <summary>The unique identifier for the event.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CommitItemCommandEventArgs.ReturnValue">
      <summary>The updated Items.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CommitItemCommandEventArgs.Status">
      <summary>The status of the command. Used in the Post event to see of the command was successful.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.CommitItemCommandEventArgs.ItemRevisionIds">
      <summary>The Items being committed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ComplexField.ComplexVal">
      <summary>The value of the field.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ComplexField.FieldTyp">
      <summary>The type of field. Inherited from NumSchmField.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ComplexField.Len">
      <summary>The number of characters for this field.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ComplexField.Name">
      <summary>The name of the field. Inherited from NumSchmField.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CompProp.CreateNew">
      <summary>If true, the property can be created within the file during a writeback operation. If false, the property will be ignored during a writeback operation.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CompProp.DispName">
      <summary>The display name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CompProp.EntClassId">
      <summary>The entity class ID.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CompProp.Moniker">
      <summary>A unique identifier for the property.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CompProp.Value">
      <summary>The property value.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CompressionHeader.Supported">
      <summary>Gets the type of compression supported.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CtntSrc.CtntSrcDefTyps">
      <summary>The types of property definitions supported.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CtntSrc.DispName">
      <summary>The display name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CtntSrc.Id">
      <summary>The unique identifier.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CtntSrc.IsReg">
      <summary>Tells if the provider is registered. If true, the provider is currently registered on the server. If false, there exists an in use property which uses this
provider, but the provider is not registered.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CtntSrc.MapDirection">
      <summary>The allowed mapping directions for property data.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CtntSrc.ReadProvClsNm">
      <summary>The class name of the read provider.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CtntSrc.ReadSrcArray">
      <summary>An array of file types (ex. ".dwg") that can be read by this provider.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CtntSrc.SysName">
      <summary>The system name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CtntSrc.WriteProvClsNm">
      <summary>The class name of the write provider.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CtntSrc.WriteSrcArray">
      <summary>An array of file types (ex. ".dwg") that can be written to by this provider.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CtntSrcPropDef.CanCreateNew">
      <summary>If true, the property can be created within the source during a writeback operation.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CtntSrcPropDef.Classification">
      <summary>Tells if the property is user defined within the source.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CtntSrcPropDef.CtntSrcDefTyp">
      <summary>The content source property type.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CtntSrcPropDef.CtntSrcId">
      <summary>The content source ID.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CtntSrcPropDef.DispName">
      <summary>The display name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CtntSrcPropDef.MapDirection">
      <summary>The allowed mapping directions.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CtntSrcPropDef.Moniker">
      <summary>The unique identifier for the property within the content source.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CtntSrcPropDef.Typ">
      <summary>The data type.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CustEnt.Cat">
      <summary>The category for this object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CustEnt.Cloaked">
      <summary>If true, the logged-in user is restricted from seeing this object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CustEnt.CustEntDefId">
      <summary>The definition of this custom entity type.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CustEnt.Id">
      <summary>The identifier for this object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CustEnt.LfCyc">
      <summary>The lifecycle information for the Entity.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CustEnt.Locked">
      <summary>Tells if the Entity can be modified by the logged-in user.<!--DXMETADATA end --></summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CustEnt.Name">
      <summary>The display name for this object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CustEnt.Num">
      <summary>A unique, persistable identifier.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CustEntDef.DispName">
      <summary>The display name for this type.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CustEntDef.DispNamePlural">
      <summary>The display name for multiple objects of this type.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CustEntDef.Id">
      <summary>The identifier for this entity definition.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CustEntDef.Name">
      <summary>A unique string identifier for this type.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CustomEntityService.AddCustomEntity(System.Int64,System.String)">
      <summary>Adds a new Custom Entity object.</summary>
      <returns>The newly created object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CustomEntityService.AddCustomEntityDefinition(System.String,System.String,System.String,Autodesk.Connectivity.WebServices.ByteArray,Autodesk.Connectivity.WebServices.ACE[])">
      <summary>Adds a new Custom Entity Definition, which can be considered a Vault object type.</summary>
      <returns>The Entity Definition object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CustomEntityService.DeleteCustomEntities(System.Int64[])">
      <summary>Deletes a set of custom entity objects.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CustomEntityService.DeleteCustomEntitiesUnconditional(System.Int64[])">
      <summary>Deletes a set of custom entity objects and ignores restrictions that may come up during the operation.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CustomEntityService.DeleteCustomEntityDefinition(System.Int64)">
      <summary>Deletes a custom entity definition.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CustomEntityService.FindCustomEntitiesByNumbers(System.String[])">
      <summary>Gets a set of Custom Entity objects based on their unique number value.</summary>
      <returns>
        <para>The set of Custom Entity objects that match the provided numbers.</para>
        <para>If a match is not found, no object is returned. So the return array may be smaller than the input array.</para>
      </returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CustomEntityService.FindCustomEntitiesBySearchConditions(Autodesk.Connectivity.WebServices.SrchCond[],Autodesk.Connectivity.WebServices.SrchSort[],System.String@,Autodesk.Connectivity.WebServices.SrchStatus@)">
      <summary>Returns a set of custom entities that match a given search criteria.</summary>
      <returns>A list of the objects that match the search criteria.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CustomEntityService.GetACEsByCustomEntityDefinitionId(System.Int64)">
      <summary>Gets the default ACL for a Custom Entity definition.</summary>
      <returns>The Access Control Entries that make up the default ACL for the provided Entity Class definition.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CustomEntityService.GetAllCustomEntityDefinitions">
      <summary>Gets all of the custom entity definitions for this Vault.</summary>
      <returns>The list of all custom entity definitions for this Vault.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CustomEntityService.GetCustomEntitiesByIds(System.Int64[])">
      <summary>Get a list of Custom Entity objects based on their IDs.</summary>
      <returns>The corresponding Custom Entity objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CustomEntityService.GetCustomEntityDefinitionIcons(System.Int64[])">
      <summary>Gets the icons for a set of Custom Entity definitions.</summary>
      <returns>An array of byte arrays representing icon information for the submitted Custom Entity definitions. The byte arrays are the contents of an .ico file.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CustomEntityService.UpdateCustomEntity(System.Int64,System.String)">
      <summary>Updates a Custom Entity object.</summary>
      <returns>The updated object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CustomEntityService.UpdateCustomEntityCategories(System.Int64[],System.Int64[])">
      <summary>Updates the categories for a set of Custom Entity objects.</summary>
      <returns>The updated Custom Entity objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CustomEntityService.UpdateCustomEntityDefinition(System.Int64,System.String,System.String,Autodesk.Connectivity.WebServices.ByteArray,Autodesk.Connectivity.WebServices.ACE[])">
      <summary>Updates a Custom Entity Definition.</summary>
      <returns>The updated Custom Entity Definition object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CustomEntityService.UpdateCustomEntityLifeCycleDefinitions(System.Int64[],System.Int64[],System.Int64[],System.String)">
      <summary>Updates the life cycle definition and state for a set of Custom Entities.<!--DXMETADATA end --></summary>
      <returns>The updated Custom Entity objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CustomEntityService.UpdateCustomEntityLifeCycleStates(System.Int64[],System.Int64[],System.String)">
      <summary>Updates the life cycle state for a set of Custom Entities.<!--DXMETADATA end --></summary>
      <returns>The updated Custom Entity objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CustomEntityService.UpdateCustomEntityProperties(System.Int64[],Autodesk.Connectivity.WebServices.PropInstParamArray[])">
      <summary>Updates the property values for a set of Custom Entity objects.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.CustomEntityService.UpdateCustomEntityPropertyDefinitions(System.Int64[],System.Int64[],System.Int64[])">
      <summary>
        <para>Updates the property definitions associated with a set of Custom Entity objects.</para>
      </summary>
      <returns>The updated objects.</returns>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CustomEntityService.AllowInvokeEvents">
      <summary>Tells if invoke events should fire. This value is true by default.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CustomEntityService.SecurityHeader">
      <summary>The security header. (from IWebService)</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CustomEntityService.UpdateCustomEntityLifecycleStateEvents">
      <summary>A collection of Web Service Command Events for Custom Entity lifecycle change operations.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.CustomEntityService.WebServiceManager">
      <summary>A reference to the WebServiceManager that created this object. Value may be null.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.CustomEntityService.PostInvokeEvents">
      <summary>Subscribe to this event to get a notification after every web service call.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.CustomEntityService.PreInvokeEvents">
      <summary>Subscribe to this event to get a notification before every web service call.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DbOwn.ExpDate">
      <summary>The expiration date and time of the lease.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DbOwn.IsExp">
      <summary>If true, the lease has expired. If false, the lease is still active.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DbOwn.IsLocalOwn">
      <summary>If true, the site that currently connected to has ownership. If false, another workgroup has ownerhsip.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DbOwn.WkgrpId">
      <summary>The workgroup that owns the database.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DeleteChangeOrderCommandEventArgs.AddRestriction(Autodesk.Connectivity.Extensibility.Framework.ExtensionRestriction)">
      <summary>Adds a restriction, which blocks the command. This function should only be called during the GetRestrictions event.</summary>
      <param>The reason that the command cannot run.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DeleteChangeOrderCommandEventArgs.Guid">
      <summary>The unique identifier for the event.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DeleteChangeOrderCommandEventArgs.Status">
      <summary>The status of the command. Used in the Post event to see of the command was successful.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.DeleteChangeOrderCommandEventArgs.ChangeOrderIds">
      <summary>The Change Orders being deleted.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DeleteFileCommandEventArgs.AddRestriction(Autodesk.Connectivity.Extensibility.Framework.ExtensionRestriction)">
      <summary>Adds a restriction, which blocks the command. This function should only be called during the GetRestrictions event.</summary>
      <param>The reason that the command cannot run.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DeleteFileCommandEventArgs.Guid">
      <summary>The unique identifier for the event.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DeleteFileCommandEventArgs.Status">
      <summary>The status of the command. Used in the Post event to see of the command was successful.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.DeleteFileCommandEventArgs.FileMasterIds">
      <summary>The Files being deleted.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.DeleteFileCommandEventArgs.FolderId">
      <summary>The folder that the files are being deleted from.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DeleteFolderCommandEventArgs.AddRestriction(Autodesk.Connectivity.Extensibility.Framework.ExtensionRestriction)">
      <summary>Adds a restriction, which blocks the command. This function should only be called during the GetRestrictions event.</summary>
      <param>The reason that the command cannot run.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DeleteFolderCommandEventArgs.Guid">
      <summary>The unique identifier for the event.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DeleteFolderCommandEventArgs.Status">
      <summary>The status of the command. Used in the Post event to see of the command was successful.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.DeleteFolderCommandEventArgs.FolderId">
      <summary>The Folder being deleted.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DeleteItemCommandEventArgs.AddRestriction(Autodesk.Connectivity.Extensibility.Framework.ExtensionRestriction)">
      <summary>Adds a restriction, which blocks the command. This function should only be called during the GetRestrictions event.</summary>
      <param>The reason that the command cannot run.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DeleteItemCommandEventArgs.Guid">
      <summary>The unique identifier for the event.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DeleteItemCommandEventArgs.Status">
      <summary>The status of the command. Used in the Post event to see of the command was successful.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.DeleteItemCommandEventArgs.ItemMasterIds">
      <summary>The Items being deleted.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DelimField.DelimVal">
      <summary>The delimiter character.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DelimField.FieldTyp">
      <summary>The type of field. Inherited from NumSchmField.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DelimField.Name">
      <summary>The name of the field. Inherited from NumSchmField.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DocRestric.Code">
      <summary>The type of restriction.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DocRestric.EntId">
      <summary>Identifies the entity associated with the restriction</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DocRestric.Params">
      <summary>Extra information specific to the restriction code.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DocRestricArray.Restrictions">
      <summary>An array of Document Restrictions.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.ActivateNumberingSchemes(System.Int64[])">
      <summary>Activates a set of File numbering schemes.</summary>
      <returns>An array of updated number schemes.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.AddComponentCustomPropertyDefinition(System.String,Autodesk.Connectivity.WebServices.DataType)">
      <summary>Defines a custom property definition for file BOM components</summary>
      <returns>The new PropDef object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.AddDesignVisualizationFileAttachment(System.Int64,Autodesk.Connectivity.WebServices.FileAssocParam)">
      <summary>Adds an attachment to a Design Visualization file without increasing the version of the file.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.AddFileNamingScheme(Autodesk.Connectivity.WebServices.FileNmngSchm)">
      <summary>Creates a new file naming scheme.</summary>
      <returns>The newly created scheme object. All properties will be identical to the passed in object, except for the ID property.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.AddFolder(System.String,System.Int64,System.Boolean)">
      <summary>Creates a new folder in the Vault.</summary>
      <returns>A Folder object representing the newly created folder.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.AddLabel(System.Int64,System.String,System.String)">
      <summary>Creates a new label in the Vault.</summary>
      <returns>A label object representing the newly created label.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.AddLink(System.Int64,System.String,System.Int64,System.String)">
      <summary>Adds a new Link.</summary>
      <returns>The newly created Link object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.AddNumberingScheme(System.String,Autodesk.Connectivity.WebServices.NumSchmField[],System.Boolean)">
      <summary>Creates a new Numbering Scheme.</summary>
      <returns>The Numbering Scheme object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.AddUploadedFile(System.Int64,System.String,System.String,System.DateTime,Autodesk.Connectivity.WebServices.FileAssocParam[],Autodesk.Connectivity.WebServices.BOM,Autodesk.Connectivity.WebServices.FileClassification,System.Boolean,Autodesk.Connectivity.WebServices.ByteArray)">
      <summary>Creates a File object based on binary data previously uploaded.</summary>
      <returns>A File object representing the Vault's view of that file.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.CheckinUploadedFile(System.Int64,System.String,System.Boolean,System.DateTime,Autodesk.Connectivity.WebServices.FileAssocParam[],Autodesk.Connectivity.WebServices.BOM,System.Boolean,System.String,Autodesk.Connectivity.WebServices.FileClassification,System.Boolean,Autodesk.Connectivity.WebServices.ByteArray)">
      <summary>Checks-in a file based on binary data previously uploaded</summary>
      <returns>A File object representing the Vault's view of the file.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.CheckoutFile(System.Int64,Autodesk.Connectivity.WebServices.CheckoutFileOptions,System.String,System.String,System.String,Autodesk.Connectivity.WebServices.ByteArray@)">
      <summary>Checks out a file from the vault.</summary>
      <returns>When a file is checked out, a new version of the file is created as a placeholder. This placeholder is returned in the form of a File object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.DeactivateNumberingSchemes(System.Int64[])">
      <summary>Deactivates a set of File numbering schemes.</summary>
      <returns>The updated Numbering Scheme objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.DeleteComponentCustomPropertyDefinition(System.Int64)">
      <summary>Deletes a custom property definition for components.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.DeleteFileFromFolder(System.Int64,System.Int64)">
      <summary>Deletes a file.</summary>
      <returns>Nothing.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.DeleteFileFromFolderUnconditional(System.Int64,System.Int64)">
      <summary>Deletes a file unconditionally.</summary>
      <returns>Nothing.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.DeleteFileNamingScheme(System.Int64)">
      <summary>Deletes a file naming scheme</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.DeleteFilesFromFolder(System.Int64[],System.Int64)">
      <summary>Deletes multiple files.</summary>
      <returns>Nothing.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.DeleteFilesFromFolderUnconditional(System.Int64[],System.Int64)">
      <summary>Deletes multiple files unconditionally.</summary>
      <returns>Nothing.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.DeleteFileVersions(System.Int64[])">
      <summary>Deletes specific versions of a file.</summary>
      <returns>A report of which versions have been deleted and which versions have not.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.DeleteFileVersionsByMasterIds(System.Int64[],System.Boolean,System.Int32,System.Int32,System.String)">
      <summary>Purges older versions of a file based on certian criteria.</summary>
      <returns>A report of which versions have been deleted and which versions have not.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.DeleteFileVersionsByMasterIdsUnconditional(System.Int64[],System.Boolean,System.Int32,System.Int32,System.String)">
      <summary>Purges older versions of a file based on certain criteria.</summary>
      <returns>A report of which versions have been deleted and which versions have not.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.DeleteFileVersionsUnconditional(System.Int64[])">
      <summary>Deletes specific versions of a file.</summary>
      <returns>A report of which versions have been deleted and which versions have not.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.DeleteFolderHierarchy(System.Int64)">
      <summary>Deletes a vault folder and all its subfolders.</summary>
      <returns>Nothing.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.DeleteFolderHierarchyUnconditional(System.Int64)">
      <summary>Deletes a folder and all its subfolders unconditionally.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.DeleteLabel(System.Int64)">
      <summary>Deletes the specified label.</summary>
      <returns>Nothing.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.DeleteLinks(System.Int64[])">
      <summary>Deletes a set of Links.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.DeleteNumberingScheme(System.Int64)">
      <summary>Deletes a file numbering scheme</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.DeleteNumberingSchemeUnconditional(System.Int64)">
      <summary>Deletes a file numbering scheme even if it is in use.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.FindFileFoldersBySearchConditions(Autodesk.Connectivity.WebServices.SrchCond[],Autodesk.Connectivity.WebServices.SrchSort[],System.Int64[],System.Boolean,System.Boolean,System.String@,Autodesk.Connectivity.WebServices.SrchStatus@)">
      <summary>Performs a search on the Vault for a list of FileFolders.</summary>
      <returns>A list of all matching file folders or null if there are no matches.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.FindFilePathsByDateAndChecksum(System.Int32,System.DateTime)">
      <summary>Returns a set of file paths based on the create date and checksum.</summary>
      <returns>An array of FilePath objects corresponding to the search information.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.FindFilePathsByDateNameAndChecksum(System.String,System.Int32,System.DateTime)">
      <summary>Finds a file and its related folders based on date, name and checksum values.</summary>
      <returns>An array of FilePath objects corresponding to the search information.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.FindFilePathsByNameAndChecksum(System.String,System.Int32)">
      <summary>Returns a set of file paths based on a name and checksum.</summary>
      <returns>An array of FilePath objects corresponding to the search information.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.FindFilesByDatesAndChecksums(System.Int32[],System.DateTime[])">
      <summary>Find a set of Files based on checksum and create date information.</summary>
      <returns>An array of File objects corresponding to the search information.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.FindFilesByDatesPathsAndChecksums(System.String[],System.Int32[],System.DateTime[])">
      <summary>Returns a set of Files based on lists of dates, paths and checksums.</summary>
      <returns>An array of File objects corresponding to the information in the input arrays. If a match is not found, the File masterId will be -1 for that entry.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.FindFilesByFoldersDatesNamesAndChecksums(System.Int64[],System.String[],System.Int32[],System.DateTime[])">
      <summary>Find Files based on folders, dates, file names and checksums.</summary>
      <returns>An array of File objects corresponding to the information in the input arrays. If no match is found, both the File.Id and File.MasterId values will be -1. If
the path is found but the checksum does not match, the File.Id value will be -1 but File.MasterId will have a valid value. If an exact match is found, the
File.Id and File.MasterId value will be valid along with the rest of the File data.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.FindFilesByFoldersNamesAndChecksums(System.Int64[],System.String[],System.Int32[])">
      <summary>Returns a set of Files based on lists of folders, file names and checksums.</summary>
      <returns>An array of File objects corresponding to the information in the input arrays. If a match is not found, the File masterId will be -1 for that entry.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.FindFilesByIds(System.Int64[])">
      <summary>Find files by ids.</summary>
      <returns>An array of file objects matching the input ids</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.FindFilesByPathsAndChecksums(System.String[],System.Int32[])">
      <summary>Returns a set of Files based on a list of paths and checksums.</summary>
      <returns>An array of File objects corresponding to the information in the filePaths and checksums input arrays. If a match is not found, the File masterId will be -1
for that entry.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.FindFilesBySearchConditions(Autodesk.Connectivity.WebServices.SrchCond[],Autodesk.Connectivity.WebServices.SrchSort[],System.Int64[],System.Boolean,System.Boolean,System.String@,Autodesk.Connectivity.WebServices.SrchStatus@)">
      <summary>Performs a search on the Vault files.</summary>
      <returns>A list of all matching files or null if there are no matches.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.FindFoldersByIds(System.Int64[])">
      <summary>Gets a Folder object based on its ID.</summary>
      <returns>An array of Folder objects corresponding to the input array. If a folder could not be located in the vault, it will have -1 as its ID.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.FindFoldersByPaths(System.String[])">
      <summary>Gets a list of Folder objects based on their paths.</summary>
      <returns>A list of Folder objects. Each element in the array corresponds to the path in the input array. The ID of a folder will be -1 if the input path was not found
in the Vault.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.FindFoldersBySearchConditions(Autodesk.Connectivity.WebServices.SrchCond[],Autodesk.Connectivity.WebServices.SrchSort[],System.Int64[],System.Boolean,System.String@,Autodesk.Connectivity.WebServices.SrchStatus@)">
      <summary>Performs a Folder search.</summary>
      <returns>A list of all matching Folders or null if there are no matches.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.FindInFileSetBySearchConditions(Autodesk.Connectivity.WebServices.SrchCond[],Autodesk.Connectivity.WebServices.SrchSort[],System.Int64[],System.String@,Autodesk.Connectivity.WebServices.SrchStatus@)">
      <summary>Performs a search within a group of files.</summary>
      <returns>A list of all matching files or null if there are no matches.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.FindLabelByName(System.String)">
      <summary>Find a label based on its name.</summary>
      <returns>A label object corresponding to the search information. Will return an “empty” Label (Id = “-1”) if no label is found.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.FindLabelsByNames(System.String[])">
      <summary>Find labels based on a list of names.</summary>
      <returns>An array of label objects corresponding to the search information. Will return an “empty” Label (Id = “-1”) in position of the array if no label is found for a
name.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.FindLatestFilesByMasterIds(System.Int64[])">
      <summary>Finds the latest version of a set of files based on their master IDs.</summary>
      <returns>An array of File objects corresponding to the input array. If there is no file to match the input master ID, a File object with an ID of -1 will be returned
for that master ID.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.FindLatestFilesByPaths(System.String[])">
      <summary>Gets only the latest versions of the files in a specified set of file paths.</summary>
      <returns>An array of File objects corresponding to the input array. If there is no file to match the input path, a File object with an ID of -1 will be returned for
that path.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.FindLinksBySearchConditions(Autodesk.Connectivity.WebServices.SrchCond[],Autodesk.Connectivity.WebServices.SrchSort[],System.String[],System.Int64[],System.Boolean,System.String[],System.String@,Autodesk.Connectivity.WebServices.SrchStatus@)">
      <summary>Gets the links on an entity that matches a set of search criteria.</summary>
      <returns>A list of all Links pointing to an Entity that matches the search criteria.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GenerateFileNumber(System.Int64,System.String[])">
      <summary>Generates a new file name based on a numbering scheme.</summary>
      <returns>The generated file number.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GenerateFileNumbers(System.Int64[],Autodesk.Connectivity.WebServices.StringArray[])">
      <summary>Generates new file names based on numbering schemes.</summary>
      <returns>An array of generated names.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetAllFileNamingSchemes">
      <summary>Gets all the file naming schemes.</summary>
      <returns>A list of all the file naming schemes.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetAllLabels">
      <summary>Get a list of all labels.</summary>
      <returns>An array of label objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetAssociatedFilePathsByIds(System.Int64[],Autodesk.Connectivity.WebServices.FileAssociationTypeEnum,System.Boolean,Autodesk.Connectivity.WebServices.FileAssociationTypeEnum,System.Boolean,System.Boolean,System.Boolean)">
      <summary>Gets associated file paths for a list of file Ids.</summary>
      <returns>A list of FilePathArray objects related to the input fileIds array. The order of the items is not guaranteed.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetBlockExplorerDesignFileCheckin">
      <summary>Tells if a non-CAD client should prevent CAD files from being checked in.</summary>
      <returns>If true, CAD files should be restricted from being added by non-CAD clients. If false, this restriction is not in place.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetBOMByFileId(System.Int64)">
      <summary>Gets the Bill of Materials data for a file.</summary>
      <returns>The Bill of Materials data for the file.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetBOMByFileIds(System.Int64[])">
      <summary>Gets the Bill of Materials data for a set of files.</summary>
      <returns>An array of BOM objects corresponding to the array of fileIds.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetBreakDesignVisualizationLinkCommandList">
      <summary>Gets which client operations should break links to design visualization files.</summary>
      <returns>An enumerated bitfield describing which operation do should not copy over design visualization links.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetBulkFileThreshold">
      <summary>Gets the threshold values for bulk file edit operations.</summary>
      <returns>The threshold values.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetComponentProperties(System.Int64,System.String)">
      <summary>Gets property data for a file which has been assigned to an item.</summary>
      <returns>Property data for the component.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetContentSourceIdsByFileIds(System.Int64[])">
      <summary>Tells which content source is associated with a set of files.</summary>
      <returns>The corresponding content source ID values. 0 is returned if there is no source for the file.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetDesignVisualizationAttachmentsByFileMasterIds(System.Int64[])">
      <summary>Gets the design visualization attachments for all versions of a set of files.</summary>
      <returns>An array of arrays of file associations. The first level of arrays corresponds to the input array. The second level of arrays is the file associations for that
File.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetDownloadTicketsByFileIds(System.Int64[])">
      <summary>Gets a ticket that you can use to download binary data from the filestore service. Use this function when downloading specific file versions.</summary>
      <returns>
        <para>An array of download tickets, one for each master ID input. The ticket itself is just a byte array. Use the ticket in the Filestore Service to download the
actual binary data for the file.</para>
        <para>The resulting download will be the specific version corresponding to the file ID.</para>
      </returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetDownloadTicketsByMasterIds(System.Int64[])">
      <summary>Gets a ticket that you can use to download binary data from the filestore service. Use this function when downloading the latest file versions.</summary>
      <returns>
        <para>An array of download tickets, one for each master ID input. The ticket itself is just a byte array. Use the ticket in the Filestore Service to download the
actual binary data for the file.</para>
        <para>The resulting download will be the latest version corresponding to the file master ID.</para>
      </returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetDWFPublishFolderLocation">
      <summary>Gets the location where DWF files are published.</summary>
      <returns>The location for publishing DWF files.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetDWFPublishPreserveFolderHierarchy">
      <summary>Gets the DWF Publish Preserve Folder Hierarchy option.</summary>
      <returns>If true, the server will preserve the Vault directory structure when publishing DWF files to the location specified by the "DWF Publish Folder Location"
option. If false, the DWF files will all be placed in a single directory.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetEditedPropertiesByMasterIds(System.Int64[])">
      <summary>Gets property values that were edited between the latest version and the latest released version.</summary>
      <returns>An array of property values that have changed values. These are the values on the lastest version.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetEnableDWFAttachment">
      <summary>Determines whether DWF attachments are enabled.</summary>
      <returns>True if DWF files should be automatically created and attached when a file is created or checked in. False if DWF files should not be automatically created.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetEnableItemPropertyWritebackToFiles">
      <summary>Gets the value of the administrator option for enabling item property write back to files</summary>
      <returns>Whether or not item property write back to files is enabled</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetEnforceInventorProjectFile">
      <summary>Gets the "Enforce Inventor Project File" property.</summary>
      <returns>If true, than the clients should enforce a specific Inventor project file. Call GetInventorProjectFileLocation to retrieve the Inventor project file location.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetEnforceWorkingFolder">
      <summary>Gets the Enforce Working Folder property.</summary>
      <returns>If true, than the clients should enforce a specific working folder. Call GetRequiredWorkingFolderLocation to retrieve the working folder path.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFileAssociationLitesByIds(System.Int64[],Autodesk.Connectivity.WebServices.FileAssocAlg,Autodesk.Connectivity.WebServices.FileAssociationTypeEnum,System.Boolean,Autodesk.Connectivity.WebServices.FileAssociationTypeEnum,System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
      <summary>This method gets all files associations of a set of files by file Id. The resulting objects will contain file IDs only instead of full File objects.</summary>
      <returns>An array of lightweight objects representing every association found.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFileAssociationRestrictionsById(System.Int64,System.Int64[],System.Int64[])">
      <summary>Gets any restrictions for setting dependencies between file versions.</summary>
      <returns>An array of file association restrictions. If there are no restrictions, the return value will be null.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFileAssociationsByIds(System.Int64[],Autodesk.Connectivity.WebServices.FileAssociationTypeEnum,System.Boolean,Autodesk.Connectivity.WebServices.FileAssociationTypeEnum,System.Boolean,System.Boolean,System.Boolean)">
      <summary>This method gets all files associations of a set of files by file Id.</summary>
      <returns>An array of FileAssocArray objects representing every association found.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFileById(System.Int64)">
      <summary>Gets a File object based on its ID.</summary>
      <returns>A File object matching the provided ID.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFileByVersion(System.Int64,System.Int32)">
      <summary>Gets data on a specific version of a file in the Vault.</summary>
      <returns>A File object matching the data specified.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFileDeleteRestrictionsByMasterId(System.Int64,System.Int64)">
      <summary>Gets all of the delete restrictions on a file.</summary>
      <returns>An array of restrictions for deleting that file or null if there are no restrictions.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFileDeleteRestrictionsByMasterIds(System.Int64[],System.Int64[])">
      <summary>Gets all of the delete restrictions on a set of files.</summary>
      <returns>An array of file delete restriction array objects corresponding to the input array. If a file has no delete restrictions, then the corresponding
FileDelRestricArray will have no elements in it.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFileFoldersByLabelId(System.Int64)">
      <summary>Gets a list of FileFolder objects based on a label ID.</summary>
      <returns>An array of FileFolder objects that correspond to the input ID.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFileRenameRestrictionsByMasterId(System.Int64,System.String)">
      <summary>Gets any rename restrictions on a file.</summary>
      <returns>An array of rename restrictions or null if there are no restrictions.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFileRenameRestrictionsByMasterIds(System.Int64[],System.String[])">
      <summary>Gets any rename restrictions on a set of files.</summary>
      <returns>An array of file rename restriction arrays indicating the rename restrictions. Each element in the return array, corresponds to the elements of the input
arrays at the same index. A FileRenameRestircArray will be empty if there are no restrictions for the rename.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFilesByHistoryType(System.Int64[],Autodesk.Connectivity.WebServices.FileHistoryTypeOptions)">
      <summary>Gets historical data about a set of files.</summary>
      <returns>An array of arrays of files. The outer array corresponds to the filesIds input array. The inner array contains the historical File object for the corresponding
file ID.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFilesByIds(System.Int64[])">
      <summary>Gets a list of Files based on their file IDs.</summary>
      <returns>An array of File objects matching the provided IDs.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFilesByMasterId(System.Int64)">
      <summary>Gets all the versions of a file.</summary>
      <returns>An array of all files matching the master ID. The files are not listed in any order.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFilesByMasterIds(System.Int64[])">
      <summary>Gets an array of files based on their master IDs.</summary>
      <returns>An array of FileArray objects. Each FileArray corresponds to the fileMasterId at the same index in the input array.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFilesByVersions(System.Int64[],System.Int32[])">
      <summary>Gets data on a specific version of a file in the Vault.</summary>
      <returns>An array of file objects matching the data specified.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFileStatusSetRestrictionsByMasterIds(System.Int64[],Autodesk.Connectivity.WebServices.FileStatus)">
      <summary>Gets any restrictions on setting the status of a File.</summary>
      <returns>An array of arrays of restrictions. The primary array corresponds to the input array. The secondary arrays correspond to the restrictions on the corresponding
File Master Id. The secondary array will be null if there are no restrictions.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFileVersionDeleteRestrictionsByFileIds(System.Int64[])">
      <summary>Gets any restrictions on deleting a specific versions of Files. This function can be used to pre-check any issues before running DeleteFileVersions.</summary>
      <returns>An array of restrictions for this operation. Null will be returned if there are no restrictions.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFileVersionDeleteRestrictionsByMasterIds(System.Int64[],System.Boolean,System.Int32,System.Int32,System.String)">
      <summary>Gets any restrictions on deleting a specific versions of Files. This function can be used to pre-check any issues before running DeleteFileVersionsByMasterIds.</summary>
      <returns>An array of restrictions for the operation. Null will be returned if there are no restrictions.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFilterConfigXml">
      <summary>Gets the contents of filterConfig.xml which is a server file that contains the property mapping of AutoCAD block attributes.</summary>
      <returns>The XML data from filterConfig.xml</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFolderById(System.Int64)">
      <summary>Gets a Folder object based on its ID.</summary>
      <returns>The Folder object that corresponds to the input ID.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFolderByPath(System.String)">
      <summary>Gets a folder object based on its path.</summary>
      <returns>The Folder object that corresponds to the input path.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFolderDeleteRestrictionsById(System.Int64)">
      <summary>Gets all of the delete restrictions on a folder.</summary>
      <returns>An array of restrictions for deleting that folder. Null will be returned if there are no restrictions.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFolderDeleteRestrictionsByIds(System.Int64[])">
      <summary>Gets all of the delete restrictions on a set of folders.</summary>
      <returns>An array of FolderDeleteRestricArray objects corresponding to the input array.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFolderIdsByParentIds(System.Int64[],System.Boolean)">
      <summary>Gets the IDs of the folders underneith a set of folders.</summary>
      <returns>Returns the IDs of the sub-folders. The output array does not correlate with the input array.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFolderMoveRestrictions(System.Int64,System.Int64)">
      <summary>Gets all move restrictions on a folder.</summary>
      <returns>An array of restrictions for moving the folder. Null will be returned if there are no restrictions.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFolderRenameRestrictions(System.Int64,System.String)">
      <summary>Gets all restrictions for renaming a folder.</summary>
      <returns>An array of restrictions for moving the folder. Null is returned if there are no restrictions.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFolderRoot">
      <summary>Gets the root Vault folder.</summary>
      <returns>The root Vault folder (ie. the folder at path "$").</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFoldersByFileMasterId(System.Int64)">
      <summary>Gets all the folders that a set of files is shared in.</summary>
      <returns>An array of all the folders that the file is in.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFoldersByFileMasterIds(System.Int64[])">
      <summary>Gets all the folders that a set of files is shared in.</summary>
      <returns>An array of FolderArray objects corresponding to the input array. Each FolderArray object lists the folders that a file is shared in.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFoldersByIds(System.Int64[])">
      <summary>Gets a Folder object based on its ID.</summary>
      <returns>An array of folders corresponding to the input array. An exception will be thrown if a folder cannot be found.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFoldersByParentId(System.Int64,System.Boolean)">
      <summary>Gets the sub-folders of a vault folder.</summary>
      <returns>An array of all the sub-folders, or null if there are none.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFoldersByParentIds(System.Int64[],System.Boolean)">
      <summary>Gets the sub-folders of a set of parent folders.</summary>
      <returns>An array sub-folder array of all the sub-folders. Each element in the array contains the child folders of the folder specified in parentFolderIds at the same
index.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetFoldersByPaths(System.String[])">
      <summary>Gets a list of Folder objects based on their paths.</summary>
      <returns>A list of Folder objects. Each element in the array corresponds to the path in the input array. An exception will be thrown if an input path is not found in
the Vault.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetInventorProjectFileLocation">
      <summary>Gets the Vault location of the Inventor project file to use. This setting is only valid if the Enforce Inventor Project File setting is true.</summary>
      <returns>The Vault location of the Inventor project file.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetLatestAssociatedFilePathsByMasterIds(System.Int64[],Autodesk.Connectivity.WebServices.FileAssociationTypeEnum,System.Boolean,Autodesk.Connectivity.WebServices.FileAssociationTypeEnum,System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
      <summary>Gets the latest associated file paths of files by their master Ids.</summary>
      <returns>An array of FilePathArray objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetLatestDuplicateFilePaths">
      <summary>Gets file paths for the latest version of any files in the Vault that share the same file name.</summary>
      <returns>An array of FilePath objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetLatestFileAssociationsByMasterIds(System.Int64[],Autodesk.Connectivity.WebServices.FileAssociationTypeEnum,System.Boolean,Autodesk.Connectivity.WebServices.FileAssociationTypeEnum,System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
      <summary>Gets the latest file associations for a list of files by their master IDs.</summary>
      <returns>An array of file association array objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetLatestFileByMasterId(System.Int64)">
      <summary>Gets the latest version of a file.</summary>
      <returns>The latest version of the file.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetLatestFileIdsByFoldersIds(System.Int64[],System.Boolean)">
      <summary>Gets the file IDs for the latest versions of all files within a set of folders.</summary>
      <returns>The IDs of the latest file versions from the input folders. The output array does not correlate with the input array.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetLatestFileIdsByIds(System.Int64[])">
      <summary>Gets the file ID of latest version on a set of Files.</summary>
      <returns>The File ID of latest file version corresponding to the input array.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetLatestFilePathsByNames(System.String[])">
      <summary>Gets the latest file paths for a set of files, by file name.</summary>
      <returns>An array of FilePathArray objects corresponding to the input array of file names.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetLatestFilesByFolderId(System.Int64,System.Boolean)">
      <summary>Gets the latest versions of all files within a folder.</summary>
      <returns>An array of files or null if the folder is empty.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetLatestFilesByFolderIds(System.Int64[],System.Boolean)">
      <summary>Gets the latest versions of all files within a set of folders.</summary>
      <returns>An array of FileArray objects corresponding to the input array. Each FileArray object lists the latest version of the files in the folder.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetLatestFilesByIds(System.Int64[])">
      <summary>Gets the latest version on a set of Files.</summary>
      <returns>The latest version of the Files corresponding to the input array.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetLatestFilesByMasterIds(System.Int64[])">
      <summary>Gets the latest version of a file.</summary>
      <returns>An array of File objects corresponding to the input array.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetLinksByIds(System.Int64[])">
      <summary>Gets a set of Links based on their IDs.</summary>
      <returns>The corresponding Link objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetLinksByParentIds(System.Int64[],System.String[])">
      <summary>Gets a set of Link objects originating from a given set of entities.</summary>
      <returns>All Link objects matching the passed in parent ID and at least one of the passed in entity class IDs.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetLinksByTargetEntityIds(System.Int64[])">
      <summary>Gets the Link objects that point to a set of Entities.</summary>
      <returns>A set of Links for each of the input Entities.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetMetaOnLinks(System.Int64[])">
      <summary>Gets the meta data for a set of links.</summary>
      <returns>The meta data for the links.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetNumberingSchemesByType(Autodesk.Connectivity.WebServices.NumSchmType)">
      <summary>Gets a set of numbering schemes based on type.</summary>
      <returns>An array of all matching objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetRequiredWorkingFolderLocation">
      <summary>The working folder that the client is expected to have. This setting is only valid if the Enforce Working Folder setting is true.</summary>
      <returns>The path to the working folder.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetRevisionAssociatedFilePathsByIds(System.Int64[],Autodesk.Connectivity.WebServices.FileAssociationTypeEnum,System.Boolean,Autodesk.Connectivity.WebServices.FileAssociationTypeEnum,System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
      <summary>Gets the associated files and path information for a given set of files. The returned files will point to the latest version within a revision.</summary>
      <returns>An array of file and path data.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetRevisionFileAssociationsByIds(System.Int64[],Autodesk.Connectivity.WebServices.FileAssociationTypeEnum,System.Boolean,Autodesk.Connectivity.WebServices.FileAssociationTypeEnum,System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
      <summary>This method gets all files associations of a set of files by file ID. The returned files will point to the latest version within a revision.</summary>
      <returns>An array of file associations.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetRevisionFileAssociationsByIds2(System.Int64[],Autodesk.Connectivity.WebServices.FileAssociationTypeEnum,System.Boolean,Autodesk.Connectivity.WebServices.FileAssociationTypeEnum,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
      <summary>This method gets all files associations of a set of files by file ID. The returned files will point to the latest version within a revision.
<!--DXMETADATA end --></summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetTrackFileStatus">
      <summary>Tells if the file status feature is enabled.</summary>
      <returns>If true, then the server is tracking the status of files. See the File section for more information about FileStatus.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetTurnOffWarningForUserGeneratedDesignVisualization">
      <summary>Gets the setting that indicates if a warning should be displayed for user generated design visualization files.</summary>
      <returns>True means that the warning should not be shown. False means that the warning should be shown.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetUniqueFileNameRequired">
      <summary>Determines whether unique file names are required.</summary>
      <returns>True to false.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.GetWorkspaceSyncFileLimit">
      <summary>Gets the maximum number of files for a Workspace Sync operation.</summary>
      <returns>The maximum number of files for a Workspace Sync operation.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.MoveFile(System.Int64,System.Int64,System.Int64)">
      <summary>Moves a file from one folder to another.</summary>
      <returns>Nothing.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.MoveFolder(System.Int64,System.Int64)">
      <summary>Moves a folder from one location to beneath a different parent folder.</summary>
      <returns>Nothing.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.MoveLink(System.Int64,System.Int64,System.Int64)">
      <summary>Moves a link to a different Folder.</summary>
      <returns>The updated Link object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.ReserveFileNamingDescriptions(System.Int64,System.Int32)">
      <summary>Reserves a set of file names for a given naming scheme.</summary>
      <returns>An array of generated names.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.RollbackFileNamingDescriptions(Autodesk.Connectivity.WebServices.FileNmngDescr[])">
      <summary>Rolls back the generated file names.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.SetBlockExplorerDesignFileCheckin(System.Boolean)">
      <summary>Sets if a non-CAD client should prevent CAD files from being checked in.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.SetBreakDesignVisualizationLinkCommandList(Autodesk.Connectivity.WebServices.BreakDesignVisualizationLinkCommandList)">
      <summary>Sets which client operations should break links to design visualization files.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.SetBulkFileThreshold(System.UInt32,System.UInt32)">
      <summary>Sets the threshold values for bulk file edit operations.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.SetDefaultFileNamingScheme(System.Int64)">
      <summary>Sets the default naming scheme.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.SetDefaultNumberingScheme(System.Int64)">
      <summary>Sets the default file numbering scheme.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.SetDesignVisualizationAttachmentStatusById(System.Int64,Autodesk.Connectivity.WebServices.DesignVisualizationAttachmentStatus)">
      <summary>Sets the status of a design visualization</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.SetDWFPublishFolderLocation(System.String)">
      <summary>Sets the location where DWF files are published to.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.SetDWFPublishPreserveFolderHierarchy(System.Boolean)">
      <summary>Sets the DWF Publish Preserve Folder Hierarchy option.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.SetEnableDWFAttachment(System.Boolean)">
      <summary>Sets whether DWF attachments are enabled.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.SetEnableItemPropertyWritebackToFiles(System.Boolean)">
      <summary>Sets the value of the administrator option for enabling item property write back to files</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.SetEnforceInventorProjectFile(System.Boolean)">
      <summary>Sets the "Enforce Inventor Project File" property.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.SetEnforceWorkingFolder(System.Boolean)">
      <summary>Enables or disables the Enforce Working Folder setting.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.SetFileStatusByMasterId(System.Int64,Autodesk.Connectivity.WebServices.FileStatus)">
      <summary>Sets the status of a File.</summary>
      <returns>The latest version of the file, including the updated status.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.SetFileStatusByMasterIds(System.Int64[],Autodesk.Connectivity.WebServices.FileStatus)">
      <summary>Sets the status for a set of Files.</summary>
      <returns>An array of latest file versions, including the updated status.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.SetInventorProjectFileLocation(System.String)">
      <summary>Sets the Vault location of the Inventor project file to use. This setting is only valid if the Enforce Inventor Project File setting is true.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.SetRequiredWorkingFolderLocation(System.String)">
      <summary>Sets the Require Working Folder setting.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.SetTrackFileStatus(System.Boolean)">
      <summary>Turn on or off the option to track file status.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.SetTurnOffWarningForUserGeneratedDesignVisualization(System.Boolean)">
      <summary>Sets the setting that indicates if a warning should be displayed for user generated design visualization files.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.SetUniqueFileNameRequired(System.Boolean)">
      <summary>Sets whether filenames must be unique.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.SetWorkspaceSyncFileLimit(System.UInt32)">
      <summary>Sets the maximum number of files for a Workspace Sync operation.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.UndoCheckoutFile(System.Int64,Autodesk.Connectivity.WebServices.ByteArray@)">
      <summary>Performs the undo checkout operation.</summary>
      <returns>A File object representing the latest checked-in version of the file.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.UnvoidPropertyDefinitions(System.Int64[])">
      <summary>Undoes a void of property definitions.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.UpdateFileAssociationReferences(System.Int64[],System.String[],Autodesk.Connectivity.WebServices.FileAssocParamArray[])">
      <summary>Updates meta-data to an existing file association.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.UpdateFileNamingScheme(Autodesk.Connectivity.WebServices.FileNmngSchm)">
      <summary>Updates a file naming scheme.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.UpdateFileProperties(System.Int64[],Autodesk.Connectivity.WebServices.PropInstParamArray[])">
      <summary>Updates the properties on a checked out file.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.UpdateFilePropertyDefinitions(System.Int64[],System.Int64[],System.Int64[],System.String)">
      <summary>Updates the property definitions associated with a set of files.</summary>
      <returns>The updated File objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.UpdateFolderName(System.Int64,System.String)">
      <summary>Sets or modifies the folder name.</summary>
      <returns>The Folder object with the updated folder name.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.UpdateFolderUNCPath(System.Int64,System.String)">
      <summary>Sets or modifies the UNC path of a Folder.</summary>
      <returns>The Folder object with the updated UNC path.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.UpdateLabel(System.Int64,System.String,System.String)">
      <summary>Sets or modifies the name and comment of a label.</summary>
      <returns>The Label object with the updated name and comment.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.UpdateNumberingScheme(System.Int64,System.String,Autodesk.Connectivity.WebServices.NumSchmField[],System.Boolean)">
      <summary>Updates an existing numbering scheme.</summary>
      <returns>The updated numbering scheme object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.ValidateBOMByFileId(System.Int64)">
      <summary>Checks to make sure a BOM contains no errors.</summary>
      <returns>Returns true if the BOM is valid. Returns false if the BOM contains errors.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentService.VoidPropertyDefinitions(System.Int64[])">
      <summary>Voids the specified property definitions.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DocumentService.AddFileEvents">
      <summary>A collection of Web Service Command Events for add File operations.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DocumentService.AddFolderEvents">
      <summary>A collection of Web Service Command Events for add Folder operations.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DocumentService.AllowInvokeEvents">
      <summary>Tells if invoke events should fire. This value is true by default.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DocumentService.CheckinFileEvents">
      <summary>A collection of Web Service Command Events for checkin File operations.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DocumentService.CheckoutFileEvents">
      <summary>A collection of Web Service Command Events for checkout File operations.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DocumentService.DeleteFileEvents">
      <summary>A collection of Web Service Command Events for delete File operations.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DocumentService.DeleteFolderEvents">
      <summary>A collection of Web Service Command Events for delete Folder operations.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DocumentService.DownloadFileEvents">
      <summary>A collection of Web Service Command Events for download File operations.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DocumentService.MoveFileEvents">
      <summary>A collection of Web Service Command Events for move File operations.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DocumentService.MoveFolderEvents">
      <summary>A collection of Web Service Command Events for move Folder operations.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DocumentService.SecurityHeader">
      <summary>The security header. (from IWebService)</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DocumentService.WebServiceManager">
      <summary>A reference to the WebServiceManager that created this object. Value may be null.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.DocumentService.PostInvokeEvents">
      <summary>Subscribe to this event to get a notification after every web service call.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.DocumentService.PreInvokeEvents">
      <summary>Subscribe to this event to get a notification before every web service call.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.AddFolderWithCategory(System.String,System.Int64,System.Boolean,System.Int64)">
      <summary>Creates a new Folder and assigns it to a Category.</summary>
      <returns>A Folder object representing the newly created folder.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.AddUploadedFileWithBehaviors(System.Int64,System.String,System.String,System.DateTime,Autodesk.Connectivity.WebServices.FileAssocParam[],Autodesk.Connectivity.WebServices.BOM,Autodesk.Connectivity.WebServices.FileClassification,System.Boolean,Autodesk.Connectivity.WebServices.ByteArray,System.Int64)">
      <summary>Creates a File object with Behaviors based on binary data previously uploaded.</summary>
      <returns>A File object representing the file in the Vault.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.DeleteLifeCycleDefinition(System.Int64)">
      <summary>Deletes a Life Cycle Definition along with all its states and transitions.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.DeleteLifeCycleStates(System.Int64[])">
      <summary>Deletes a set of Life Cycle States.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.FindFileElementFacetSetsBySearchClause(Autodesk.Connectivity.WebServices.FileElementSrchClause,Autodesk.Connectivity.WebServices.FacetSetFilter,System.Boolean,Autodesk.Connectivity.WebServices.SrchCond[],System.Int64[],System.Int64[])">
      <summary>Gets a set of file element values for a given set of property definitions.</summary>
      <returns>The properties that have have values for the given search criteria.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.FindFileElementsBySearchClause(Autodesk.Connectivity.WebServices.FileElementSrchClause,System.Int32@,Autodesk.Connectivity.WebServices.SrchCond[],System.Int64[],System.Int64[],System.Int32@)">
      <summary>Gets a set of file elements and their properties for a given set of search conditions.</summary>
      <returns>Returns the matching elements along with all the related element property values.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.GetAllLifeCycleDefinitions">
      <summary>Gets all the Life Cycle Definitions in the system.</summary>
      <returns>All the Life Cycle Definitions in the system.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.GetAllowedFileLifeCycleStateTransitionIds">
      <summary>Get the Life Cycle Transitions that the current user is allowed to perform.</summary>
      <returns>An array of Life Cycle Transition IDs.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.GetBehaviorConfigurationsByNames(System.String[])">
      <summary>Gets a set of behavior configuration object based on their names.</summary>
      <returns>An array of corresponding behavior configuration objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.GetJobTypesByLifeCycleStateTransitionIds(System.Int64[])">
      <summary>Gets the custom jobs that get fired for a set of transitions.</summary>
      <returns>An array of arrays of strings telling the jobs fired during the transitions. The top level array matches the size of the input array.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.GetLatestRevisionLifeCycleStatesByDefinitionId(System.Int64)">
      <summary>Gets those lifecycle states being used by any latest version of any revision.</summary>
      <returns>An array of states currently in use as a latest state in a revision.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.GetLifeCycleDefinitionDeleteRestrictionsById(System.Int64)">
      <summary>Gets the restrictions on deleting a life cycle definition.</summary>
      <returns>A list of delete restrictions, or null if there are no restrictions.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.GetLifeCycleDefinitionsByIds(System.Int64[])">
      <summary>Gets a Life Cycle Definition object based on its ID.</summary>
      <returns>The resulting Life Cycle Definition objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.GetLifecycleRollbackTargetFile(System.Int64)">
      <summary>Gets the target file iteration for rolling back the file's lifecycle state</summary>
      <returns>the target file iteration to rollback to</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.GetLifeCycleStateIdsByFileMasterIds(System.Int64[])">
      <summary>Get the life cycle states for a set of files.</summary>
      <returns>The file master ID and life cycle state ID values. The file master ID is the 'EntityId' part of the IdPair object. NOTE: The return order may not be the same
as the order of the input array.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.GetLifeCycleStateIdsByFolderIds(System.Int64[])">
      <summary>Deprecated - do not use</summary>
      <returns>Deprecated - do not use</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.GetLifeCycleStatesByIds(System.Int64[])">
      <summary>Gets a set of Life Cycle State objects based on their IDs.</summary>
      <returns>The resulting Life Cycle State objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.GetLifeCycleStateTransitionsByIds(System.Int64[])">
      <summary>Gets a set of Life Cycle Transitions by their IDs.</summary>
      <returns>The resulting Life Cycle Transition objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.GetRestrictFileLifeCycleStateChangeToChangeOrder">
      <summary>Gets the value of the "Restrict file lifecycle state changes to change orders" setting</summary>
      <returns>The restrict file lifecycle state changes to change orders setting</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.GetStateACLByLifeCycleStateId(System.Int64)">
      <summary>Gets the security information for a life cycle state.</summary>
      <returns>The security information for that state.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.GetTransitionACEsByTransitionId(System.Int64)">
      <summary>Gets the security information.</summary>
      <returns>The security information for the transition.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.RollbackToTargetFile(System.Int64,System.Int32)">
      <summary>Executes a file rollback to the specified target file "MaxCkInVerNum"</summary>
      <returns>the new tip file iteration</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.SetRestrictFileLifeCycleStateChangeToChangeOrder(Autodesk.Connectivity.WebServices.RestrictLifecycleChange)">
      <summary>Sets the value for the "Restrict file lifecycle state changes to change orders" setting.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.UpdateBehaviorAssignmentTypes(System.String,Autodesk.Connectivity.WebServices.AllowNoBehavior,System.Int64[],Autodesk.Connectivity.WebServices.BehaviorAssignmentType[],System.Boolean)">
      <summary>Updates the behavior classes for specific behavior objects.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.UpdateFileCategories(System.Int64[],System.Int64[],System.String)">
      <summary>Updates the category for a set of files.</summary>
      <returns>The updated file objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.UpdateFileLifeCycleDefinitions(System.Int64[],System.Int64[],System.Int64[],System.String)">
      <summary>Updates the life cycle definition and state for a set of files.</summary>
      <returns>The updated file objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.UpdateFileLifeCycleStates(System.Int64[],System.Int64[],System.String)">
      <summary>Updates the life cycle state for a set of files.</summary>
      <returns>The updated file objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.UpdateFileRevisionNumbers(System.Int64[],System.String[],System.String)">
      <summary>Updates the revision numbers for a set of files.</summary>
      <returns>The updated file objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.UpdateFolderCategories(System.Int64[],System.Int64[])">
      <summary>Updates the categories for a set of folders.</summary>
      <returns>The updated Folder objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.UpdateFolderLifeCycleDefinitions(System.Int64[],System.Int64[],System.Int64[],System.String)">
      <summary>
        <para>Updates the life cycle definition and state for a set of Folders.</para>
      </summary>
      <returns>The updated Folder objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.UpdateFolderLifeCycleStates(System.Int64[],System.Int64[],System.String)">
      <summary>Updates the lifecycle states for a set of Folders.</summary>
      <returns>The updated Folder objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.UpdateFolderProperties(System.Int64[],Autodesk.Connectivity.WebServices.PropInstParamArray[])">
      <summary>Updates the property values on a set of Folders.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.UpdateFolderPropertyDefinitions(System.Int64[],System.Int64[],System.Int64[])">
      <summary>Updates the property definitions associated with a set of Folders.</summary>
      <returns>The updated Folder objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.UpdateLifeCycleStateTransitionJobTypes(System.Int64,System.String[])">
      <summary>Updates the custom jobs fired for a lifecycle state transition.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.UpdateRevisionDefinitionAndNumbers(System.Int64[],System.Int64[],System.String[],System.String)">
      <summary>Updates the revision definition and revision number for a set of files.</summary>
      <returns>An array of updated file objects.</returns>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.AllowInvokeEvents">
      <summary>Tells if invoke events should fire. This value is true by default.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.SecurityHeader">
      <summary>The security header. (from IWebService)</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.UpdateFileLifecycleStateEvents">
      <summary>A collection of Web Service Command Events for File lifecycle change operations.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.WebServiceManager">
      <summary>A reference to the WebServiceManager that created this object. Value may be null.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.PostInvokeEvents">
      <summary>Subscribe to this event to get a notification after every web service call.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.DocumentServiceExtensions.PreInvokeEvents">
      <summary>Subscribe to this event to get a notification before every web service call.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.DownloadFileCommandEventArgs.AddRestriction(Autodesk.Connectivity.Extensibility.Framework.ExtensionRestriction)">
      <summary>Adds a restriction, which blocks the command. This function should only be called during the GetRestrictions event.</summary>
      <param>The reason that the command cannot run.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DownloadFileCommandEventArgs.Guid">
      <summary>The unique identifier for the event.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.DownloadFileCommandEventArgs.Status">
      <summary>The status of the command. Used in the Post event to see of the command was successful.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.DownloadFileCommandEventArgs.FileIds">
      <summary>The files being downloaded.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.EditChangeOrderCommandEventArgs.AddRestriction(Autodesk.Connectivity.Extensibility.Framework.ExtensionRestriction)">
      <summary>Adds a restriction, which blocks the command. This function should only be called during the GetRestrictions event.</summary>
      <param>The reason that the command cannot run.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EditChangeOrderCommandEventArgs.Guid">
      <summary>The unique identifier for the event.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EditChangeOrderCommandEventArgs.ReturnValue">
      <summary>The updated Change Order.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EditChangeOrderCommandEventArgs.Status">
      <summary>The status of the command. Used in the Post event to see of the command was successful.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.EditChangeOrderCommandEventArgs.ChangeOrderId">
      <summary>The Change Order being edited.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.EditItemCommandEventArgs.AddRestriction(Autodesk.Connectivity.Extensibility.Framework.ExtensionRestriction)">
      <summary>Adds a restriction, which blocks the command. This function should only be called during the GetRestrictions event.</summary>
      <param>The reason that the command cannot run.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EditItemCommandEventArgs.Guid">
      <summary>The unique identifier for the event.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EditItemCommandEventArgs.ReturnValue">
      <summary>The updated Items.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EditItemCommandEventArgs.Status">
      <summary>The status of the command. Used in the Post event to see of the command was successful.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.EditItemCommandEventArgs.ItemRevisionIds">
      <summary>The Items being edited.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Email.Body">
      <summary>The body of the email.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Email.Subject">
      <summary>The subject of the email.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Email.ToArray">
      <summary>A list of people to send the email to.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Ent.EntClassId">
      <summary>The entity type.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Ent.Id">
      <summary>The ID value of the entity.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntACL.ACLId">
      <summary>A unique identifier for the ACL.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntACL.EntId">
      <summary>The file or folder ID.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntACL.SysACLId">
      <summary>Reserved for future use.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntCat.CatId">
      <summary>The Category ID.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntClassAssoc.EntClassId">
      <summary>The entity class ID.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntClassAssoc.MapDirection">
      <summary>The property mapping direction.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntClassBehAssoc.BehAssignTyp">
      <summary>The tells if the behavior is active or not for this entity class.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntClassBehAssoc.EntClassId">
      <summary>The entity class ID.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntClassCfg.BhvArray">
      <summary>The behavior names associated with the entity class.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntClassCfg.DispName">
      <summary>The display name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntClassCfg.EntClassPropCfg">
      <summary>Property information.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntClassCfg.Id">
      <summary>The unique identifier.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntClassCfg.IsLnkTarget">
      <summary>If true, then this Entity Class can be the target of a Link. If false, then Links can't point to objects of this Entity Class.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntClassCtntSrcPropCfg.CanCreateNewArray">
      <summary>If true, the property can be created within the file during a writeback operation.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntClassCtntSrcPropCfg.CtntSrcPropDefArray">
      <summary>An array of property source definitions.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntClassCtntSrcPropCfg.EntClassId">
      <summary>The entity class.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntClassCtntSrcPropCfg.MapDirectionArray">
      <summary>The mapping direction for this content source.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntClassCtntSrcPropCfg.MapTypArray">
      <summary>The mapping type. Tells when data is read from a conent source.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntClassCtntSrcPropCfg.PriorityArray">
      <summary>The priority of the mapping. Used for cases where the multiple content source properties map to the same property definition. The non-null value with the
lowest number is used.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntClassPropCfg.CtntSrcMap">
      <summary>If true, the user defined properties can be mapped to a content source property.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntClassPropCfg.CtntSrcMapTypes">
      <summary>The content source property definition types supported for the mapping.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntClassPropCfg.Equiv">
      <summary>If true, the system can report if a user defined property value is not equivalent to a mapped content source property. If false, the system will not detect if
the values are equivalent or not.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntClassPropCfg.Policy">
      <summary>If true, the system supports property constraints. If false, the system does not support property constraints.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntClassPropCfg.Searchable">
      <summary>If true the properties are searchable. If false, the properties cannot be searched.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntClassPropCfg.Visible">
      <summary>If true, the properties are visible. If false, the properties are hidden.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntLfCyc.Consume">
      <summary>Gets if the Entity is in a consumable (released) state.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntLfCyc.LfCycDefId">
      <summary>The lifecycle definition ID.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntLfCyc.LfCycStateId">
      <summary>The lifecycle state ID.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntLfCyc.Obsolete">
      <summary>Indicates if entity is in a life cycle state that is designated as obsolete</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntOwn.EntId">
      <summary>The entity ID.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntOwn.ExpDate">
      <summary>The expiration date and time of the lease.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntOwn.IsExp">
      <summary>If true, the lease has expired. If false, the lease is still active.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntOwn.IsLocalOwn">
      <summary>If true, the site that currently connected to has ownership. If false, another workgroup has ownerhsip.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntOwn.WkgrpId">
      <summary>The workgroup that has ownership.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntsAndACLs.ACLArray">
      <summary>An array of referenced ACL objects.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.EntsAndACLs.EntACLArray">
      <summary>An array of entities and related ACL IDs.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Facet.HitCount">
      <summary>The number of things that have this value.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Facet.Val">
      <summary>A property value.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FacetSet.FacetArray">
      <summary>The collection of property values.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FacetSet.PropDef">
      <summary>The property definition that corresponds to the value set.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FacetSetFilter.FilterPropDefIdArray">
      <summary>An array of file element property definitions.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FacetSetFilter.Typ">
      <summary>Tells if 'FilterPropDefIdArray' specifies the properties to be included or excluded.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Facility.DisplayName">
      <summary>The display name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Facility.Name">
      <summary>The system name (key) of the facility.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.File.Cat">
      <summary>Category information about this file. For files in the "None" category, the FileCat object will have an ID of 0.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.File.CheckedOut">
      <summary>If true, then the latest version of this file is in the checked-out state. Whenever a file is checked-out, a new version is created as a placeholder.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.File.CkInDate">
      <summary>The date and time that the file was checked in. This property is only valid if CheckedOut is false.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.File.CkOutMach">
      <summary>The network ID of the computer that checked out the file. This parameter is valid only if CheckedOut is true.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.File.CkOutSpec">
      <summary>The path on the client's computer that the file was checked out to. This parameter is valid only if CheckedOut is true.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.File.CkOutUserId">
      <summary>The ID of the user that has the file checked out. This parameter is valid only if CheckedOut is true.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.File.Cksum">
      <summary>The file's checksum. This parameter is valid only if CheckedOut is false.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.File.Cloaked">
      <summary>If true, the logged-in user is restricted from seeing this file. The only valid data in a cloaked File is the Id and MasterId. All other data will be null or
0.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.File.Comm">
      <summary>A comment string set by the user. If CheckedOut is true, then this property contains the comment set during checkout. If CheckedOut is false, then this
property contains the comment set during check-in. Checking in a file, resets the comment for that version.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.File.ControlledByChangeOrder">
      <summary>If true, the file is controlled by a change order. If false, the file is not controlled by a change order.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.File.CreateDate">
      <summary>Tells the date and time that this version of the file was created in the Vault. For the first version of a file, this date will match CkInDate. For later
versions, this value will have the time when the previous version was checked out.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.File.CreateUserId">
      <summary>The ID of the user who checked-in or uploaded this file.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.File.CreateUserName">
      <summary>The Name of the user who checked-in or uploaded this file.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.File.DesignVisAttmtStatus">
      <summary>The status of a design visualization (.dwf) file.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.File.FileClass">
      <summary>The classification of the file.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.File.FileLfCyc">
      <summary>Life cycle information about this file.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.File.FileRev">
      <summary>Revision information about this file. For files in "NULL" revisions, the FileRev object will have an ID of 0.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.File.FileSize">
      <summary>The size, in bytes, of the file. This property is only valid if CheckedOut is false.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.File.FileStatus">
      <summary>Tells if the file needs updating or not.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.File.FolderId">
      <summary>The ID of the parent folder.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.File.Hidden">
      <summary>If true, the file should be hidden by the client.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.File.Id">
      <summary>A unique identifier for the file.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.File.IsOnSite">
      <summary>In a multi-site environment, this property tells if the file is on the local site. True means that the file is on the local site. False means that the file is
not on the local site. This value will always be true on a single-site environment.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.File.Locked">
      <summary>Tells if the file can be modified by the logged in user.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.File.MasterId">
      <summary>A unique identifier used to group all the versions of a file. In other words, different versions of a file will have a different Id but the same MasterId.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.File.MaxCkInVerNum">
      <summary>The maximum version of this file currently checked-in to the vault.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.File.ModDate">
      <summary>The last modified date of the file. This value is set by the client that uploaded the file.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.File.Name">
      <summary>The name of the file. This value applies to all versions of the file. 'VerName' contains the name for a specific version of a file.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.File.VerName">
      <summary>The historical name that matches this version of the file.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.File.VerNum">
      <summary>The version of the file. A file that has just been added to the Vault, will have 1 as its VerNum.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileArray.Files">
      <summary>An array of files.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileAssoc.CldFile">
      <summary>The child file.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileAssoc.CorrectRev">
      <summary>If false, the associated file is not in the expected revision. Only applies to 'get latest' calls.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileAssoc.ExpectedVaultPath">
      <summary>The Vault path where the file expects the child to be.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileAssoc.Id">
      <summary>A unique identifier describing the dependency. Set to -1 if creating a new dependency.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileAssoc.ParFile">
      <summary>The parent file.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileAssoc.RefId">
      <summary>The identifier for the reference within the file. This value needs to be understood by the resolver when updating file references. If this value is null, it is
not possible to fix broken references.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileAssoc.Source">
      <summary>Metadata regarding the source of the link (ex. "Inventor" or "AutoCAD").<!--DXMETADATA end --> This is mainly for use by CAD plug-ins. When creating new
associations, it's recommended to set this value to null.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileAssoc.Typ">
      <summary>Enum indicating the type of association.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileAssoc.VaultPathChanged">
      <summary>True means that the reference may be broken and should be fixed when the file is downloaded. False means that the file reference should not be broken.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileAssocArray.FileAssocs">
      <summary>An array of FileAssoc objects.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileAssocLite.CldFileId">
      <summary>The ID of the child file.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileAssocLite.CorrectRev">
      <summary>If false, the associated file is not in the expected revision. Only applies to 'get latest' calls.<!--DXMETADATA end --></summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileAssocLite.ExpectedVaultPath">
      <summary>The Vault path where the parent file expects the child to be.<!--DXMETADATA end --></summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileAssocLite.Id">
      <summary>A unique identifier for the dependency. Set to -1 if creating a new dependency.<!--DXMETADATA end --></summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileAssocLite.ParFileId">
      <summary>The ID of the parent file.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileAssocLite.RefId">
      <summary>The identifier for the reference within the file. This value needs to be understood by the resolver when updating file references. If this value is null, it is
not possible to fix broken references.<!--DXMETADATA end --></summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileAssocLite.Source">
      <summary>Metadata regarding the source of the link (ex. "Inventor" or "AutoCAD").<!--DXMETADATA end --> This is mainly for use by CAD plug-ins. When creating new
associations, it's recommended to set this value to null.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileAssocLite.Typ">
      <summary>The type of association.<!--DXMETADATA end --></summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileAssocLite.VaultPathChanged">
      <summary>True means that the reference may be broken and should be fixed when the file is downloaded. False means that the file reference should not be broken.
<!--DXMETADATA end --></summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileAssocParam.CldFileId">
      <summary>The ID of the child file.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileAssocParam.ExpectedVaultPath">
      <summary>The Vault path where you expect the child to be.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileAssocParam.RefId">
      <summary>The identifier for the reference within the file. This value needs to be understood by the resolver when updating file references. Pass in null if the file
type has no resolver or you don't know that the resolver expects.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileAssocParam.Source">
      <summary>Metadata indicating which application 'owns' the association. It is recommended to pass in null or the value from the previous version.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileAssocParam.Typ">
      <summary>Tells the associaiton type.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileAssocParamArray.FileAssocs">
      <summary>An array of file association parameters.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileAssocRestric.Code">
      <summary>A numeric value explaining the restriction. See table below.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileAssocRestric.FileId">
      <summary>The ID of the file which is the child of the restriction.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileAssocRestric.FileName">
      <summary>The Name of the file which is the child of the restriction.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileAssocRestric.Info">
      <summary>Contains more information about the restriction. See table below.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileCat.CatId">
      <summary>The Category ID.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileCat.CatName">
      <summary>The Category name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileDelRestric.Code">
      <summary>A numeric value explaining the restriction.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileDelRestric.Info">
      <summary>The value is dependent on the code.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileDelRestricArray.FileDeleteRestrictions">
      <summary>An array of delete restrictions.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileDelRestricEx.canOverde">
      <summary>Tells if an administrator can override the restriction.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileDelRestricEx.Code">
      <summary>The restriction code.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileDelRestricEx.FileId">
      <summary>The File ID.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileDelRestricEx.MasterId">
      <summary>The File master ID.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileDelRestricEx.Param">
      <summary>Extra data depending on the Code.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileDelRestricEx.Param2">
      <summary>Extra data depending on the Code.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileDelRestricEx.VerNum">
      <summary>The version number.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileDelStatus.Code">
      <summary>A value of 1 means that the File has been deleted. A value of 2 means that the File has not been deleted.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileDelStatus.File">
      <summary>The File object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileElement.FileExt">
      <summary>The file extension.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileElement.FileMasterId">
      <summary>The file Master ID.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileElement.Id">
      <summary>The ID of the file element.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileElement.PropArray">
      <summary>The set of properties on the element.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileElement.ResId">
      <summary>The file ID.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileElementProp.PropDef">
      <summary>The file element property definition.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileElementProp.Val">
      <summary>The property value.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileElementPropDef.DispName">
      <summary>The display name of the property.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileElementPropDef.Id">
      <summary>The ID of the object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileElementPropDef.IsFacet">
      <summary>If true, it can be used for getting a facet set. If false, it cannot be used to get a facet set.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileElementPropDef.IsSys">
      <summary>If true, the object is a system property.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileElementPropDef.Typ">
      <summary>The data type of the property values.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileElementSrchClause.CondArray">
      <summary>A set of search conditions.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileElementSrchClause.Rule">
      <summary>The rule for how the conditions interact with each other.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileElementSrchCondition.FileElementPropDefId">
      <summary>The ID of the property definition to search on.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileElementSrchCondition.Oper">
      <summary>The operator used for the search.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileElementSrchCondition.Val">
      <summary>The value to use for the search.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileFolder.File">
      <summary>A Vault file that lives in the folder.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileFolder.Folder">
      <summary>A Vault folder.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileLfCyc.Consume">
      <summary>Tells if the current file version is in a consumable (released) state.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileLfCyc.LfCycDefId">
      <summary>The ID of the life cycle definition for the latest file within the same revision.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileLfCyc.LfCycStateId">
      <summary>The ID of the life cycle state for the latest file within the same revision.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileLfCyc.LfCycStateName">
      <summary>The name of the life cycle state for the current file version.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileLfCyc.Obsolete">
      <summary>Indicates if file is in a life cycle state that is designated as an obsolete state</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileNameAndURL.FileSize">
      <summary>The size of the file.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileNameAndURL.Name">
      <summary>The name of the file.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileNameAndURL.URL">
      <summary>The URL of the file.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileNmngDescr.Num">
      <summary>The numeric part of the name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileNmngDescr.SchemeId">
      <summary>The scheme used to generate the name</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileNmngDescr.Val">
      <summary>The full generated file name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileNmngSchm.Descr">
      <summary>A description of the scheme.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileNmngSchm.FieldLen">
      <summary>The number of digits for the number. The number will always fill up the FieldLen (ex. for a length of 4, a number might be '0005').</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileNmngSchm.Id">
      <summary>A unique identifier for the scheme.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileNmngSchm.IsDflt">
      <summary>If true, than the naming scheme is the default scheme.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileNmngSchm.Name">
      <summary>The unique name of the scheme.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileNmngSchm.Prefix">
      <summary>A prefix to the file name. Null means no prefix.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileNmngSchm.StartNum">
      <summary>The first number in the scheme.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileNmngSchm.Suffix">
      <summary>A suffix to the file name. Null means not suffix.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FilePath.File">
      <summary>A Vault file that lives in the folder.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FilePath.Path">
      <summary>A Vault path.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FilePathArray.FilePaths">
      <summary>An array of FilePath objects.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileRenameRestric.Code">
      <summary>A numeric value explaining the restriction.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileRenameRestric.FileId">
      <summary>Contains a file ID.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileRenameRestric.Info">
      <summary>Contains more information about the restriction.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileRenameRestricArray.FileRenameRestrictions">
      <summary>An array of rename restrictions.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileRev.Label">
      <summary>The revision label.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileRev.MaxConsumeFileId">
      <summary>The ID of the file with the highest version that is in a consumable life cycle state. Will be -1 if there is no consumable version.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileRev.MaxFileId">
      <summary>The ID of the file with the highest version for this revision.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileRev.MaxRevId">
      <summary>The maximum revision ID for a file master.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileRev.Order">
      <summary>Indicates how the revision should be ordered in relation to other revisions. Lower order revisions should be listed before higher order revisions.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileRev.RevDefId">
      <summary>The ID of the revision definition.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileRev.RevId">
      <summary>The unique identifier for this file revision.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.FilestoreService.DownloadFilePart(System.Byte[],System.Int64,System.Int64,System.Boolean)">
      <summary>Downloads the binary content of a file.</summary>
      <returns>The binary contents the file part.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.FilestoreService.GetContentSourceDeepIndex(System.Byte[],System.Byte[],System.Boolean)">
      <summary>Internal use only.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.FilestoreService.GetContentSourceProperties(System.Byte[],System.Boolean)">
      <summary>Internal use only.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.FilestoreService.GetContentSourcePropertyDefinitions(System.Byte[],System.Boolean)">
      <summary>Gets the property definitions internal to the file.<!--DXMETADATA end --></summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.FilestoreService.GetContentSourceText(System.Byte[],System.Boolean)">
      <summary>Internal use only.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.FilestoreService.GetMaximumPartSize">
      <summary>Gets the maximum size for a binary data transfer.<!--DXMETADATA end --></summary>
      <returns>The maximum size file (in bytes) allowed for a file part. If a file is bigger than the maximum size, the file will need to be uploaded or downloaded in
multiple parts.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.FilestoreService.GetRegisteredExternalFileUploaderNames">
      <summary>Internal use only.</summary>
      <returns>Internal use only.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.FilestoreService.UploadExternalFile(System.String,Autodesk.Connectivity.WebServices.NameValuePair[])">
      <summary>Internal use only.</summary>
      <returns>Internal use only.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.FilestoreService.UploadFilePart(System.Byte[])">
      <summary>Uploads the binary content of a file.</summary>
      <returns>The upload ticket.</returns>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FilestoreService.AllowInvokeEvents">
      <summary>Tells if invoke events should fire. This value is true by default.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FilestoreService.CompressionHeaderValue">
      <summary>Information about the compression on the binary data.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FilestoreService.FileTransferHeaderValue">
      <summary>Information on the file transfer.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FilestoreService.SecurityHeader">
      <summary>The security header. (from IWebService). This value will always be null.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FilestoreService.WebServiceManager">
      <summary>A reference to the WebServiceManager that created this object. Value may be null.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.FilestoreService.PostInvokeEvents">
      <summary>Subscribe to this event to get a notification after every web service call.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.FilestoreService.PreInvokeEvents">
      <summary>Subscribe to this event to get a notification before every web service call.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.FilestoreVaultService.GetAllKnowledgeVaults">
      <summary>Gets all Knowledge Vaults on the server.<!--DXMETADATA end --></summary>
      <returns>A list of all Knowledge Vaults.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.FilestoreVaultService.GetKnowledgeVaultById(System.Int64)">
      <summary>Gets a Knowledge Vault based on its ID.<!--DXMETADATA end --></summary>
      <returns>The corresponding Knowledge Vault object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.FilestoreVaultService.GetKnowledgeVaultByName(System.String)">
      <summary>Gets a Knowledge Vault based on its name.<!--DXMETADATA end --></summary>
      <returns>The corresponding Knowledge Vault object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.FilestoreVaultService.GetKnowledgeVaultsByIds(System.Int64[])">
      <summary>Get multiples knowledge vaults by their IDs.</summary>
      <returns>An array of Knowledge Vault objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.FilestoreVaultService.GetKnowledgeVaultsByNames(System.String[])">
      <summary>Gets Knowledge Vaults based on their names.<!--DXMETADATA end --></summary>
      <returns>An array of corresponding Knowledge Vault objects.</returns>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FilestoreVaultService.AllowInvokeEvents">
      <summary>Tells if invoke events should fire. This value is true by default.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FilestoreVaultService.SecurityHeader">
      <summary>The security header. (from IWebService). This value will always be null.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FilestoreVaultService.WebServiceManager">
      <summary>A reference to the WebServiceManager that created this object. Value may be null.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.FilestoreVaultService.PostInvokeEvents">
      <summary>Subscribe to this event to get a notification after every web service call.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.FilestoreVaultService.PreInvokeEvents">
      <summary>Subscribe to this event to get a notification before every web service call.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileTransferHeader.Compression">
      <summary>The type of compression used.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileTransferHeader.Extension">
      <summary>The file externsion. Ex: ".dwg"</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileTransferHeader.Identity">
      <summary>
        <para>An identifier for cases when a file needs to bo uploaded in multiple parts. In these cases, a random GUID should be created, and that GUId value shouyld
used for all upload calls.</para>
        <para>This value does not need to be set in cases were the entire file is uploaded in one part.</para>
      </summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileTransferHeader.IsComplete">
      <summary>Used to indicate that a file upload is complete. For cases where the entire file is uploaded in a single part, this value is always true. For cases where the
file is uploaded across multiple parts, this value is true for the last part and false for all other parts.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileTransferHeader.UncompressedSize">
      <summary>The size of the byte array before compression.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FileTransferHeader.Vault">
      <summary>The name of the Vault.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FixedTxtField.FieldTyp">
      <summary>The type of field. Inherited from NumSchmField.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FixedTxtField.FixedTxtVal">
      <summary>A block of static text.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FixedTxtField.Name">
      <summary>The name of the field. Inherited from NumSchmField.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Folder.Cat">
      <summary>The folder category.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Folder.Cloaked">
      <summary>If true, the logged-in user is restricted from seeing this folder. The only valid data in a cloaked Folder is the ID. All other data will be null or 0.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Folder.CreateDate">
      <summary>The date and time that the folder was created.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Folder.CreateUserId">
      <summary>The ID of the user who created the folder.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Folder.CreateUserName">
      <summary>The name of the user who created the folder.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Folder.FullName">
      <summary>The full vault path (example "$/Folder1").</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Folder.FullUncName">
      <summary>The full UNC path or null if no UNC path exists.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Folder.Id">
      <summary>A unique identifier for the folder.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Folder.IsLib">
      <summary>Tells if the folder is a library.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Folder.LfCyc">
      <summary>The life cycle state.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Folder.Locked">
      <summary>Tells if the folder can be modified by the logged in user.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Folder.Name">
      <summary>The name of the folder (example "Folder1").</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Folder.NumClds">
      <summary>The number of child folders.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Folder.ParId">
      <summary>The ID of the folder above the current folder. A value of -1 means that the folder has no parent.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FolderArray.Folders">
      <summary>An array of folders.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FolderCat.CatId">
      <summary>The Category ID.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FolderCat.CatName">
      <summary>The Category name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FolderDelRestric.Code">
      <summary>A numeric value explaining the restriction.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FolderDelRestric.Info">
      <summary>Information about the error.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FolderDelRestric.Info2">
      <summary>More information about the error.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FolderDelRestricArray.FolderDeleteRestrictions">
      <summary>An array of folder delete restrictions.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ForumService.GetMessageGroupsByForumId(System.Int64)">
      <summary>Gets all of the Messages and attachments for a Forum.</summary>
      <returns>An array of all Message Groups for the Forum.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ForumService.GetMessagesByForumId(System.Int64)">
      <summary>Gets all the Messages for a Forum.</summary>
      <returns>An array of Messages for the Forum.</returns>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ForumService.AllowInvokeEvents">
      <summary>Tells if invoke events should fire. This value is true by default.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ForumService.SecurityHeader">
      <summary>The security header. (from IWebService)</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ForumService.WebServiceManager">
      <summary>A reference to the WebServiceManager that created this object. Value may be null.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.ForumService.PostInvokeEvents">
      <summary>Subscribe to this event to get a notification after every web service call.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.ForumService.PreInvokeEvents">
      <summary>Subscribe to this event to get a notification before every web service call.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FreeTxtField.DfltVal">
      <summary>The default value.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FreeTxtField.FieldTyp">
      <summary>The type of field. Inherited from NumSchmField.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FreeTxtField.FreeTxtVal">
      <summary>The user-specified value.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FreeTxtField.MaxLen">
      <summary>The maximum length for the field.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FreeTxtField.MinLen">
      <summary>The minimum length for the field.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.FreeTxtField.Name">
      <summary>The name of the field. Inherited from NumSchmField.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.GetPromoteOrderResults.NonPrimaryArray">
      <summary>An array of Item Ids that will not have new iterations created for this promote</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.GetPromoteOrderResults.PrimaryArray">
      <summary>An array of component IDs in the order that they should be promoted</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Group.Auth">
      <summary>The authentication type.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Group.CreateDate">
      <summary>The date the Group was created.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Group.CreateUserId">
      <summary>The Id of the User that created the Group.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Group.EmailDL">
      <summary>The email address (distribution list) for the Group.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Group.Id">
      <summary>The identifier for the object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Group.IsActive">
      <summary>If true, the Group is active. If false, the Group is inactive.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Group.IsSys">
      <summary>If true, the Group is a system group. If false, the Group is user-defined.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Group.Name">
      <summary>The unique name of the Group.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.GroupInfo.Group">
      <summary>A Group.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.GroupInfo.Groups">
      <summary>The immediate Groups in the Group.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.GroupInfo.Roles">
      <summary>The Roles associated with the Group.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.GroupInfo.Users">
      <summary>The immediate Users in the Group.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.GroupInfo.Vaults">
      <summary>The Vaults associated with the Group.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.IdentificationService.GetServerIdentities">
      <summary>Gets the locations of the Data server and Filestore server.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.IdentificationService.AllowInvokeEvents">
      <summary>Tells if invoke events should fire. This value is true by default.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.IdentificationService.SecurityHeader">
      <summary>The security header. (from IWebService)</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.IdentificationService.WebServiceManager">
      <summary>A reference to the WebServiceManager that created this object. Value may be null.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.IdentificationService.PostInvokeEvents">
      <summary>Subscribe to this event to get a notification after every web service call.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.IdentificationService.PreInvokeEvents">
      <summary>Subscribe to this event to get a notification before every web service call.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.IdPair.EntityId">
      <summary>The ID of the primary object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.IdPair.ValId">
      <summary>The value ID.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.InformationService.GetServerName">
      <summary>Gets the name of the server.</summary>
      <returns>The name of the server.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.InformationService.GetSupportedFacilities">
      <summary>Gets the Facilities available for the site.</summary>
      <returns>An array of all the installed facilities for the site.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.InformationService.GetSupportedProducts">
      <summary>Gets a list of the Autodesk Data Management products installed on the site.</summary>
      <returns>An array of installed products on the site.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.InformationService.GetSystemFacilities">
      <summary>Gets the facilites for the Autodesk Data Management system.</summary>
      <returns>The list of installed facilities for the system.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.InformationService.GetSystemProducts">
      <summary>Gets the products for the Autodesk Data Management Server.</summary>
      <returns>The list of installed products for the system.</returns>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.InformationService.AllowInvokeEvents">
      <summary>Tells if invoke events should fire. This value is true by default.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.InformationService.SecurityHeader">
      <summary>The security header. (from IWebService). This value will always be null.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.InformationService.WebServiceManager">
      <summary>A reference to the WebServiceManager that created this object. Value may be null.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.InformationService.PostInvokeEvents">
      <summary>Subscribe to this event to get a notification after every web service call.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.InformationService.PreInvokeEvents">
      <summary>Subscribe to this event to get a notification before every web service call.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Item.CadBOMStructId">
      <summary>The BOMStruct ID for the Item's type. Use GetAllBOMStructures to get a list of valid BOMStruct IDs.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Item.Cat">
      <summary>Category assigned to the item</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Item.Comm">
      <summary>Reserved for future use.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Item.ControlledByChangeOrder">
      <summary>If true, item is being controlled by a Change Order and certain properties cannot be manually changed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Item.Detail">
      <summary>A description of the version.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Item.Id">
      <summary>The unique identifier for the object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Item.IsCloaked">
      <summary>If true, the logged-in user is restricted from seeing this item. The only valid data in a cloaked Item is the Id and MasterId. All other data will be null or
0.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Item.ItemNum">
      <summary>The Item number.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Item.ItemTypId">
      <summary>The ID of the associated category.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Item.LastModDate">
      <summary>The last time the Revision was modified.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Item.LastModUserId">
      <summary>The ID of the user that made the last modification.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Item.LastModUserName">
      <summary>The name of the user that made the last modification.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Item.LfCyc">
      <summary>Life cycle assigned to the item</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Item.LfCycStateId">
      <summary>The Life Cycle State.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Item.Locked">
      <summary>Reserved for future use.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Item.MasterId">
      <summary>A unique identifier used to group all the versions of an Item. In other words, different versions of an Item will have a different Id but the same MasterId.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Item.MaxCommittedId">
      <summary>Tip committed iteration Id of the item</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Item.NumSchmId">
      <summary>The Numbering Scheme for the Item.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Item.RevId">
      <summary>The ID for the Item Revision.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Item.RevNum">
      <summary>The display value for the revision.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Item.Title">
      <summary>The display title.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Item.UnitId">
      <summary>The Unit of Measure ID.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Item.Units">
      <summary>The units for the Item.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Item.VerNum">
      <summary>The version number, which is a sequential number given to each version. For example, if this value is 5, then the object is the 5th version.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssoc.AssocMasterId">
      <summary>Item association's master Id (does not change between iterations)</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssoc.BOMCompId">
      <summary>Id of the BOM Component (only valid when BOM row is a component - not assigned an item)</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssoc.BOMOrder">
      <summary>The order that the child entry shows up in the BOM.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssoc.CldHasCldren">
      <summary>If true, the child Item has children of its own. If false, the child is a leaf.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssoc.CldItemID">
      <summary>The ID of the child Item.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssoc.CldItemMasterID">
      <summary>The Master ID of the child Item.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssoc.GroupId">
      <summary>Id of the Multiple Row grouping</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssoc.Id">
      <summary>The ID for the object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssoc.InstCount">
      <summary>Instance Count of this BOM association</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssoc.IsCad">
      <summary>Indicates if this BOM row is created and maintained by CAD data or is a manual BOM row</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssoc.IsGroup">
      <summary>Indicates if this BOM association is a grouped row of multiple BOM rows</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssoc.IsIncluded">
      <summary>Indicates if this BOM row is included or excluded from the BOM</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssoc.IsStatic">
      <summary>If true, the dependency is static. If false, the dependency is computed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssoc.ParItemID">
      <summary>The ID of the parent Item.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssoc.ParItemMasterID">
      <summary>The Master ID of the parent Item.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssoc.PositionNum">
      <summary>Assigned BOm row position number</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssoc.Quant">
      <summary>BOM row quantity</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssoc.RefDesId">
      <summary>The reference designator ID. This value will be 0 if reference designators are not available.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssoc.UnitID">
      <summary>The ID if the Unit of Measure.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssoc.Units">
      <summary>The display name for the Unit of Measure on CldItemUsage.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssoc.UnitSize">
      <summary>The unit size for this BOM row</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssocParam.BOMOrder">
      <summary>The BOM order of this association</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssocParam.CldItemID">
      <summary>The child Item ID for this association</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssocParam.EditAct">
      <summary>The edit action when updating this association</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssocParam.Id">
      <summary>The association's ID</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssocParam.InstCount">
      <summary>The instance count of the association</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssocParam.IsIncluded">
      <summary>Specifies whether or not the child of this association is included in the BOM</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssocParam.IsStatic">
      <summary>Specifies if this is a static association or not</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssocParam.PositionNum">
      <summary>The position number of this association</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssocParam.Quant">
      <summary>The association's quantity value</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssocParam.UnitID">
      <summary>The unit ID for this association</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssocParam.UnitSize">
      <summary>The unit size for this association</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssocProp.AssocId">
      <summary>Item Association Id</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssocProp.PropDefId">
      <summary>Property Definition Id</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssocProp.Val">
      <summary>Property Value</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssocProp.ValTyp">
      <summary>Property Data Type</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAssocPropArray.Items">
      <summary>An array of ItemAssocProp</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAttmt.AttmtArray">
      <summary>Attachments to the Item.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemAttmt.ItemId">
      <summary>The Item ID.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemBOM.BOMCompArray">
      <summary>Array of BOM components in this BOM</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemBOM.ItemAssocArray">
      <summary>The associations between the Items.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemBOM.ItemRevArray">
      <summary>An array of Items.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemBOM.OccurArray">
      <summary>An array of occurrences.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemBOM.RefDesAvailable">
      <summary>If true, reference designators are available.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemBOMAndDiff.BOMOccDiffArray">
      <summary>An array of differences between BOM Occurrences.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemBOMAndDiff.DiffArray">
      <summary>An array of differences between BOM links.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemBOMAndDiff.ItemBOM">
      <summary>BOM data for the Item.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemBOMCompareDiff.ItemAssocID">
      <summary>The item association ID</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemBOMCompareDiff.ItemAssocMasterID">
      <summary>the Item association master ID</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemBOMCompareDiff.PropDefId">
      <summary>The ID of the property containing the difference. Use GetAllPropertyDefinitions for a list of properties. See table below for a list of "reserved" properties
only used for BOM compare.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemBOMCompareDiff.Val1">
      <summary>The property value from the first BOM.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemBOMCompareDiff.Val2">
      <summary>The property value from the second BOM.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemBOMFileAssoc.AssocType">
      <summary>The association type.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemBOMFileAssoc.FileId">
      <summary>The ID of the associated File.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemBOMOcc.Id">
      <summary>A unique identifier for this occurrence within the Item BOM.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemBOMOcc.ParOccId">
      <summary>The immediate parent. -1 means no parent occurrence.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemBOMOcc.Path">
      <summary>The path to the occurrence in the BOM. The format is the list of Item Master IDs separated by the '/' delimiter.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemBOMOcc.Pos">
      <summary>The position on the BOM.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemBOMOcc.RootItemId">
      <summary>The top most Item of the BOM.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemBOMOcc.Val">
      <summary>The value of the occurrence. Also known as the "Detail Id."</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemCat.CatId">
      <summary>Category ID</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemCat.CatName">
      <summary>Category name</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemConflict.IsRevGE">
      <summary>If true the Item Revision is greater than or equal to the existing Item Revision.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemDelStatus.DelCount">
      <summary>The number of versions deleted.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemDelStatus.ItemMasterId">
      <summary>The Master ID of the purged Item.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemFileAssoc.CldFileId">
      <summary>The ID of the child File.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemFileAssoc.CldFileVerNum">
      <summary>The File version number.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemFileAssoc.Cloaked">
      <summary>If true, the file is cloaked from the current user, and FileName will be empty. If false, the user has permission to view the file, and FileName will be filled
in.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemFileAssoc.FileName">
      <summary>The name of the File.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemFileAssoc.ParItemId">
      <summary>The ID of the parent Item.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemFileAssoc.Typ">
      <summary>The type of link.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemNum.ItemMasterId">
      <summary>The Master ID for the Item.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemNum.ItemNum1">
      <summary>The Item number.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemRollbackLifeCycleStateCommandEventArgs.AddRestriction(Autodesk.Connectivity.Extensibility.Framework.ExtensionRestriction)">
      <summary>Adds a restriction, which blocks the command. This function should only be called during the GetRestrictions event.</summary>
      <param>The reason that the command cannot run.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemRollbackLifeCycleStateCommandEventArgs.Guid">
      <summary>The unique identifier for the event.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemRollbackLifeCycleStateCommandEventArgs.ReturnValue">
      <summary>The updated Item.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemRollbackLifeCycleStateCommandEventArgs.Status">
      <summary>The status of the command. Used in the Post event to see of the command was successful.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.ItemRollbackLifeCycleStateCommandEventArgs.ItemIterationId">
      <summary>The Item being rolled back.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemsAndFiles.FileAssocArray">
      <summary>The links to Vault Files for each item in ItemRevArray.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemsAndFiles.ItemAssocArray">
      <summary>Child dependencies for each Item in ItemRevArray.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemsAndFiles.ItemRevArray">
      <summary>An array of Item Revisions.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemsAndFiles.RootArray">
      <summary>An array of Item IDs. One entry for each independent BOM root node.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemsAndFiles.StatusArray">
      <summary>The status of each item in ItemRevArray.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.ActivateNumberingSchemes(System.Int64[])">
      <summary>Activates a set of Item numbering schemes.</summary>
      <returns>An array of updated number schemes.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.AddComponentsToPromote(System.Int64[],Autodesk.Connectivity.WebServices.ItemAssignAll,System.Boolean)">
      <summary>Assign item for specific BOM components</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.AddFilesToPromote(System.Int64[],Autodesk.Connectivity.WebServices.ItemAssignAll,System.Boolean)">
      <summary>Assign item for specific files</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.AddItemNumbers(System.Int64[],System.Int64[],Autodesk.Connectivity.WebServices.StringArray[],Autodesk.Connectivity.WebServices.ProductRestric[]@)">
      <summary>Creates new item numbers.</summary>
      <returns>An array of records of the created item numbers.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.AddItemRevision(System.Int64)">
      <summary>Creates a new Item.</summary>
      <returns>The newly created Item. The Item is still in an editable state. Call a commit function, such as UpdateAndCommitItems to commit the changes.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.AddNumberingScheme(Autodesk.Connectivity.WebServices.NumSchm)">
      <summary>Creates a new Numbering Scheme.</summary>
      <returns>The newly created NumSchm. Should be identical to the passed in object except for the Id.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.AddUnitOfMeasure(System.String,System.String,System.Double,System.Int64)">
      <summary>Creates a Unit of Measure object.</summary>
      <returns>The new Unit of Measure object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.AssignComponentToItem(System.Int64,System.Int64)">
      <summary>Assign a component to an item revision</summary>
      <returns>Either an uncommitted or committed item depending on the type of link created</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.AssignFileToItem(System.Int64,System.Int64)">
      <summary>Assign a file to an item revision</summary>
      <returns>Either an uncommitted or committed item depending on the type of link created</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.CommitItemNumbers(System.Int64[],System.String[])">
      <summary>Commits the specified Item Number changes to Items</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.CompareItemBOMs(System.Int64,System.Int64,System.DateTime,System.DateTime,Autodesk.Connectivity.WebServices.BOMTyp,Autodesk.Connectivity.WebServices.BOMTyp,Autodesk.Connectivity.WebServices.BOMViewEditOptions,System.Int64[])">
      <summary>Compare the BOMs of two Versions of an Item.</summary>
      <returns>The results of the compare.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.DeactivateNumberingSchemes(System.Int64[])">
      <summary>Deactivates a set of Item numbering schemes.</summary>
      <returns>The updated Numbering Scheme objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.DeleteItems(System.Int64[])">
      <summary>Deletes a set of items. All associated revisions and versions are also deleted.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.DeleteItemsUnconditional(System.Int64[])">
      <summary>Deletes a set of Items regardless of server restrictions.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.DeleteItemVersionsByMasterItemId(System.Int64)">
      <summary>Deletes/purges the item iteration history of the specified item</summary>
      <returns>the status of the item delete</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.DeleteItemVersionsByMasterItemIds(System.Int64[])">
      <summary>Deletes/Purges the item iteration history of the specified items</summary>
      <returns>The state of the delete</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.DeleteNumberingScheme(System.Int64)">
      <summary>Deletes an Item numbering scheme.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.DeleteUncommittedItems(System.Boolean)">
      <summary>Deletes all the uncommitted items for this user.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.DeleteUnitOfMeasure(System.Int64)">
      <summary>Deletes a unit of measure.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.DeleteUnusedItemNumbers(System.Int64[])">
      <summary>Deletes unused item numbers for a set of item master IDs.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.EditItems(System.Int64[])">
      <summary>Creates new, editable Items used to modify the existing revision.</summary>
      <returns>An array of editable Items. Call a commit function, such as UpdateAndCommitItems to finalize the changes.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.FindItemRevisionsBySearchConditions(Autodesk.Connectivity.WebServices.SrchCond[],Autodesk.Connectivity.WebServices.SrchSort[],System.Boolean,System.String@,Autodesk.Connectivity.WebServices.SrchStatus@)">
      <summary>Gets a set of Item revisions based on a property search.</summary>
      <returns>An array of all matching Item revisions or null if there were no matches.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.FindLatestItemRevisionIdsByComponentIds(System.Int64[])">
      <summary>Finds the latest revision ids of items based on component ids</summary>
      <returns>
        <para>The latest item revision IDs for the specified component IDs.</para>
      </returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetAllBaseUnitsOfMeasure">
      <summary>Returns all Units of Measure which are not derived from another Unit of Measure.</summary>
      <returns>A list of all base Units of Measure.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetAllUnitsOfMeasure">
      <summary>Gets all Units of Measure.</summary>
      <returns>An array of all Units of Measure.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetAllWatermarks">
      <summary>Gets all Watermark objects.</summary>
      <returns>An array of all Watermark objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetAttachmentsByItemIds(System.Int64[])">
      <summary>Gets the File attachments for a set of Items.</summary>
      <returns>An array of File attachments for the input Items or null if there are no attachments. The return array may not be the same size or correspond to the input
array.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetComponentProperties(System.Int64,System.Int64)">
      <summary>Retrieves the list of writeback properties and their associated values for the specified item and Content Source</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetContentSourcePropertyDefinitionsByFileMasterId(System.Int64,System.Boolean,System.Boolean)">
      <summary>Gets the property definitions from the file's BOM data.</summary>
      <returns>The property definitions from the file BOM.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetEnablementConfiguration">
      <summary>Gets the setting that controls if Items and Change Orders are enabled.</summary>
      <returns>The setting that controls if Items and Change Orders are enabled. This is a bitfield which means it may contain multiple values joined together.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetEnableWatermarking">
      <summary>Tells if the watermark feature has been enabled.</summary>
      <returns>If true, watermarks are enabled. Otherwise they are disabled.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetItemAssignmentBehavior">
      <summary>Retrieve the default item assignment behavior from the server</summary>
      <returns>The current default Item Assignment behavior (Admin setting for Items)</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetItemAutogroupBehavior">
      <summary>Gets the value of the administrator option for controlling BOM autogroup behavior</summary>
      <returns>The current autogrouping behavior</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetItemBOMAssociationProperties(System.Int64[],System.Int64[])">
      <summary>Gets values for properties on BOM associations.</summary>
      <returns>The property values in the association between the parent and child Items.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetItemBOMAssociationsByItemIds(System.Int64[],System.Boolean)">
      <summary>Gets child Item links.</summary>
      <returns>The set of Item associations.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetItemBOMByItemIdAndDate(System.Int64,System.DateTime,Autodesk.Connectivity.WebServices.BOMTyp,Autodesk.Connectivity.WebServices.BOMViewEditOptions)">
      <summary>Gets the Item BOM for a given Item by its effective date.</summary>
      <returns>The Item BOM that was effective for the given date. The only exception is that Item passed in. That version will show up in the BOM regardless of effectivity.
All other Items in the BOM are based on effectivity.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetItemBOMFileAssociationsByDate(System.Int64,Autodesk.Connectivity.WebServices.BOMTyp,System.DateTime,System.Boolean)">
      <summary>Gets Files associated with an Item BOM.</summary>
      <returns>An Item BOM with links to Files.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetItemByItemNumberAndRevisionNumber(System.String,System.String)">
      <summary>Gets an Item object based on the item number and revision number.</summary>
      <returns>The matching Item object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetItemDeleteRestrictionsByIds(System.Int64[])">
      <summary>Gets the restrictions for deleting a set of Items.</summary>
      <returns>The delete restrictions, if any.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetItemFileAssociationsByItemIds(System.Int64[],Autodesk.Connectivity.WebServices.ItemFileLnkTypOpt)">
      <summary>Gets all File associations on a given Item. Does not include attachments.</summary>
      <returns>An array of File-Item associations on the input Items.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetItemHistoryByItemMasterId(System.Int64,Autodesk.Connectivity.WebServices.ItemHistoryTyp)">
      <summary>Gets the item history for the specific item master based on history type</summary>
      <returns>array of historical items</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetItemsByAttachmentFileId(System.Int64)">
      <summary>Gets the items that a file is attached to. Only finds items if the file is attached to the tip revision.</summary>
      <returns>An array of matching items.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetItemsByFileId(System.Int64)">
      <summary>Gets all of the Items assigned to a File.</summary>
      <returns>An array of Items assigned to the File or null if there are no assigned Items.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetItemsByFileIdAndLinkTypeOptions(System.Int64,Autodesk.Connectivity.WebServices.ItemFileLnkTypOpt)">
      <summary>Gets a set of Items that are associated in specific ways to a given File.</summary>
      <returns>An array of Items that are associated to the File based on the input types.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetItemsByIds(System.Int64[])">
      <summary>Gets a set of Items based on their IDs</summary>
      <returns>An array of Item objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetItemsByRevisionIds(System.Int64[],System.Boolean)">
      <summary>Gets a set of Item objects based on a set of Item revision IDs.</summary>
      <returns>The corresponding Item objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetLatestItemByItemMasterId(System.Int64)">
      <summary>Gets the latest version of an Item.</summary>
      <returns>The latest Item Revision.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetLatestItemByItemNumber(System.String)">
      <summary>Gets the latest Revision for an Item.</summary>
      <returns>The latest Revision for the Item.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetLatestItemsByItemMasterIds(System.Int64[])">
      <summary>Gets the latest versions for a set of Item Master IDs.</summary>
      <returns>An array of latest Items.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetLifecycleRollbackTargetItem(System.Int64)">
      <summary>Gets the target item for rolling back an item's lifecycle state</summary>
      <returns>the target item iteration to rollback to</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetNumberingSchemesByType(Autodesk.Connectivity.WebServices.NumSchmType)">
      <summary>Gets the Numbering Schemes for a given type.</summary>
      <returns>An array of matching objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetPrimaryComponentsByItemIds(System.Int64[])">
      <summary>
        <para>Gets the BOMComponents that form the primary link between the specified items and the components’ files.</para>
      </summary>
      <returns>An array of BOM components that are the primary links on the specified items</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetPromoteComponentOrder(System.DateTime@)">
      <summary>Gets the order that components should be promoted.</summary>
      <returns>A GetPromoteComponentOrderResults object containing information about the promote order.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetPromoteComponentsResults(System.DateTime)">
      <summary>Gets the resulting Item objects generated from PromoteComponent calls.</summary>
      <returns>Item an file data.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetPropertyConstraintFailuresByItemMasterIds(System.Int64[])">
      <summary>Gets all properties that violate a constraint for a given Item.</summary>
      <returns>A list of failures or null if there are no failures.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetReferenceDesignatorProperties(System.Int64[],System.Int64[])">
      <summary>Gets the properties for a set of Reference Designators</summary>
      <returns>A list of all matching properties. The maximum size of the return array is equal to the size of the itemRefDesIds times the propertyListIDs. The return array
can be smaller if the Reference Designators do not have some of the properties specified. Null will be returned if none of the Reference Designators have any
of the properties specified.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetRestrictAssignDesignFiles">
      <summary>Gets a boolean value indicating if assigning design files to Items is restricted.</summary>
      <returns>If true, design files are restricted from being assigned to an Item. If false, there is no restriction.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetRestrictLifeCycleStateChangeToChangeOrder">
      <summary>Gets the value indicating if all life cycle changes must occur within the context of a Change Order.</summary>
      <returns>Whether lifecycle state changes are restricted to change orders</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetSubComponentLinkTypeForAssignItem">
      <summary>Gets the value for the vault setting controlling the type of link to be created for subcomponents during assign/update item</summary>
      <returns>The link type setting value</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetUnitOfMeasureById(System.Int64)">
      <summary>Gets a Unit of Measure object.</summary>
      <returns>The Unit of Measure object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetUnitOfMeasureFamilyByUnitOfMeasureId(System.Int64)">
      <summary>Gets all Units of Measure that share a common base.</summary>
      <returns>A list of Units of Measure that share the same base as the inputed Unit of Measure.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.GetWatermarkByItemIdAndFileId(System.Int64,System.Int64)">
      <summary>Gets the watermark for an item and file.</summary>
      <returns>The watermark.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.ItemRollbackLifeCycleState(System.Int64)">
      <summary>Executes an item rollback to the specified target item iteration</summary>
      <returns>the new tip item iteration</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.PromoteComponentLinks(System.Int64[])">
      <summary>Creates secondary links without creating a new item iteration for BOM components</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.PromoteComponents(System.DateTime,System.Int64[])">
      <summary>Creates items on a component-by-component basis.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.ReassignComponentsToDifferentItems(System.Int64[],System.Int64[])">
      <summary>Assigns a Component to a different Item.</summary>
      <returns>An array of new destination Items. The Items are editable. Call a commit function, such as UpdateAndCommitItems to finalize the changes.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.SetDefaultNumberingScheme(System.Int64)">
      <summary>Sets the default Item numbering scheme for the Vault.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.SetEnablementConfiguration(Autodesk.Connectivity.WebServices.EnablementConfig)">
      <summary>Sets the setting that controls if Items and Change Orders are enabled</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.SetEnableWatermarking(System.Boolean)">
      <summary>Turns the watermark feature on or off.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.SetItemAssignmentBehavior(Autodesk.Connectivity.WebServices.ItemAssignBhv)">
      <summary>Sets the server's default item assignment behavior (Admin setting for items)</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.SetItemAutogroupBehavior(Autodesk.Connectivity.WebServices.ItemAutogroupBhv)">
      <summary>Sets the value of the administrator option for BOM autogrouping behavior</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.SetRestrictAssignDesignFiles(System.Boolean)">
      <summary>Gets a boolean value indicating if assigning design files to Items is restricted.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.SetRestrictLifeCycleStateChangeToChangeOrder(Autodesk.Connectivity.WebServices.RestrictLifecycleChange)">
      <summary>Sets a value indicating if all life cycle changes must occur within the context of a Change Order.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.SetSubComponentLinkTypeForAssignItem(Autodesk.Connectivity.WebServices.ItemSubComponentLinkType)">
      <summary>Sets the vlaue for the vault setting controlling which type of links are created for subcomponents during an assign/update item</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.UndoEditItems(System.Int64[])">
      <summary>Removes changes to an uncommitted Item.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.UpdateAndCommitItems(Autodesk.Connectivity.WebServices.Item[])">
      <summary>Commits the changes to a set of Items.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.UpdateAttachments(System.Int64,Autodesk.Connectivity.WebServices.Attmt[])">
      <summary>Updates the attachments associated with the specified Item Revision</summary>
      <returns>The item with the update attachments applied</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.UpdateItemBOMAssociationProperties(System.Int64,System.Int64[],Autodesk.Connectivity.WebServices.ItemAssocPropArray[])">
      <summary>Update the property values on an BOM association.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.UpdateItemBOMAssociations(System.Int64,Autodesk.Connectivity.WebServices.ItemAssocParam[],Autodesk.Connectivity.WebServices.BOMViewEditOptions)">
      <summary>Updates the associations for an Item's BOM.</summary>
      <returns>The updated BOM.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.UpdateItemCategories(System.Int64[],System.Int64[],System.String)">
      <summary>update the category assigned to items</summary>
      <returns>updated items</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.UpdateItemFileAssociations(System.Int64,System.Int64,System.Boolean,System.Int64[],System.Int64[],System.Int64[],System.Int64[])">
      <summary>Updates which files are associated with the specified Item Revision</summary>
      <returns>An editable Item containing the changes. Call a commit function, such as UpdateAndCommitItems, to finalize the changes.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.UpdateItemLifeCycleDefinitions(System.Int64[],System.Int64[],System.Int64[],System.String)">
      <summary>updates the item life cycle definitions assigned to items</summary>
      <returns>updated items</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.UpdateItemLifeCycleStates(System.Int64[],System.Int64[],System.String)">
      <summary>updates the life cycle state of an item</summary>
      <returns>updated items</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.UpdateItemProperties(System.Int64[],Autodesk.Connectivity.WebServices.PropInstParamArray[])">
      <summary>Updates the property values for a set of Items and commits the changes.</summary>
      <returns>Item objects after applying UpdateItemProperties</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.UpdateItemPropertyDefinitions(System.Int64[],System.Int64[],System.Int64[],System.String)">
      <summary>Updates the property definitions associated with a set of Items.</summary>
      <returns>The update Item objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.UpdateItemRevisionNumbers(System.Int64[],System.String[],System.String)">
      <summary>Updates the revision numbers for a set of items.</summary>
      <returns>The updated items.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.UpdateNumberingScheme(Autodesk.Connectivity.WebServices.NumSchm)">
      <summary>Updates an existing Item numbering scheme.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.UpdatePromoteComponents(System.Int64[],Autodesk.Connectivity.WebServices.ItemAssignAll,System.Boolean)">
      <summary>Begins an "update items from files" operation on a component-by-component basis.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.UpdateRevisionDefinitionAndNumbers(System.Int64[],System.Int64[],System.String[],System.String)">
      <summary>Updates the revision number and switches to a new revision definition for a set of items.</summary>
      <returns>The updated items.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.UpdateUnitOfMeasure(Autodesk.Connectivity.WebServices.UnitOfMeasure)">
      <summary>Updates the data for a Unit of Measure.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ItemService.UpdateWatermarkDefinitions(Autodesk.Connectivity.WebServices.Wmark[])">
      <summary>Updates a set of Watermarks.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemService.AddItemEvents">
      <summary>A collection of Web Service Command Events for add Item operations.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemService.AllowInvokeEvents">
      <summary>Tells if invoke events should fire. This value is true by default.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemService.CommitItemEvents">
      <summary>A collection of Web Service Command Events for commit Item operations.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemService.DeleteItemEvents">
      <summary>A collection of Web Service Command Events for delete Item operations.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemService.EditItemEvents">
      <summary>A collection of Web Service Command Events for edit Item operations.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemService.ItemRollbackLifeCycleStatesEvents">
      <summary>A collection of Web Service Command Events for item rollback operations.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemService.PromoteItemEvents">
      <summary>A collection of Web Service Command Events for promote Item operations.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemService.SecurityHeader">
      <summary>The security header. (from IWebService)</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemService.UpdateItemLifecycleStateEvents">
      <summary>A collection of Web Service Command Events for change Item lifecycle state operations.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ItemService.WebServiceManager">
      <summary>A reference to the WebServiceManager that created this object. Value may be null.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.ItemService.PostInvokeEvents">
      <summary>Subscribe to this event to get a notification after every web service call.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.ItemService.PreInvokeEvents">
      <summary>Subscribe to this event to get a notification before every web service call.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Job.ClientName">
      <summary>The computer name of the client that the job is reserved to.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Job.CreateDate">
      <summary>The date the job was created.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Job.CreateUserId">
      <summary>The ID of the user who created the job.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Job.CreateUserName">
      <summary>The name of the user who created the job.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Job.Descr">
      <summary>A description of the job.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Job.Id">
      <summary>A unique identifier for the job.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Job.IsOnSite">
      <summary>In a multi-site environment, this property tells if the file is on the local site.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Job.ParamArray">
      <summary>An array of parameters which provide meta-data about the job.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Job.Priority">
      <summary>The priority of the job. A lower number means a higher priority. 1 is the lowest possible number.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Job.ReserveDate">
      <summary>The date that the job was reserved. This value is only valid if ReserveDateSpecified is true.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Job.ReserveDateSpecified">
      <summary>If true, then the ReserveDate field has a valid value. If false, there is no reserve date.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Job.StatusCode">
      <summary>The status of the job.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Job.StatusMsg">
      <summary>A message regarding the status. Only used for failures.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Job.Typ">
      <summary>The job type.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Job.VaultId">
      <summary>The ID of the Vault that the job applies to.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.JobParam.Name">
      <summary>The parameter name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.JobParam.Val">
      <summary>The parameter value.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.JobService.AddJob(System.String,System.String,Autodesk.Connectivity.WebServices.JobParam[],System.Int32)">
      <summary>Adds a new job to the queue.</summary>
      <returns>The new job object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.JobService.DeleteJobById(System.Int64)">
      <summary>Deletes a job from the queue.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.JobService.EnableChangeObservation(System.String,System.String,System.Boolean)">
      <summary>Reserved for future use.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.JobService.GetJobQueueEnabled">
      <summary>Tells if the job queue is enabled.</summary>
      <returns>If true, the job queue is enabled. If false, the job queue is not enabled.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.JobService.GetJobsByDate(System.Int32,System.DateTime)">
      <summary>Get all jobs from the queue queued on or after the specified start date.</summary>
      <returns>An array of related jobs.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.JobService.ReserveNextJob(System.String[],System.String)">
      <summary>Reserve the next job in the queue</summary>
      <returns>A job that is reserved to the client or null of there are no jobs in the queue of a matching type.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.JobService.SetJobQueueEnabled(System.Boolean)">
      <summary>Enables or disables the job queue.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.JobService.UnReserveJobById(System.Int64)">
      <summary>Removes a reservation on a job.</summary>
      <returns>The updated job object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.JobService.UnReserveJobsByClientName(System.String)">
      <summary>Un-reserve all jobs for a given client.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.JobService.UpdateJobFailure(System.Int64,System.String)">
      <summary>Inform the job queue that the client was unable to complete the job.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.JobService.UpdateJobSuccess(System.Int64)">
      <summary>Inform the job queue that the job was successfully completed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.JobService.AllowInvokeEvents">
      <summary>Tells if invoke events should fire. This value is true by default.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.JobService.SecurityHeader">
      <summary>The security header. (from IWebService)</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.JobService.WebServiceManager">
      <summary>A reference to the WebServiceManager that created this object. Value may be null.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.JobService.PostInvokeEvents">
      <summary>Subscribe to this event to get a notification after every web service call.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.JobService.PreInvokeEvents">
      <summary>Subscribe to this event to get a notification before every web service call.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.KnowledgeLibraryHeader.KnowledgeLibrary">
      <summary>Internal use only.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.KnowledgeVault.CreateDate">
      <summary>The date that the Knowledge Vault was created.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.KnowledgeVault.CreateUserId">
      <summary>The ID of the user that created the Knowledge Vault.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.KnowledgeVault.Id">
      <summary>A unique identifier for the Knowledge Vault.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.KnowledgeVault.Name">
      <summary>The name of the Knowledge Vault. This is also the name of the database.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.KnowledgeVault.NtfsRoot">
      <summary>The network path to the file store. A local path is also allowed.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.KnowledgeVaultService.AddKnowledgeVault(System.String)">
      <summary>Creates a new Knowledge Vault.</summary>
      <returns>An object representing the newly created Knowledge Vault.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.KnowledgeVaultService.CreateKnowledgeVaultMaster(System.String,System.String)">
      <summary>Creates the Knowledge Vault Master database.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.KnowledgeVaultService.DoesKnowledgeVaultMasterExist(System.String,System.String)">
      <summary>Gets whether the knowledge vault master exists.</summary>
      <returns>Boolean value indicating true or false. If false, the master does not exist.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.KnowledgeVaultService.GetExecutionTimeout">
      <summary>Gets the timeout value for the HTTP connection.</summary>
      <returns>The HTTP timeout value, in seconds.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.KnowledgeVaultService.GetPersistentIds(System.String,System.Int64[],Autodesk.Connectivity.WebServices.EntPersistOpt)">
      <summary>Gets persistant IDs for a set of entities.</summary>
      <returns>A set of persistant IDs.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.KnowledgeVaultService.GetSearchPagesize">
      <summary>Gets the maximum number of results for a single search call.</summary>
      <returns>The number maximum number of results for a single search call.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.KnowledgeVaultService.GetSiteCompatibilityWithKnowledgeMaster">
      <summary>Tells if the Knowledge Master of the current site is compatible with the database.</summary>
      <returns>Tells if the site is compatible or not. If it is not compatible, extra information is provided on the conflicts.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.KnowledgeVaultService.GetSiteCompatibilityWithKnowledgeVault(System.String)">
      <summary>Tells if the the current site is compatible with a specific Vault database.</summary>
      <returns>Tells if the site is compatible or not. If it is not compatible, extra information is provided on the conflicts.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.KnowledgeVaultService.GetSystemOption(System.String)">
      <summary>Gets an options setting which applies to the entire system.</summary>
      <returns>The value of the option.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.KnowledgeVaultService.GetVaultOption(System.String)">
      <summary>Gets an options setting which applies to a Vault.</summary>
      <returns>The value of the option.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.KnowledgeVaultService.ResolvePersistentIds(System.String[])">
      <summary>Returns entity objects for a give set of persistant IDs.</summary>
      <returns>A set of entities referenced by the input IDs.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.KnowledgeVaultService.SetSystemOption(System.String,System.String)">
      <summary>Sets a system wide options value.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.KnowledgeVaultService.SetVaultOption(System.String,System.String)">
      <summary>Sets a Vault wide options value.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.KnowledgeVaultService.UpdateDatabaseLogins(System.String,System.String)">
      <summary>Resets the ADMS database logins.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.KnowledgeVaultService.AllowInvokeEvents">
      <summary>Tells if invoke events should fire. This value is true by default.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.KnowledgeVaultService.SecurityHeader">
      <summary>The security header. (from IWebService)</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.KnowledgeVaultService.WebServiceManager">
      <summary>A reference to the WebServiceManager that created this object. Value may be null.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.KnowledgeVaultService.PostInvokeEvents">
      <summary>Subscribe to this event to get a notification after every web service call.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.KnowledgeVaultService.PreInvokeEvents">
      <summary>Subscribe to this event to get a notification before every web service call.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Label.Comm">
      <summary>A comment associated with the label.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Label.CreateDate">
      <summary>The date and time the label was created.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Label.CreateUserId">
      <summary>The Id of the user who created the label.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Label.CreateUserName">
      <summary>The name of the user who created the label.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Label.Id">
      <summary>The label Id.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Label.Name">
      <summary>The name of the label.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Label.NumFiles">
      <summary>The number of files included in the label.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycDef.Descr">
      <summary>A description of the Life Cycle state.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycDef.DispName">
      <summary>The display name of the Life Cycle state.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycDef.Id">
      <summary>The ID for the object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycDef.Name">
      <summary>The constant name. This value cannot be edited.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycDef.StateArray">
      <summary>An array of Life Cycle States.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycDef.SysName">
      <summary>The system name of the Life Cycle state. This name cannot be changed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycDef.TransArray">
      <summary>An array of Life Cycle State Transitions</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycState.CommArray">
      <summary>An array of suggested comments to use as file comments when changing a file life cycle state.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycState.Descr">
      <summary>The description of the state.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycState.DispName">
      <summary>The display name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycState.DispOrder">
      <summary>Display order of the life cycle state</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycState.Id">
      <summary>The unique identifier for this object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycState.IsDflt">
      <summary>If true, this is the default, or starting, state.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycState.ItemFileSecMode">
      <summary>Item File Link security of the life cycle state</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycState.LfCycDefId">
      <summary>The Life Cycle Definition that this state belongs to.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycState.Name">
      <summary>The system name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycState.ObsoleteState">
      <summary>Is life cycle state a designated obsolete state</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycState.ReleasedState">
      <summary>If true, this is a released state.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycState.RestrictPurgeOption">
      <summary>Tells which versions in this state are restricted from deletion during a Purge operation.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycState.StateBasedSec">
      <summary>If true, state based security is enabled.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycTrans.Bump">
      <summary>If true, the object revision should be bumped during the transition.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycTrans.CldObsState">
      <summary>Transition action to verify if a child is not in an obsolete state</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycTrans.CldState">
      <summary>A bitfield indicating how lifecycle states should be enforced in child entities.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycTrans.CtntState">
      <summary>A bitfield indicating how the state should be enforced in linked Entities.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycTrans.FromId">
      <summary>The starting state.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycTrans.Id">
      <summary>A unique identifier for this object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycTrans.ItemFileLnkState">
      <summary>Transition action to verify if Item File Links are in a consumable state</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycTrans.ItemFileLnkUptodate">
      <summary>Transition action to verify if Item File Links are up-to-date</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycTrans.RuleSet">
      <summary>The rules for this transition. If the rule set requirements are not met, the file cannot make the transition.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycTrans.SyncPropOption">
      <summary>This option tells if the 'state' property needs to be synchronized with the file during a state change.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycTrans.ToId">
      <summary>The destination state.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LfCycTrans.TransBasedSec">
      <summary>If true, security is enabled for this transition.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.License.Bhv">
      <summary>The behavior.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.License.ExpireDate">
      <summary>The expiration date. This value is only valid if 'ExpireDateSpecified' is true.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.License.ExpireDateSpecified">
      <summary>If true, the 'ExpireDate' property is valid.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.License.FeatureVer">
      <summary>Feature information from the license.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.License.Host">
      <summary>The hostname where ADMS is running.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.License.Id">
      <summary>The license feature name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.License.Name">
      <summary>The product name for the application being licensed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.License.ProdKey">
      <summary>The product key.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.License.SerialNum">
      <summary>The serial number.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.License.Server">
      <summary>a comma separated list of License Servers from which this license is checked-out.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.License.Typ">
      <summary>The type.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.License.UsageTyp">
      <summary>The intended usage.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.LifeCycleService.AddLifeCycleDefinition(System.String,System.String,System.String)">
      <summary>
        <para>Adds a new Life Cycle Definition.</para>
      </summary>
      <returns>
        <para>The new Life Cycle Definition object.</para>
      </returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.LifeCycleService.AddLifeCycleState(System.Int64,System.String,System.String,System.String,System.Boolean,System.Boolean,Autodesk.Connectivity.WebServices.ACE[],System.String[],System.Boolean,System.Boolean,System.Int32,Autodesk.Connectivity.WebServices.RestrictPurgeOption,Autodesk.Connectivity.WebServices.ItemToFileSecurityModeEnum,Autodesk.Connectivity.WebServices.ACE[])">
      <summary>Adds a new lifecycle state.</summary>
      <returns>
        <para>The object for this Life Cycle State.</para>
      </returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.LifeCycleService.AddLifeCycleStateTransition(System.Int64,System.Int64,Autodesk.Connectivity.WebServices.EnforceChildStateEnum,Autodesk.Connectivity.WebServices.EnforceContentStateEnum,Autodesk.Connectivity.WebServices.BumpRevisionEnum,Autodesk.Connectivity.WebServices.JobSyncPropEnum,Autodesk.Connectivity.WebServices.FileLinkTypeEnum,Autodesk.Connectivity.WebServices.FileLinkTypeEnum,System.Boolean,System.Boolean,System.Int64[],System.Int64[],Autodesk.Connectivity.WebServices.PropDefCond[])">
      <summary>
        <para>Adds a transition from one Life Cycle State to another.</para>
      </summary>
      <returns>
        <para>The new Life Cycle Transition object.</para>
      </returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.LifeCycleService.DeleteLifeCycleDefinition(System.Int64)">
      <summary>
        <para>Deletes a Life Cycle Definition along with all its states and transitions.</para>
      </summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.LifeCycleService.DeleteLifeCycleStates(System.Int64[])">
      <summary>
        <para>Deletes a set of Life Cycle States.</para>
      </summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.LifeCycleService.GetAllLifeCycleDefinitions">
      <summary>
        <para>Gets all the Life Cycle Definitions in this vault.</para>
      </summary>
      <returns>
        <para>All the Life Cycle Definitions in this vault.</para>
      </returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.LifeCycleService.GetAllowedLifeCycleStateTransitionIds">
      <summary>
        <para>Get the Life Cycle Transitions that the current user is allowed to perform.</para>
      </summary>
      <returns>
        <para>An array of Life Cycle Transition IDs.</para>
      </returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.LifeCycleService.GetJobTypesByLifeCycleStateTransitionIds(System.Int64[])">
      <summary>
        <para>Gets the custom jobs that are queued for a set of transitions.</para>
      </summary>
      <returns>
        <para>An array of arrays of strings telling which jobs are queued during the transitions. The top level array matches the size of the input array.</para>
      </returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.LifeCycleService.GetLatestRevisionLifeCycleStatesByDefinitionId(System.Int64)">
      <summary>
        <para>Gets those lifecycle states being used by any latest version of any revision.</para>
      </summary>
      <returns>
        <para>An array of states currently in use as a latest state in a revision.</para>
      </returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.LifeCycleService.GetLifeCycleDefinitionDeleteRestrictionsById(System.Int64)">
      <summary>
        <para>Gets the restrictions on deleting a life cycle definition.</para>
      </summary>
      <returns>
        <para>A list of delete restrictions, or null if there are no restrictions.</para>
      </returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.LifeCycleService.GetLifeCycleDefinitionsByIds(System.Int64[])">
      <summary>
        <para>Gets a set of Life Cycle Definition objects based on their IDs.</para>
      </summary>
      <returns>
        <para>The resulting Life Cycle Definition objects.</para>
      </returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.LifeCycleService.GetLifeCycleStatesByIds(System.Int64[])">
      <summary>
        <para>Gets a set of Life Cycle State objects based on their IDs.</para>
      </summary>
      <returns>
        <para>The resulting Life Cycle State objects.</para>
      </returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.LifeCycleService.GetLifeCycleStateTransitionsByIds(System.Int64[])">
      <summary>
        <para>Gets a set of Life Cycle Transitions by their IDs.</para>
      </summary>
      <returns>
        <para>The resulting Life Cycle Transition objects.</para>
      </returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.LifeCycleService.GetStateACLByLifeCycleStateId(System.Int64)">
      <summary>
        <para>Gets the security information for a life cycle state.</para>
      </summary>
      <returns>
        <para>The security information for the state.</para>
      </returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.LifeCycleService.GetTransitionACEsByTransitionId(System.Int64)">
      <summary>
        <para>Gets the security information for a Life Cycle Transition.</para>
      </summary>
      <returns>
        <para>The security information for the transition.</para>
      </returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.LifeCycleService.UpdateLifeCycleDefinition(System.Int64,System.String,System.String)">
      <summary>
        <para>Updates a life cycle definition.</para>
      </summary>
      <returns>
        <para>The updated life cycle definition object.</para>
      </returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.LifeCycleService.UpdateLifeCycleState(System.Int64,System.String,System.String,System.Boolean,System.Boolean,System.String[],Autodesk.Connectivity.WebServices.ACE[],System.Boolean,System.Boolean,System.Int32,Autodesk.Connectivity.WebServices.RestrictPurgeOption,Autodesk.Connectivity.WebServices.ItemToFileSecurityModeEnum,Autodesk.Connectivity.WebServices.ACE[])">
      <summary>
        <para>Updates a Life Cycle State.</para>
      </summary>
      <returns>
        <para>The updated life cycle state object.</para>
      </returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.LifeCycleService.UpdateLifeCycleStateTransition(System.Int64,Autodesk.Connectivity.WebServices.BumpRevisionEnum,Autodesk.Connectivity.WebServices.JobSyncPropEnum,Autodesk.Connectivity.WebServices.EnforceChildStateEnum,Autodesk.Connectivity.WebServices.EnforceContentStateEnum,Autodesk.Connectivity.WebServices.FileLinkTypeEnum,Autodesk.Connectivity.WebServices.FileLinkTypeEnum,System.Boolean,System.Boolean,System.Int64[],System.Int64[],Autodesk.Connectivity.WebServices.PropDefCond[])">
      <summary>
        <para>Updates a Life Cycle Transition.</para>
      </summary>
      <returns>
        <para>The new Life Cycle Transition object.</para>
      </returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.LifeCycleService.UpdateLifeCycleStateTransitionJobTypes(System.Int64,System.String[])">
      <summary>
        <para>Updates the custom jobs queued during a lifecycle state transition.</para>
      </summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LifeCycleService.AllowInvokeEvents">
      <summary>Tells if invoke events should fire. This value is true by default.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LifeCycleService.SecurityHeader">
      <summary>The security header. (from IWebService)</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LifeCycleService.WebServiceManager">
      <summary>A reference to the WebServiceManager that created this object. Value may be null.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.LifeCycleService.PostInvokeEvents">
      <summary>Subscribe to this event to get a notification after every web service call.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.LifeCycleService.PreInvokeEvents">
      <summary>Subscribe to this event to get a notification before every web service call.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Lnk.Id">
      <summary>The identifier for the Link.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Lnk.LnkNum">
      <summary>A persistant identifier.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Lnk.ParEntClsId">
      <summary>The entity class ID of the parent.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Lnk.ParentId">
      <summary>The ID of the parent.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Lnk.ToEntClsId">
      <summary>The type of Entity being pointed to.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Lnk.ToEntId">
      <summary>The ID of the Entity being pointed to.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.LongArray.Items">
      <summary>An array of long integer values.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Map.PairArray">
      <summary>A list of mappings.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.MapPair.FromName">
      <summary>The property name we are mapping from.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.MapPair.ToName">
      <summary>The property name we are mapping to.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.MoveFileCommandEventArgs.AddRestriction(Autodesk.Connectivity.Extensibility.Framework.ExtensionRestriction)">
      <summary>Adds a restriction, which blocks the command. This function should only be called during the GetRestrictions event.</summary>
      <param>The reason that the command cannot run.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.MoveFileCommandEventArgs.Guid">
      <summary>The unique identifier for the event.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.MoveFileCommandEventArgs.Status">
      <summary>The status of the command. Used in the Post event to see of the command was successful.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.MoveFileCommandEventArgs.DstFolderId">
      <summary>The ID of the Folder that the File is moving to.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.MoveFileCommandEventArgs.FileMasterId">
      <summary>The Master ID of the File being moved.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.MoveFileCommandEventArgs.SrcFolderId">
      <summary>The ID of the Folder that the File is moving from.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.MoveFolderCommandEventArgs.AddRestriction(Autodesk.Connectivity.Extensibility.Framework.ExtensionRestriction)">
      <summary>Adds a restriction, which blocks the command. This function should only be called during the GetRestrictions event.</summary>
      <param>The reason that the command cannot run.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.MoveFolderCommandEventArgs.Guid">
      <summary>The unique identifier for the event.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.MoveFolderCommandEventArgs.Status">
      <summary>The status of the command. Used in the Post event to see of the command was successful.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.MoveFolderCommandEventArgs.FolderId">
      <summary>The ID of the Folder being moved.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.MoveFolderCommandEventArgs.NewParentFolderId">
      <summary>The ID of the Folder that will be the new parent Folder.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Msg.CreateDate">
      <summary>The date and time that the message was created.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Msg.CreateUserId">
      <summary>The ID of the user that created the message.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Msg.CreateUserName">
      <summary>The name of the user that created the message.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Msg.ForumId">
      <summary>The ID of the Forum.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Msg.Id">
      <summary>The Vault ID for the object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Msg.MsgTxt">
      <summary>The body of the message.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Msg.ParMsgId">
      <summary>If the message is a reply to another message, this is the ID of the parent message. Otherwise the value is -1.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Msg.Subject">
      <summary>The subject of the message.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.MsgGroup.FileIdArray">
      <summary>An array of file IDs associated with the message.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.MsgGroup.Msg">
      <summary>The message.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.NameValuePair.Name">
      <summary>Name in the pair</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.NameValuePair.Val">
      <summary>Value associated with the name</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.NumSchm.FieldArray">
      <summary>An array of fields that make up the scheme.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.NumSchm.IsAct">
      <summary>If true, the scheme is active. If false, the scheme is inactive.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.NumSchm.IsDflt">
      <summary>If true, the scheme is the default scheme for the repository.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.NumSchm.IsInUse">
      <summary>If true, the scheme is in use by at least one Item. If false, there are no Items using the scheme.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.NumSchm.IsSys">
      <summary>If true, the object is system defined. Otherwise it is a user-defined object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.NumSchm.Name">
      <summary>The unique name of the scheme.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.NumSchm.SchmID">
      <summary>The Vault ID of the object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.NumSchm.SysName">
      <summary>System name of the numbering scheme</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.NumSchm.ToUpper">
      <summary>If true, the characters in the scheme should be displayed in upper case.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.NumSchmField.FieldTyp">
      <summary>An enumeration of field types.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.NumSchmField.Name">
      <summary>The name of the field.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PackageService.CommitImportedData(Autodesk.Connectivity.WebServices.PkgItemsAndBOM)">
      <summary>Commits the item contained in the packageData to complete the import process</summary>
      <returns>The package data that was commited</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PackageService.DeletePackage(System.String)">
      <summary>Deletes a package from the ERP file store.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PackageService.DownloadPackagePart(System.String,System.Int64,System.Int64)">
      <summary>Downloads a package from the server that was created by ExportToPackage</summary>
      <returns>The binary contents of the file part</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PackageService.ExportToPackage(Autodesk.Connectivity.WebServices.PkgItemsAndBOM,Autodesk.Connectivity.WebServices.FileFormat,Autodesk.Connectivity.WebServices.MapPair[])">
      <summary>Creates a package on the server based on the input packageData, fileFormat, and mappingInfo</summary>
      <returns>Information about the package file</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PackageService.GetAllPackages">
      <summary>Gets all the packages in the ERP file store.</summary>
      <returns>Information about the package files in the ERP file store.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PackageService.GetLatestPackageDataByItemIds(System.Int64[],Autodesk.Connectivity.WebServices.BOMTyp)">
      <summary>Gets the item info objects and BOM item info objects to be exported for the specific item IDs</summary>
      <returns>PkgItemsAndBOM object representing the items</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PackageService.GetPackageDataByItemId(System.Int64,Autodesk.Connectivity.WebServices.BOMTyp,System.DateTime)">
      <summary>Gets the item info objects and BOM item info objects to be exported for the specified item</summary>
      <returns>An object containing the exported items and BOM data</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PackageService.GetPreviewInfo(Autodesk.Connectivity.WebServices.FileFormat,System.String)">
      <summary>Gets a preview of an uploaded package for import</summary>
      <returns>Preview of the imported package</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PackageService.ImportFromPackage(Autodesk.Connectivity.WebServices.FileFormat,Autodesk.Connectivity.WebServices.Map,System.String)">
      <summary>Returns the package data that will be imported from an uploaded package</summary>
      <returns>Package data from an imported package</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PackageService.ResolveImportedData(Autodesk.Connectivity.WebServices.PkgItem[])">
      <summary>Updates existing package items (returned from ImportFromPackage) that require updates.</summary>
      <returns>The updated package items</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PackageService.UploadPackagePart(System.String,System.String,System.Byte[])">
      <summary>Uploads a part of the import file contents to the server</summary>
      <returns>Information about the uploaded package to be used with other import APIs</returns>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PackageService.AllowInvokeEvents">
      <summary>Tells if invoke events should fire. This value is true by default.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PackageService.SecurityHeader">
      <summary>The security header. (from IWebService)</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PackageService.WebServiceManager">
      <summary>A reference to the WebServiceManager that created this object. Value may be null.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.PackageService.PostInvokeEvents">
      <summary>Subscribe to this event to get a notification after every web service call.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.PackageService.PreInvokeEvents">
      <summary>Subscribe to this event to get a notification before every web service call.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Permis.Descr">
      <summary>A display name for the permission.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Permis.Id">
      <summary>A unique number that the Vault uses to reference the permission.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgAttmt.CheckSum">
      <summary>Checksum value of the file</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgAttmt.Cloaked">
      <summary>Whether the file is cloaked</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgAttmt.Conflict">
      <summary>Description of any conflict for the file</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgAttmt.FileId">
      <summary>The ID of the File.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgAttmt.FileSize">
      <summary>The size of the File.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgAttmt.ImportFolder">
      <summary>The import folder of the file</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgAttmt.Name">
      <summary>The name of the File.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgAttmt.OriginalName">
      <summary>Original name of the file</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgAttmt.Resolution">
      <summary>Resolution method used when resolving conflicts</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgBOM.BOMArray">
      <summary>An array of BOMItemInfo children.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgBOM.BOMOrder">
      <summary>The BOM order for this item</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgBOM.BOMOrderSpecified">
      <summary>Whether the BOM order was specified</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgBOM.DetailID">
      <summary>The usage (detail ID) for this BOM Item.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgBOM.ExportId">
      <summary>The export ID for this item</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgBOM.InstCount">
      <summary>The instance count</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgBOM.InstCountSpecified">
      <summary>Whether the instance count was specified</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgBOM.ItemIdx">
      <summary>The index of the item list for this BOM Item.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgBOM.Level">
      <summary>The level indicator string.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgBOM.PositionNum">
      <summary>Position number in the BOM</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgBOM.Quant">
      <summary>The usage (quantity) for this BOM Item.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgBOM.QuantSpecified">
      <summary>Whether or not the quantity was specified</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgBOM.RestricStatus">
      <summary>If Succeeded is false, RestrictionStatus will contain the failure reason.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgBOM.Succeed">
      <summary>If true, the BOM Item exported or imported successfully.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgBOM.UDPArray">
      <summary>Array of user defined BOM row properties</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgBOM.UnitSize">
      <summary>The unit size</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgBOM.UnitSizeSpecified">
      <summary>Whether the unit size was specified</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.AttmtArray">
      <summary>An array of file attachments.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.BOMStructId">
      <summary>The ID referencing the CAD BOM structure.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.Comm">
      <summary>Comments related to the revision.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.Consume">
      <summary>Whether the item is marked consumable</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.ControlledByChangeOrder">
      <summary>If true, item is being controlled by a Change Order and certain properties cannot be manually changed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.Descr">
      <summary>A description of the Item.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.ID">
      <summary>A unique identifier for the Item.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.ItemConflict">
      <summary>An import or export conflict.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.ItemDispTyp">
      <summary>The display name of the Item type.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.ItemNum">
      <summary>The Item number.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.ItemTitle">
      <summary>The display title for the Item.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.ItemTyp">
      <summary>The system name of the Item type.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.ItemTypId">
      <summary>The type of Item. Use GetAllItemTypes in the Item Service to get a list of valid values.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.LastModDate">
      <summary>The last time the revision was modified.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.LastModUserId">
      <summary>The ID of the user that made the last modification to the revision.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.LastModUserName">
      <summary>The name of the user that made the last modification to the revision.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.LfCycDefId">
      <summary>The lifecycle definition ID</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.LfCycDefName">
      <summary>The lifecycle definition name</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.LfCycStateId">
      <summary>The Life Cycle State ID of the revision. Use GetLifeCycleDef in the Item Service for a list of valid states.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.LfCycStateName">
      <summary>The name of the Life Cycle State.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.MasterID">
      <summary>The master ID for the Item.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.NumSchmId">
      <summary>The Numbering Scheme for revisions.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.Obsolete">
      <summary>Whether the item is marked obsolete</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.Resolution">
      <summary>The resolution to the conflict.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.RevId">
      <summary>A unique identifier for the Item Revision.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.RevNum">
      <summary>A string that is unique from other revisions of the same Item.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.TipObsolete">
      <summary>Whether the item is the tip obsolete iteration</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.UDPArray">
      <summary>An array of user defined properties.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.UoM">
      <summary>The Unit of Measure name for the revision.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.UoMID">
      <summary>The Unit of Measure ID for the revision. Use GetAllUnitsOfMeasure in the Item Service to get a list of valid values.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItem.VerNum">
      <summary>A sequential number given to each version. For example, if VerNum is 5, than the object is the 5th version.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItemsAndBOM.PkgBOM">
      <summary>The Bill of Materials (BOM) information.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgItemsAndBOM.PkgItemArray">
      <summary>A list of Items.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgProp.Name">
      <summary>The name of the property.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PkgProp.Val">
      <summary>The value of the property.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PredefListField.CodeArray">
      <summary>List of legal values.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PredefListField.DfltVal">
      <summary>The default value.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PredefListField.FieldTyp">
      <summary>The type of field. Inherited from NumSchmField.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PredefListField.Name">
      <summary>The name of the field. Inherited from NumSchmField.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PredefListField.PredefListVal">
      <summary>The assigned value. If NULL, the default value is used.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Product.DisplayName">
      <summary>The display name for the current release.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Product.ProductName">
      <summary>The the internal product name. For backward compatibility, this value contains the original product name, which may not be the same as the current product
name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Product.ProductVersion">
      <summary>The version.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ProductRestric.Code">
      <summary>A code indicating what the restriction is.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ProductRestric.EntId">
      <summary>Identifies the entity associated with the restriction</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ProductRestric.ParamArray">
      <summary>Extra information specific to the restriction code.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PromoteItemCommandEventArgs.AddRestriction(Autodesk.Connectivity.Extensibility.Framework.ExtensionRestriction)">
      <summary>Adds a restriction, which blocks the command. This function should only be called during the GetRestrictions event.</summary>
      <param>The reason that the command cannot run.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PromoteItemCommandEventArgs.Guid">
      <summary>The unique identifier for the event.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PromoteItemCommandEventArgs.ReturnValue">
      <summary>The updated Items and related Files.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PromoteItemCommandEventArgs.Status">
      <summary>The status of the command. Used in the Post event to see of the command was successful.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.PromoteItemCommandEventArgs.FileIds">
      <summary>The Files that are providing the Item data.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.PromoteItemCommandEventArgs.ItemRevisionIds">
      <summary>The Items being updated. This value is null if the Items are being created.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropCompFail.MasterId">
      <summary>The master ID of the entity with the compliance failure..</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropCompFail.PropConstrFailArray">
      <summary>An array of constraint falures.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropCompFail.PropDefId">
      <summary>The property definition with the compliance failure.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropCompFail.PropEquivFailArray">
      <summary>An array of cases where value doesn't match the mapped value.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropConstr.CatId">
      <summary>The category that the constraint applies to.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropConstr.Id">
      <summary>The unique identifier.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropConstr.PropConstrTyp">
      <summary>The type of constraint.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropConstr.PropDefId">
      <summary>The property definition that the constraint applies to.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropConstr.Val">
      <summary>The value of the constraint.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropConstrFail.PropConstrFailTyp">
      <summary>The type of value.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropConstrFail.Val">
      <summary>The constraint value.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropConstrFailure.ItemMasterId">
      <summary>The Item Master ID of the Item with the failure.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropConstrFailure.ItemNum">
      <summary>The Item Number of the Item with the failure.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropConstrFailure.PropConstrId">
      <summary>The ID of the Property Constraint.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropDef.DfltVal">
      <summary>The default value.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropDef.DispName">
      <summary>How the property should be displayed in the UI.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropDef.EntClassAssocArray">
      <summary>The entity classes this property definition is associated to.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropDef.Id">
      <summary>A unique identifier that the vault uses to reference the property definition.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropDef.IsAct">
      <summary>Tells if the property definition is active. True means the object is active. False means the object is inactive, which is what happens when the property
definition is deleted but there are existing property instances.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropDef.IsBasicSrch">
      <summary>If true, this property definition is used in a basic search. If false, this property definition is not used in a basic search.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropDef.IsSys">
      <summary>If true, the proeprty is a system property.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropDef.SysName">
      <summary>How the property should be displayed as a column header.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropDef.Typ">
      <summary>The data type.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropDef.UsageCount">
      <summary>Deprecated - do not use.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropDefCond.Oper">
      <summary>The condition operator.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropDefCond.PropDefId">
      <summary>The property definition ID that the condition</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropDefCond.Val">
      <summary>The value for the condition. For datetime property definitions, the text should be converted to Universal Time and printed in the format "MM/dd/yyyy HH:mm:ss".</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropDefInfo.EntClassCtntSrcPropCfgArray">
      <summary>The content source mappings for each entity class.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropDefInfo.ListValArray">
      <summary>the list of legal values. A null array means all values are allowed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropDefInfo.PropConstrArray">
      <summary>The property constraints.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropDefInfo.PropDef">
      <summary>The property definition object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropEquivFail.DispName">
      <summary>The contetn source property definition display name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropEquivFail.MapDirection">
      <summary>The mapping direction.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropEquivFail.Moniker">
      <summary>The unique identifier for the property within the content source.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropEquivFail.PropEquivFailTyp">
      <summary>The failure type.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropEquivFail.Val">
      <summary>The value of the content source property.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropEquivFail.ValTyp">
      <summary>The value data type.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PropertyService.AddAssociationPropertyDefinition(System.String,System.String,Autodesk.Connectivity.WebServices.DataType,System.Boolean,System.Object,Autodesk.Connectivity.WebServices.EntClassCtntSrcPropCfg[],System.Object[],Autodesk.Connectivity.WebServices.AssocPropTyp)">
      <summary>Adds a Property Definiton for use on associations.</summary>
      <returns>The new Association Property Definition object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PropertyService.AddPropertyDefinition(System.String,System.String,Autodesk.Connectivity.WebServices.DataType,System.Boolean,System.Boolean,System.Object,System.String[],Autodesk.Connectivity.WebServices.EntClassCtntSrcPropCfg[],Autodesk.Connectivity.WebServices.PropConstr[],System.Object[])">
      <summary>Adds a property definition.</summary>
      <returns>The new Property Definition object and related information.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PropertyService.DeleteAssociationPropertyDefinitions(System.Int64[])">
      <summary>Deletes an association property definition.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PropertyService.DeletePropertyDefinitions(System.Int64[])">
      <summary>Deletes a set of property definitions.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PropertyService.EnableAssociationPropertyDefinitions(System.Int64[],System.Boolean)">
      <summary>Enables or disables a set of association property definitions.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PropertyService.EnableBasicSearchForPropertyDefinitions(System.Int64[],System.Boolean)">
      <summary>Enables or disables a property definitions use in a basic search.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PropertyService.EnablePropertyDefinitions(System.Int64[],System.Boolean)">
      <summary>Enables or disables a set of property definitions.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PropertyService.FindPropertyDefinitionsBySystemNames(System.String,System.String[])">
      <summary>Gets a set of property definitions for a given set of system names.</summary>
      <returns>An array of matching property definitions objects. A NULL value will be in the array of the corresponding system name is not found.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PropertyService.GetAllAssociationPropertyDefinitionInfos(Autodesk.Connectivity.WebServices.AssocPropTyp)">
      <summary>Gets all association property definitions with extended information.</summary>
      <returns>An array of all association property definitions with extended information.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PropertyService.GetAllTripleSysNamePairs">
      <summary>Gets all parings between property definition system names and triple values.</summary>
      <returns>An array of all parings between property definition system names and triple values.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PropertyService.GetAssociationPropertyDefinitionInfosByIds(System.Int64[])">
      <summary>Gets a set of property definitions with extended information based on a set of IDs.</summary>
      <returns>The resulting property definition objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PropertyService.GetAssociationPropertyDefinitionsByType(Autodesk.Connectivity.WebServices.AssocPropTyp)">
      <summary>Gets all the property definitions for a given association type.</summary>
      <returns>All the property definitions for the given association type.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PropertyService.GetAssociationPropertyDefinitionsUsageCounts(System.Int64[])">
      <summary>Gets the usage counts for a set of association property definitions.</summary>
      <returns>An array of usages for each association property definition. A usage is the number of entities that use that property.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PropertyService.GetDeleteAssociationPropertyDefinitionsRestrictions(System.Int64[])">
      <summary>Gets any restrictions on deleting a set of association property definitions.</summary>
      <returns>An array of restrictions. The array will be null or empty if there are no restrictions.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PropertyService.GetDeletePropertyDefinitionsRestrictions(System.Int64[])">
      <summary>Gets any restrictions on deleting a set of property definitions.</summary>
      <returns>An array of restrictions. The array will be null or empty if there are no restrictions.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PropertyService.GetProperties(System.String,System.Int64[],System.Int64[])">
      <summary>Gets the values for a set of properties and a set of entities.</summary>
      <returns>A list of all matching properties. The maximum size of the return array is equal to the size of the EntityIds multiplied by the size of propertyDefIDs. The
return array can be smaller if entities do not have some of the properties specified. Null will be returned if none of the entities have any of the properties
specified.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PropertyService.GetPropertiesByEntityIds(System.String,System.Int64[])">
      <summary>Gets all the properties for a set of entities.</summary>
      <returns>An array of all property values for the given entities.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PropertyService.GetPropertyComplianceFailuresByEntityIds(System.String,System.Int64[],System.Boolean)">
      <summary>Get the property compliance failures for a set of objects.</summary>
      <returns>An array of failures. The result will be null if there are no failures.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PropertyService.GetPropertyDefinitionInfosByEntityClassId(System.String,System.Int64[])">
      <summary>Gets a set of property definitions and extended information for a given set of IDs.</summary>
      <returns>The resuting property definition objects with extended data.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PropertyService.GetPropertyDefinitionsByContentSourceIdAndMoniker(System.Int64,System.String)">
      <summary>Gets a list of property definitions mapped to a content source property.</summary>
      <returns>A list of property definitions mapped to the content source property.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PropertyService.GetPropertyDefinitionsByEntityClassId(System.String)">
      <summary>Gets all the property definitions for an entity class.</summary>
      <returns>All property definitions associated with the entity class.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PropertyService.GetPropertyDefinitionsUsageCounts(System.Int64[])">
      <summary>Gets the usage counts for a set of property definitions.</summary>
      <returns>An array of usages for each property definition. A usage is the number of entities that use that property.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PropertyService.GetSystemNamesByTriplicates(System.String[])">
      <summary>Gets the system name of a property definition by a set of triplicate values.</summary>
      <returns>An array of triplicate/"system name" pairs.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PropertyService.UpdateAssociationPropertyDefinition(System.Int64,System.String,System.Boolean,System.Object)">
      <summary>Updates an association property definition object.</summary>
      <returns>The updated property definition with extended information.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PropertyService.UpdateAssociationPropertyDefinitionInfo(Autodesk.Connectivity.WebServices.AssocPropDef,Autodesk.Connectivity.WebServices.EntClassCtntSrcPropCfg[],System.Object[])">
      <summary>Updates an association property definition object and extended information.</summary>
      <returns>The updated objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PropertyService.UpdateContentSourcePropertyDefinitionMappingPriorities(System.String[],System.Int64[],System.Int64[],System.String[],Autodesk.Connectivity.WebServices.MappingDirection[],System.Int32[])">
      <summary>Updates the priorities on a set of content source property mappings.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PropertyService.UpdatePropertyDefinition(System.Int64,System.String,System.Boolean,System.Boolean,System.Object,System.String[])">
      <summary>Updates a property definition.</summary>
      <returns>The updated property definition with extended information.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PropertyService.UpdatePropertyDefinitionInfo(Autodesk.Connectivity.WebServices.PropDef,Autodesk.Connectivity.WebServices.EntClassCtntSrcPropCfg[],Autodesk.Connectivity.WebServices.PropConstr[],System.Object[])">
      <summary>Updates a property definition and its extended information.</summary>
      <returns>The updated Property Definition object and related information.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.PropertyService.UpdateSystemPropertyDefinition(System.Int64,System.String,System.Boolean,Autodesk.Connectivity.WebServices.EntClassCtntSrcPropCfg[],Autodesk.Connectivity.WebServices.PropConstr[])">
      <summary>Updates information about a system property.</summary>
      <returns>The updated property definition object with related information.</returns>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropertyService.AllowInvokeEvents">
      <summary>Tells if invoke events should fire. This value is true by default.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropertyService.SecurityHeader">
      <summary>The security header. (from IWebService)</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.PropertyService.PostInvokeEvents">
      <summary>Subscribe to this event to get a notification after every web service call.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.PropertyService.PreInvokeEvents">
      <summary>Subscribe to this event to get a notification before every web service call.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropInst.EntityId">
      <summary>The Item that this property is associated with.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropInst.PropDefId">
      <summary>The id of the property definition that defines this property.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropInst.Val">
      <summary>The value of the property.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropInst.ValTyp">
      <summary>The data type of Val.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropInstParam.PropDefId">
      <summary>Property definition ID of the property to update</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropInstParam.Val">
      <summary>Value of the property being updated</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropInstParamArray.Items">
      <summary>Collection of property instance parameters</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropRestric.Code">
      <summary>The restriciton code.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropRestric.ParamArray">
      <summary>An array of parameters.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.PropRestric.PropDefId">
      <summary>The property defintion.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ReplicationService.GetAllWorkgroups">
      <summary>Gets all the workgroups.</summary>
      <returns>An array of Workgroup objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ReplicationService.GetDatabaseOwnership(Autodesk.Connectivity.WebServices.DatabaseType)">
      <summary>Get ownership infromation of a database. Database ownership covers anything that can't be owned on an entity basis.</summary>
      <returns>Onwership information.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ReplicationService.GetDefaultLeaseDuration">
      <summary>Gets the default lease duration.</summary>
      <returns>The default lease duration in minutes.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ReplicationService.GetLocalWorkgroup">
      <summary>Gets the workgroup that you are currently connected to.</summary>
      <returns>The workgroup object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ReplicationService.GetOwnershipByEntityId(System.Int64[])">
      <summary>Gets the ownership for an entity.</summary>
      <returns>The ownership information.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ReplicationService.GetReplicatingWorkgroupsByDatabaseType(Autodesk.Connectivity.WebServices.DatabaseType)">
      <summary>Gets the workgroups that are replicating the current database.</summary>
      <returns>The workgroups replicating the current database.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ReplicationService.GetWorkgroupByDatabaseServer(System.String)">
      <summary>Gets the workgroup for a given database instance.</summary>
      <returns>The Workgroup object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ReplicationService.GetWorkgroupsByDatabaseType(Autodesk.Connectivity.WebServices.DatabaseType)">
      <summary>Gets the Workgroups that have the signed in database.</summary>
      <returns>The Workgroups that have the signed in database.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ReplicationService.SetDefaultLeaseDuration(System.Int32)">
      <summary>Sets the default lease duration.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ReplicationService.TransferDatabaseOwnership(Autodesk.Connectivity.WebServices.DatabaseType,System.Int64,System.Int32)">
      <summary>Sets the database ownership for a database. Database ownership covers anything that can't be owned on an entity basis.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.ReplicationService.TransferEntityGroupOwnership(System.Int64,Autodesk.Connectivity.WebServices.LongArray[],System.Int32)">
      <summary>Transfers ownership on groups of entities.</summary>
      <returns>The status of the transfer. It is possible for some groups to succeed while others fail. This array will not be the same size as the 'entityIDGroups' array.</returns>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ReplicationService.AllowInvokeEvents">
      <summary>Tells if invoke events should fire. This value is true by default.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ReplicationService.KnowledgeLibraryHeaderValue">
      <summary>Internal use only.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ReplicationService.SecurityHeader">
      <summary>The security header. (from IWebService)</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ReplicationService.WebServiceManager">
      <summary>A reference to the WebServiceManager that created this object. Value may be null.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.ReplicationService.PostInvokeEvents">
      <summary>Subscribe to this event to get a notification after every web service call.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.ReplicationService.PreInvokeEvents">
      <summary>Subscribe to this event to get a notification before every web service call.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Resolution.ResolutionMethod">
      <summary>How the conflict was resolved.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Resolution.Restric">
      <summary>The status of the restriction.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RevDef.CommArray">
      <summary>A collection of comments.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RevDef.Delim">
      <summary>The delimiter character.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RevDef.Descr">
      <summary>A description of the revision.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RevDef.DispName">
      <summary>The display name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RevDef.EntClassAssocArray">
      <summary>The associated entity classes.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RevDef.Id">
      <summary>The unique identifier for the object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RevDef.IsSys">
      <summary>If true, the revision is system defined, which means there are more restrictions on what can be edited.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RevDef.PriSchmId">
      <summary>The ID of the sequence used for the primary number.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RevDef.SecSchmId">
      <summary>The ID of the sequence used for the secondary number.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RevDef.SysName">
      <summary>The system name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RevDef.TerSchmId">
      <summary>The ID of the sequence used for the tertiary number.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RevDefInfo.RevDefArray">
      <summary>An array of revision definitions.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RevDefInfo.RevSeqArray">
      <summary>An array of revision sequences.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.RevisionService.AddRevisionDefinition(System.String,System.String,System.Char,System.Int64,System.Int64,System.Int64,System.String[])">
      <summary>Adds a new revision defintion.</summary>
      <returns>The new revision definition object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.RevisionService.AddRevisionDefinitionAndSequence(System.String,System.String,System.Char,System.String,System.String[],System.Int32)">
      <summary>Adds a revision definition and sequence.</summary>
      <returns>The new definition and sequence.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.RevisionService.AddRevisionSequence(System.String,System.String[],System.Int32)">
      <summary>Adds a revision sequence.</summary>
      <returns>The new revision sequence object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.RevisionService.DeleteRevisionDefinition(System.Int64)">
      <summary>Deletes a revision definition.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.RevisionService.DeleteRevisionSequences(System.Int64[])">
      <summary>Deletes a set of revision sequences.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.RevisionService.GetAllRevisionDefinitionInfo">
      <summary>Gets all revision definitions and sequences.</summary>
      <returns>All revision definitions and sequences</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.RevisionService.GetNextRevisionNumbersByMasterIds(System.Int64[],System.Int64[])">
      <summary>Gets the next revision numbers for a set of files.</summary>
      <returns>An array of arrays of strings. The top level array matches the input arrays. The sub arrays consist of the primary, secondary and tertiary values.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.RevisionService.GetRevisionDefinitionDeleteRestrictionsById(System.Int64)">
      <summary>Gets any restrictions on deleting a revision definition.</summary>
      <returns>An array of restrictions. If there are no restrictions, null is returned.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.RevisionService.GetRevisionDefinitionIdsByMasterIds(System.Int64[])">
      <summary>Gets the revision definitions for a set of entities.</summary>
      <returns>the corresponding revision definition IDs.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.RevisionService.GetRevisionDefinitionInfoByIds(System.Int64[])">
      <summary>Gets a set of revision defintions by their IDs.</summary>
      <returns>An object containing the correstonding revision definition objects.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.RevisionService.UpdateRevisionDefinition(System.Int64,System.String,System.String,System.Char,System.Int64,System.Int64,System.Int64,System.String[])">
      <summary>Updates a revision definition.</summary>
      <returns>The updated revision definition object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.RevisionService.UpdateRevisionSequence(System.Int64,System.String,System.String[],System.Int32)">
      <summary>Updates a revision sequence.</summary>
      <returns>The updated revision sequence object.</returns>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RevisionService.AllowInvokeEvents">
      <summary>Tells if invoke events should fire. This value is true by default.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RevisionService.SecurityHeader">
      <summary>The security header. (from IWebService)</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RevisionService.WebServiceManager">
      <summary>A reference to the WebServiceManager that created this object. Value may be null.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.RevisionService.PostInvokeEvents">
      <summary>Subscribe to this event to get a notification after every web service call.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.RevisionService.PreInvokeEvents">
      <summary>Subscribe to this event to get a notification before every web service call.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RevLabel.LabelArray">
      <summary>An array of label values.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RevLabel.StartIdx">
      <summary>The start index. The value is zero indexed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RevSeq.DispName">
      <summary>The display name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RevSeq.Id">
      <summary>The unique identifier for the object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RevSeq.IsSys">
      <summary>If true, the object is system defined, which means there are more restrictions on what can be edited.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RevSeq.Label">
      <summary>The label.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RevSeq.Name">
      <summary>The name of the sequence.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Role.Id">
      <summary>A unique number for the role.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Role.Name">
      <summary>The name of the role.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Routing.Id">
      <summary>The ID for the object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Routing.IsAct">
      <summary>If true, the Routing is active.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Routing.IsDflt">
      <summary>If true, the Routing is the default for the repository.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Routing.IsInUse">
      <summary>If true, the Routing is in use.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Routing.Name">
      <summary>The unique name of the Routing.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Routing.ReqAllApproval">
      <summary>If true, all users that can approve, must approve in order to complete the process.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RoutingMemb.RoleIdArray">
      <summary>The routing roles associated with the user.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RoutingMemb.UserId">
      <summary>The ID of the user.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RoutingMemb.UserName">
      <summary>The name of the user.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RoutingMembAndRoles.MembArray">
      <summary>An array of Routing Members.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RoutingMembAndRoles.RoleDefArray">
      <summary>An array of Role Definition objects.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RoutingRoleDef.Access">
      <summary>The access level.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RoutingRoleDef.Id">
      <summary>The Vault ID for the object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RoutingRoleDef.Name">
      <summary>The unique name of the role.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RoutingUserRoles.RoleIdArray">
      <summary>An array of Routing Roles.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RoutingUserRoles.UserId">
      <summary>A user ID.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RuleSet.CondArray">
      <summary>An array of property conditions.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RuleSet.Desc">
      <summary>A description of the rule set.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RuleSet.DispName">
      <summary>The display name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.RuleSet.Id">
      <summary>The unique identifier for the object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.SecRestric.Code">
      <summary>A code describing the restriction.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.SecRestric.ParamArray">
      <summary>An array of parameters. The content of the array is dependent on the restriction code.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.SecurityHeader.Ticket">
      <summary>The authentication ticket.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.SecurityHeader.UserId">
      <summary>The ID of the logged-in user.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.SecurityService.AddSystemACL(Autodesk.Connectivity.WebServices.ACE[])">
      <summary>Adds a new system ACL.</summary>
      <returns>The newly created system ACL. However it is not yet bound to any entities.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.SecurityService.GetACLsByEntityIds(System.Int64[])">
      <summary>Gets the ACL data for a list of objects.</summary>
      <returns>An array of ACLs corresponding to the input array. If the object has no ACL, null will be returned.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.SecurityService.GetACLUpdateRestrictions(System.Int64,Autodesk.Connectivity.WebServices.ACE[])">
      <summary>Gets any restrictions for updating an Access Control List.</summary>
      <returns>An array of restrictions for updating the ACL. Null is returned if there are no restrictions.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.SecurityService.GetEntACLsByEntityIds(System.Int64[])">
      <summary>Gets all the ACL information for a set of file or folder IDs.</summary>
      <returns>A data structure listing all the Entities and their ACLs.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.SecurityService.SetSystemACLs(System.Int64[],System.Int64)">
      <summary>Sets the system ACL on a set of entities.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.SecurityService.UpdateACL(System.Int64,Autodesk.Connectivity.WebServices.ACE[],Autodesk.Connectivity.WebServices.PrpgType)">
      <summary>Adds, modifies, or deletes the ACL for an object.</summary>
      <returns>The newly created ACL object.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.SecurityService.UpdateSystemACL(System.Int64,Autodesk.Connectivity.WebServices.ACE[],Autodesk.Connectivity.WebServices.PrpgType)">
      <summary>Updates the system ACL for an Entity.</summary>
      <returns>The update Access Control List.</returns>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.SecurityService.AllowInvokeEvents">
      <summary>Tells if invoke events should fire. This value is true by default.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.SecurityService.SecurityHeader">
      <summary>The security header. (from IWebService)</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.SecurityService.WebServiceManager">
      <summary>A reference to the WebServiceManager that created this object. Value may be null.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.SecurityService.PostInvokeEvents">
      <summary>Subscribe to this event to get a notification after every web service call.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.SecurityService.PreInvokeEvents">
      <summary>Subscribe to this event to get a notification before every web service call.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ServerCfg.BhvArray">
      <summary>All the behaviors in the system.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ServerCfg.CtntSrcArray">
      <summary>All the content sources on the system.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ServerCfg.EntClassCfgArray">
      <summary>The entity classes on the system.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ServerCfg.Equiv">
      <summary>If true, the system can report if a user defined property value is not equivalent to a mapped content source property. If false, the system will not detect if
the values are equivalent or not.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ServerCfg.InitVals">
      <summary>If true, user defined properties support an initial default value.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ServerCfg.NoCtntMap">
      <summary>If true, the system supports user defined properties that are not mapped to a content source property. If false, all user defined properties must be mapped to
a content source property.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ServerCfg.Policy">
      <summary>If true, the system supports property constraints. If false, the system does not support property constraints.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ServerCfg.PropLists">
      <summary>If true, user defined properties support list types. If false it is not possible to have user defined property values set from a list.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ServerIdentities.DataServer">
      <summary>The data server location, which is where all the database information resides.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.ServerIdentities.FileServer">
      <summary>The file store server location, which is where all the file information resides.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.SiteCompat.FacilityArray">
      <summary>If the site is less than the system, this property is an array of facilities missing from the site. If the system is less than the site, this property is an
array of facilities missing from the system. If the site and system are equal, this value is empty.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.SiteCompat.ProdArray">
      <summary>If the site is less than the system, this property is an array of products missing from the site. If the system is less than the site, this property is an
array of products missing from the system. If the site and system are equal, this value is empty.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.SiteCompat.Status">
      <summary>Tells if the site is greater than, less than or equal to the system.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.SrchCond.PropDefId">
      <summary>The ID of the property definition to search on. This parameter is only used if PropTyp is set to SingleProperty.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.SrchCond.PropTyp">
      <summary>The type of search being done. If they types are AllProperties or AllPropertiesAndContent, then SrchOper must have a value of 1 (Contains).</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.SrchCond.SrchOper">
      <summary>A number representing the operator used for the search. See table below.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.SrchCond.SrchRule">
      <summary>Tells if the condition must, may or may not be true. In other words, does is the condition an AND, OR or NOT type.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.SrchCond.SrchTxt">
      <summary>The value to use for the search. This value may not be used depending on SrchOper. When doing a datetime search, the text should be converted to Universal Time
and printed in the format "MM/dd/yyyy HH:mm:ss".</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.SrchSort.PropDefId">
      <summary>The property definition to sort on.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.SrchSort.SortAsc">
      <summary>If true, the sort is ascending. If false, the sort is descending.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.SrchStatus.IndxStatus">
      <summary>The status of the indexing engine.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.SrchStatus.TotalHits">
      <summary>Number of total hits. If this value is larger than the objects returned, then multiple searches will have to be performed to get the complete result set.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.StateACE.Permis">
      <summary>A set of permissions.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.StateACE.UserGrpId">
      <summary>A user or group ID.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.StateACE.UserGrpName">
      <summary>The user or group name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.StateACL.ACEArray">
      <summary>An array of access control entries.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.StateACL.Id">
      <summary>A unique identifier for this object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.StringArray.Items">
      <summary>An array of string values</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.TransACE.Allowed">
      <summary>If true, the user or group is allowed. If false, the user or group is denied.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.TransACE.UserGrpId">
      <summary>A user or group ID.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.TransACE.UserGrpName">
      <summary>A user or group name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.TripleSysNamePair.SysName">
      <summary>The system name identifier.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.TripleSysNamePair.Triple">
      <summary>The triplicate identifier.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.UnitOfMeasure.Abbr">
      <summary>The abbreviated name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.UnitOfMeasure.BaseUofMID">
      <summary>The base Unit of Measure. If the object is its own base unit, then this value is equal to UofMID.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.UnitOfMeasure.Conversion">
      <summary>The conversion factor. This number is used to convert from the base unit. If the object is its own base unit, this value should be 1.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.UnitOfMeasure.Id">
      <summary>The ID of the object.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.UnitOfMeasure.SysName">
      <summary>System name of the unit of measure</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.UnitOfMeasure.SysUofM">
      <summary>If true, the object is a system Unit of Measure and cannot be modified. If false, the object is a user-defined Unit of Measure.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.UnitOfMeasure.UnitName">
      <summary>The name of the unit.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.UpdateChangeOrderLifeCycleStateCommandEventArgs.AddRestriction(Autodesk.Connectivity.Extensibility.Framework.ExtensionRestriction)">
      <summary>Adds a restriction, which blocks the command. This function should only be called during the GetRestrictions event.</summary>
      <param>The reason that the command cannot run.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.UpdateChangeOrderLifeCycleStateCommandEventArgs.Guid">
      <summary>The unique identifier for the event.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.UpdateChangeOrderLifeCycleStateCommandEventArgs.ReturnValue">
      <summary>The updated Change Order.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.UpdateChangeOrderLifeCycleStateCommandEventArgs.Status">
      <summary>The status of the command. Used in the Post event to see of the command was successful.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.UpdateChangeOrderLifeCycleStateCommandEventArgs.ActivityId">
      <summary>The activity being completed.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.UpdateChangeOrderLifeCycleStateCommandEventArgs.AddComments">
      <summary>Comments to be added to the change order.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.UpdateChangeOrderLifeCycleStateCommandEventArgs.ChangeOrderId">
      <summary>The Change Order being updated.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.UpdateChangeOrderLifeCycleStateCommandEventArgs.NotifyEmails">
      <summary>Emails to send out upon completion.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.UpdateChangeOrderLifeCycleStateCommandEventArgs.StateEntered">
      <summary>Time when the change order entered its state.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.UpdateChangeOrderLifeCycleStateCommandEventArgs.StateId">
      <summary>The ID of the state that the Change Order is currently in.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.UpdateCustomEntityLifeCycleStateCommandEventArgs.AddRestriction(Autodesk.Connectivity.Extensibility.Framework.ExtensionRestriction)">
      <summary>Adds a restriction, which blocks the command. This function should only be called during the GetRestrictions event.</summary>
      <param>The reason that the command cannot run.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.UpdateCustomEntityLifeCycleStateCommandEventArgs.Guid">
      <summary>The unique identifier for the event.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.UpdateCustomEntityLifeCycleStateCommandEventArgs.ReturnValue">
      <summary>The updated Custom Entities.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.UpdateCustomEntityLifeCycleStateCommandEventArgs.Status">
      <summary>The status of the command. Used in the Post event to see of the command was successful.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.UpdateCustomEntityLifeCycleStateCommandEventArgs.Comment">
      <summary>The comment.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.UpdateCustomEntityLifeCycleStateCommandEventArgs.CustomEntityIds">
      <summary>The Custom Entities changing state.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.UpdateCustomEntityLifeCycleStateCommandEventArgs.ToStateIds">
      <summary>The new state for the corresponding entry in CustomEntityIds.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.UpdateFileLifeCycleStateCommandEventArgs.AddRestriction(Autodesk.Connectivity.Extensibility.Framework.ExtensionRestriction)">
      <summary>Adds a restriction, which blocks the command. This function should only be called during the GetRestrictions event.</summary>
      <param>The reason that the command cannot run.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.UpdateFileLifeCycleStateCommandEventArgs.Guid">
      <summary>The unique identifier for the event.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.UpdateFileLifeCycleStateCommandEventArgs.ReturnValue">
      <summary>The updated Files.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.UpdateFileLifeCycleStateCommandEventArgs.Status">
      <summary>The status of the command. Used in the Post event to see of the command was successful.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.UpdateFileLifeCycleStateCommandEventArgs.Comment">
      <summary>The comment for the new version of the Files.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.UpdateFileLifeCycleStateCommandEventArgs.FileMasterIds">
      <summary>The Files changing state.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.UpdateFileLifeCycleStateCommandEventArgs.ToStateIds">
      <summary>The new state for the corresponding entry in FileMasterIds.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.UpdateItemLifeCycleStateCommandEventArgs.AddRestriction(Autodesk.Connectivity.Extensibility.Framework.ExtensionRestriction)">
      <summary>Adds a restruction to the command. Used to block the command.</summary>
      <param>The reason that the command cannot run.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.UpdateItemLifeCycleStateCommandEventArgs.Guid">
      <summary>The unique identifier for the event.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.UpdateItemLifeCycleStateCommandEventArgs.ReturnValue">
      <summary>The updated Item objects.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.UpdateItemLifeCycleStateCommandEventArgs.Status">
      <summary>The status of the command. Used in the Post event to see of the command was successful.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.UpdateItemLifeCycleStateCommandEventArgs.Comment">
      <summary>The comment for the new version of the Items.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.UpdateItemLifeCycleStateCommandEventArgs.ItemMasterIds">
      <summary>The Items changing state.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.UpdateItemLifeCycleStateCommandEventArgs.ToStateIds">
      <summary>The state that the Items are changing to.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.User.Auth">
      <summary>The authentication type.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.User.CreateDate">
      <summary>The date that the user was created.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.User.CreateUserId">
      <summary>The user that created this user.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.User.Email">
      <summary>The email address of the user.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.User.FirstName">
      <summary>The first name of the user.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.User.Id">
      <summary>A unique number that the Vault uses to reference the user.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.User.IsActive">
      <summary>If false, the user account is inactive, and the user cannot log in.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.User.IsSys">
      <summary>If true, the user is a system user.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.User.LastName">
      <summary>The last name of the user.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.User.Name">
      <summary>The login name of the user.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.UserInfo.Roles">
      <summary>An array of Role objects associated with this user.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.UserInfo.User">
      <summary>The userId of the user.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.UserInfo.Vaults">
      <summary>An array of KnowledgeVault objects associated with this user.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.WebServiceCommandEventArgs.AddRestriction(Autodesk.Connectivity.Extensibility.Framework.ExtensionRestriction)">
      <summary>Adds a restriction, which blocks the command. This function should only be called during the GetRestrictions event.</summary>
      <param>The reason that the command cannot run.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.WebServiceCommandEventArgs.Guid">
      <summary>The unique identifier for the event.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.WebServiceCommandEventArgs.Status">
      <summary>The status of the command. Used in the Post event to see of the command was successful.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.WebServiceCommandEvents`1.GetRestrictions">
      <summary>This is called first. It allows handlers to add restrictions, which block the web service call.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.WebServiceCommandEvents`1.Post">
      <summary>This event is called after the web service function is called.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.WebServiceCommandEvents`1.Pre">
      <summary>This event is called after GetRestrictions and before the web service function is called.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.WebServiceInvokeEventArgs.Guid">
      <summary>Gets the unique identifier for the event.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.WebServiceInvokeEventArgs.Exception">
      <summary>Contains the Exception from the method call. If null, then the method completed with no Exceptions fired.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.WebServiceInvokeEventArgs.MethodName">
      <summary>The name of the method being called.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.WebServiceInvokeEventArgs.Parameters">
      <summary>The method parameters.</summary>
    </member>
    <member name="F:Autodesk.Connectivity.WebServices.WebServiceInvokeEventArgs.ReturnValue">
      <summary>The return values. The first element will be the standard return. Elements after are from [out] parameters.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.WinAuthService.SignIn(System.String,System.String)">
      <summary>Authenticates to a specific Knowledge Vault via Windows credentials.</summary>
      <returns>Although there is no return value, a successful sign in will populate the SecurityHeaderValue of the security service. The SecurityHeaderValue information is
then used for other web service calls.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.WinAuthService.SignIn2(System.String)">
      <summary>Authenticates via Windows credentials to ADMS but not to a specific Knowledge Vault.</summary>
      <returns>Although there is no return value, a successful sign in will populate the SecurityHeaderValue of the security service. The SecurityHeaderValue information is
then used for other web service calls.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.WinAuthService.SignInAltLicense(System.String,System.String,System.Byte[])">
      <summary>Reserved for future use</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServices.WinAuthService.SignInReadOnly(System.String,System.String)">
      <summary>Authenticates to a specific Vault with read only permissions via Windows authentication.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.WinAuthService.AllowInvokeEvents">
      <summary>Tells if invoke events should fire. This value is true by default.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.WinAuthService.SecurityHeader">
      <summary>The security header. (from IWebService)</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.WinAuthService.WebServiceManager">
      <summary>A reference to the WebServiceManager that created this object. Value may be null.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.WinAuthService.PostInvokeEvents">
      <summary>Subscribe to this event to get a notification after every web service call.</summary>
    </member>
    <member name="E:Autodesk.Connectivity.WebServices.WinAuthService.PreInvokeEvents">
      <summary>Subscribe to this event to get a notification before every web service call.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Wkgrp.DbServer">
      <summary>The database instance name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Wkgrp.Descr">
      <summary>The description.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Wkgrp.Id">
      <summary>The workgroup ID.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Wkgrp.Label">
      <summary>A label for use when auto-generating names and numbers.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Wkgrp.ReplState">
      <summary>The replication state.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.WkgrpLabelField.FieldTyp">
      <summary>The type of field. Inherited from NumSchmField.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.WkgrpLabelField.Name">
      <summary>The name of the field. Inherited from NumSchmField.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.WkgrpLabelField.Val">
      <summary>The value.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Wmark.Active">
      <summary>If true, the watermark is active. If false, the watermark is not active.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Wmark.Alpha">
      <summary>The green component of the color.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Wmark.Blue">
      <summary>The blue component of the color.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Wmark.EntClassIdArray">
      <summary>The entity class IDs for the property definitions. Must match the PropDefIdArray size.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Wmark.FontName">
      <summary>The name of the font.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Wmark.FreeTextArray">
      <summary>An array of free text. Must match the FreeTextRankArray size.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Wmark.FreeTextRankArray">
      <summary>The ranks for the free text values. Must match the FreeTextArray size.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Wmark.Green">
      <summary>The green component of the color.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Wmark.Id">
      <summary>The unique identifier.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Wmark.Loc">
      <summary>The location of the Watermark.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Wmark.PropDefIdArray">
      <summary>An array of property definitions. Must match the EntClassIdArray size.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Wmark.Red">
      <summary>The red component of the color.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Wmark.Size">
      <summary>The text size.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Wmark.StateId">
      <summary>The ID of the life cycle state and effectivity that applies to the Watermark.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Wmark.Text">
      <summary>The display text.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Wmark.Typ">
      <summary>The source of information for the watermark.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Workflow.DispName">
      <summary>A display name for the workflow.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Workflow.Id">
      <summary>A unique identifier for the workflow.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Workflow.IsAct">
      <summary>If true, then the Workflow is active.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Workflow.IsDflt">
      <summary>If true, then the Workflow is the default workflow.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.Workflow.Name">
      <summary>A unique name for the workflow.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.WorkflowInfo.ActivityArray">
      <summary>An array of all the Activities in the Workflow. An Activity is a connection from one state to another.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.WorkflowInfo.WorkflowStateArray">
      <summary>An array of all the states in the Workflow.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.WorkflowState.DispName">
      <summary>The display name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.WorkflowState.Id">
      <summary>The unique identifier.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.WorkflowState.Name">
      <summary>The unique name.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.WorkflowState.SuccessorActivityIdArray">
      <summary>An array of activities avilable for this state.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.XferStatus.EntId">
      <summary>The entity ID.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.XferStatus.ErrorCode">
      <summary>The error code. This parameter is only used if 'isSuccess' is false.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.XferStatus.ErrorParam">
      <summary>Additional information on the error. This parameter is only used if 'isSuccess' is false.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.XferStatus.IsComplete">
      <summary>
        <para>If true, it indicates that the transfer was successful and already completed.</para>
        <para>If false, it indicates that either the transfer failed or needs to wait for a sync to complete.</para>
      </summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServices.XferStatus.IsSuccess">
      <summary>If true the entity has successfully transfered ownership. If false, the transfer has failed.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServicesTools.IWebServiceCredentials">
      <summary>Properties and methods for establishing and maintaining authentication data.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServicesTools.IWebServiceCredentials.SignIn(Autodesk.Connectivity.WebServices.AuthService,Autodesk.Connectivity.WebServices.WinAuthService)">
      <summary>Signs in to the Vault server.</summary>
      <param>An AuthService object.</param>
      <param>A Win Auth Service object.</param>
      <returns>The security header from the sign in.</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServicesTools.IWebServiceCredentials.SignOut(Autodesk.Connectivity.WebServices.AuthService,Autodesk.Connectivity.WebServices.WinAuthService)">
      <summary>Signs out from the Vault server.</summary>
      <param>A AuthService object.</param>
      <param>A Win Auth Service object.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.IWebServiceCredentials.RequiresSignIn">
      <summary>Gets the value telling if a sign in is required before a service can be used.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.IWebServiceCredentials.RequiresSignOut">
      <summary>Gets the value telling if a sign out should be called when the service goes out of scope.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.IWebServiceCredentials.SecurityHeader">
      <summary>Gets the security header.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.IWebServiceCredentials.ServerIdentities">
      <summary>The server identity.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.IWebServiceCredentials.SupportsSignIn">
      <summary>Gets the value telling if the SignIn method is implemented.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.IWebServiceCredentials.SupportsSignOut">
      <summary>Gets the value telling if the SignOut method is implemented.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.IWebServiceCredentials.VaultName">
      <summary>The name of the vault.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServicesTools.AnonymousCredentials">
      <summary>Credentials for making anonymous server calls.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServicesTools.ContainerCredentials">
      <summary>A set of credentials that contains another set of credentials. This is useful for cases where a new sign in is not required, but a re-sign-in may be needed.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServicesTools.UserIdTicketCredentials">
      <summary>Credentials for making server calls with userId and ticket information from a previous sign in.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServicesTools.UserPasswordCredentials">
      <summary>Credentials for making server calls with username and password information.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServicesTools.WebServiceCredentials">
      <summary>Credentials for making server calls based on information from an existing web service.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServicesTools.WebServiceErrorInfo">
      <summary>Information about an error from the Vault server.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServicesTools.WebServiceManager">
      <summary>A class for managing web service objects and connection information.</summary>
    </member>
    <member name="T:Autodesk.Connectivity.WebServicesTools.WinAuthCredentials">
      <summary>Credentials for making server calls with Windows user account information.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServicesTools.AnonymousCredentials.#ctor(System.String)">
      <summary>Creates new anonymous credentials.</summary>
      <param>The name of the Vault file server.</param>
    </member>
    <member name="M:Autodesk.Connectivity.WebServicesTools.AnonymousCredentials.#ctor(Autodesk.Connectivity.WebServices.ServerIdentities)">
      <summary>Creates new anonymous credentials.</summary>
      <param>The set of Vault servers.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.AnonymousCredentials.RequiresSignIn">
      <summary>Gets the value telling if a sign in is required before a service can be used.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.AnonymousCredentials.RequiresSignOut">
      <summary>Gets the value telling if a sign out should be called when the service goes out of scope.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.AnonymousCredentials.SecurityHeader">
      <summary>Gets the security header.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.AnonymousCredentials.ServerIdentities">
      <summary>Gets the server identity.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.AnonymousCredentials.SupportsSignIn">
      <summary>Gets the value telling if the credentials can sign in.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.AnonymousCredentials.SupportsSignOut">
      <summary>Gets the value telling if the credentials can sign out.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.AnonymousCredentials.VaultName">
      <summary>Gets the vault name.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServicesTools.ContainerCredentials.#ctor(Autodesk.Connectivity.WebServicesTools.IWebServiceCredentials,Autodesk.Connectivity.WebServices.SecurityHeader)">
      <summary>The constructor.</summary>
      <param>The existing credentials.</param>
      <param>The Security header</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.ContainerCredentials.RequiresSignIn">
      <summary>Gets the value telling if a sign in is required before a service can be used.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.ContainerCredentials.RequiresSignOut">
      <summary>Gets the value telling if a sign out should be called when the service goes out of scope.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.ContainerCredentials.SecurityHeader">
      <summary>Gets the security header.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.ContainerCredentials.ServerIdentities">
      <summary>Gets the server identity.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.ContainerCredentials.SupportsSignIn">
      <summary>Gets the value telling if the credentials can sign in.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.ContainerCredentials.SupportsSignOut">
      <summary>Gets the value telling if the credentials can sign out.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.ContainerCredentials.VaultName">
      <summary>Gets the vault name.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServicesTools.UserIdTicketCredentials.#ctor(System.String,System.String,System.Int64,System.String)">
      <summary>The constructor.</summary>
      <param>The name of the Vault file server.</param>
      <param>The name of the Vault.</param>
      <param>The User ID</param>
      <param>The security ticket.</param>
    </member>
    <member name="M:Autodesk.Connectivity.WebServicesTools.UserIdTicketCredentials.#ctor(Autodesk.Connectivity.WebServices.ServerIdentities,System.String,System.Int64,System.String)">
      <summary>The constructor.</summary>
      <param>The set of Vault servers.</param>
      <param>The name of the Vault.</param>
      <param>The User ID</param>
      <param>The security ticket.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.UserIdTicketCredentials.RequiresSignIn">
      <summary>Gets the value telling if a sign in is required before a service can be used.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.UserIdTicketCredentials.RequiresSignOut">
      <summary>Gets the value telling if a sign out should be called when the service goes out of scope.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.UserIdTicketCredentials.SecurityHeader">
      <summary>Gets the security header.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.UserIdTicketCredentials.ServerIdentities">
      <summary>Gets the server identity.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.UserIdTicketCredentials.SupportsSignIn">
      <summary>Gets the value telling if the credentials can sign in.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.UserIdTicketCredentials.SupportsSignOut">
      <summary>Gets the value telling if the credentials can sign out.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.UserIdTicketCredentials.VaultName">
      <summary>Gets the vault name.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServicesTools.UserPasswordCredentials.#ctor">
      <summary>The Constructor.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServicesTools.UserPasswordCredentials.#ctor(System.String,System.String,System.String,System.String)">
      <summary>The Constructor.</summary>
      <param>The file server name.</param>
      <param>The vault name. Enter null for cases that need global access.</param>
      <param>The user name.</param>
      <param>The password.</param>
    </member>
    <member name="M:Autodesk.Connectivity.WebServicesTools.UserPasswordCredentials.#ctor(Autodesk.Connectivity.WebServices.ServerIdentities,System.String,System.String,System.String)">
      <summary>The Constructor.</summary>
      <param>The set of Vault servers.</param>
      <param>The vault name. Enter null for cases that need global access.</param>
      <param>The user name.</param>
      <param>The password.</param>
    </member>
    <member name="M:Autodesk.Connectivity.WebServicesTools.UserPasswordCredentials.#ctor(System.String,System.String,System.String,System.String,System.Boolean)">
      <summary>The Constructor.</summary>
      <param>The file server name.</param>
      <param>The vault name. Enter null for cases that need global access.</param>
      <param>The user name.</param>
      <param>The password.</param>
      <param>The read only setting. Default is false.</param>
    </member>
    <member name="M:Autodesk.Connectivity.WebServicesTools.UserPasswordCredentials.#ctor(Autodesk.Connectivity.WebServices.ServerIdentities,System.String,System.String,System.String,System.Boolean)">
      <summary>The Constructor.</summary>
      <param>The set of Vault servers.</param>
      <param>The vault name. Enter null for cases that need global access.</param>
      <param>The user name.</param>
      <param>The password.</param>
      <param>The read only setting. Default is false.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.UserPasswordCredentials.ReadOnly">
      <summary>Gets if the connection is read only</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.UserPasswordCredentials.RequiresSignIn">
      <summary>Gets the value telling if a sign in is required before a service can be used.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.UserPasswordCredentials.RequiresSignOut">
      <summary>Gets the value telling if a sign out should be called when the service goes out of scope.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.UserPasswordCredentials.SecurityHeader">
      <summary>Gets the security header.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.UserPasswordCredentials.ServerIdentities">
      <summary>Gets the server identity.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.UserPasswordCredentials.SupportsSignIn">
      <summary>Gets the value telling if the credentials can sign in.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.UserPasswordCredentials.SupportsSignOut">
      <summary>Gets the value telling if the credentials can sign out.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.UserPasswordCredentials.UserName">
      <summary>Gets the username.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.UserPasswordCredentials.VaultName">
      <summary>Gets the vault name.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServicesTools.WebServiceCredentials.#ctor(Autodesk.Connectivity.WebServices.IWebService)">
      <summary>The constructor.</summary>
      <param>A web service object. The service must already have security information from a prior sign-in.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceCredentials.RequiresSignIn">
      <summary>Gets the value telling if a sign in is required before a service can be used.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceCredentials.RequiresSignOut">
      <summary>Gets the value telling if a sign out should be called when the service goes out of scope.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceCredentials.SecurityHeader">
      <summary>Gets the security header.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceCredentials.ServerIdentities">
      <summary>Gets the server identity.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceCredentials.SupportsSignIn">
      <summary>Gets the value telling if the credentials can sign in.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceCredentials.SupportsSignOut">
      <summary>Gets the value telling if the credentials can sign out.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceCredentials.VaultName">
      <summary>Gets an empty string. The vault name information is not known in this case.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServicesTools.WebServiceErrorInfo.#ctor(System.Exception)">
      <summary>The constructor.</summary>
      <param>An exception thrown from a Web Service API call.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceErrorInfo.ErrorCode">
      <summary>Gets the error code from the Vault server.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceErrorInfo.Exception">
      <summary>Gets the original Exception.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceErrorInfo.HasRestrictions">
      <summary>Gets the value telling if the restriction data needs to be examined.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceErrorInfo.IsWebServiceError">
      <summary>Gets the value telling if the error originated from the Vault server.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServicesTools.WebServiceManager.#ctor">
      <summary>Constructor for Service Manager.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServicesTools.WebServiceManager.#ctor(Autodesk.Connectivity.WebServicesTools.IWebServiceCredentials)">
      <summary>Constructor for Service Manager.</summary>
      <param>The security credentials used to log in and/or set the security headers.</param>
    </member>
    <member name="M:Autodesk.Connectivity.WebServicesTools.WebServiceManager.Clone">
      <summary>Creates a duplicate WebServiceManager with its own service classes.</summary>
      <returns>A cloned WebServiceManager</returns>
    </member>
    <member name="M:Autodesk.Connectivity.WebServicesTools.WebServiceManager.Dispose">
      <summary>Used for IDisposable. Signs out of Vault and clears the security information on all services.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServicesTools.WebServiceManager.GetServerIdentities(System.String)">
      <summary>Calls the identification service to determine server identities</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceManager.AdminService">
      <summary>Gets the Admin service object, or creates one if needed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceManager.AuthService">
      <summary>Gets the auth service object, or creates one if needed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceManager.AutoTransferOwnership">
      <summary>Gets or sets the automatic ownership behavior on all the services in the WebServiceManager.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceManager.BehaviorService">
      <summary>Gets the Behavior service object, or creates one if needed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceManager.CategoryService">
      <summary>Gets the category service object, or creates one if needed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceManager.ChangeOrderService">
      <summary>Gets the change order service object, or creates one if needed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceManager.CustomEntityService">
      <summary>Gets the custom entity service object, or creates one if needed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceManager.DocumentService">
      <summary>Gets the Document service object, or creates one if needed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceManager.DocumentServiceExtensions">
      <summary>Gets the Document service object, or creates one if needed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceManager.FilestoreService">
      <summary>Gets the Filestore service object, or creates one if needed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceManager.FilestoreVaultService">
      <summary>Gets the FilestoreVault service object, or creates one if needed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceManager.ForumService">
      <summary>Gets the forum service object, or creates one if needed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceManager.InformationService">
      <summary>Gets the Information service object, or creates one if needed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceManager.ItemService">
      <summary>Gets the item service object, or creates one if needed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceManager.JobService">
      <summary>Gets the job service object, or creates one if needed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceManager.KnowledgeVaultService">
      <summary>Gets the Document service object, or creates one if needed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceManager.LifeCycleService">
      <summary>Gets the life cycle service object, or creates one if needed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceManager.PackageService">
      <summary>Gets the package service object, or creates one if needed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceManager.PropertyService">
      <summary>Gets the property service object, or creates one if needed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceManager.ReplicationService">
      <summary>Gets the replication service object, or creates one if needed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceManager.ReSignIn">
      <summary>Gets or sets the re-sign in behavior on all the services in the WebServiceManager.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceManager.RevisionService">
      <summary>Gets the revision service object, or creates one if needed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceManager.SecurityService">
      <summary>Gets the security service object, or creates one if needed.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceManager.WinAuthService">
      <summary>Gets the WinAuth service object, or creates one if needed.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServicesTools.WebServiceManager.Internal.CommonProxyInfo.ConnectionChanged(System.String)">
      <summary>If the connectionTicket is different from the previous one then this methods send out a notification to interested listeners.</summary>
      <param>The ticket that uniquely identifies the connection.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WebServiceManager.Internal.CommonProxyInfo.BeginCall">
      <summary>Give a way for the Explorer Framework call CommandManager.ErrorIfInOnIdle() at the beginning of every call.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServicesTools.WinAuthCredentials.#ctor">
      <summary>The Constructor.</summary>
    </member>
    <member name="M:Autodesk.Connectivity.WebServicesTools.WinAuthCredentials.#ctor(System.String,System.String,System.Boolean,System.Net.ICredentials)">
      <summary>The Constructor.</summary>
      <param>The file server name.</param>
      <param>The vault name. Enter null for cases that need global access.</param>
      <param>The read only setting. Default is false.</param>
      <param>The Windows credentials to use.</param>
    </member>
    <member name="M:Autodesk.Connectivity.WebServicesTools.WinAuthCredentials.#ctor(Autodesk.Connectivity.WebServices.ServerIdentities,System.String,System.Boolean,System.Net.ICredentials)">
      <summary>The Constructor.</summary>
      <param>The set of Vault servers.</param>
      <param>The vault name. Enter null for cases that need global access.</param>
      <param>The read only setting. Default is false.</param>
      <param>The Windows credentials to use.</param>
    </member>
    <member name="M:Autodesk.Connectivity.WebServicesTools.WinAuthCredentials.#ctor(System.String,System.String,System.Boolean)">
      <summary>The Constructor.</summary>
      <param>The file server name.</param>
      <param>The vault name. Enter null for cases that need global access.</param>
      <param>The read only setting. Default is false.</param>
    </member>
    <member name="M:Autodesk.Connectivity.WebServicesTools.WinAuthCredentials.#ctor(Autodesk.Connectivity.WebServices.ServerIdentities,System.String,System.Boolean)">
      <summary>The Constructor.</summary>
      <param>The set of Vault servers.</param>
      <param>The vault name. Enter null for cases that need global access.</param>
      <param>The read only setting. Default is false.</param>
    </member>
    <member name="M:Autodesk.Connectivity.WebServicesTools.WinAuthCredentials.#ctor(System.String,System.String)">
      <summary>The Constructor.</summary>
      <param>The file server name.</param>
      <param>The vault name. Enter null for cases that need global access.</param>
    </member>
    <member name="M:Autodesk.Connectivity.WebServicesTools.WinAuthCredentials.#ctor(Autodesk.Connectivity.WebServices.ServerIdentities,System.String)">
      <summary>The Constructor.</summary>
      <param>The set of Vault servers.</param>
      <param>The vault name. Enter null for cases that need global access.</param>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WinAuthCredentials.ReadOnly">
      <summary>Gets if the connection is read only</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WinAuthCredentials.RequiresSignIn">
      <summary>Gets the value telling if a sign in is required before a service can be used.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WinAuthCredentials.RequiresSignOut">
      <summary>Gets the value telling if a sign out should be called when the service goes out of scope.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WinAuthCredentials.SecurityHeader">
      <summary>Gets the security header.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WinAuthCredentials.ServerIdentities">
      <summary>Gets the server identity.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WinAuthCredentials.SupportsSignIn">
      <summary>Gets the value telling if the credentials can sign in.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WinAuthCredentials.SupportsSignOut">
      <summary>Gets the value telling if the credentials can sign out.</summary>
    </member>
    <member name="P:Autodesk.Connectivity.WebServicesTools.WinAuthCredentials.VaultName">
      <summary>Gets the vault name.</summary>
    </member>
  </members>
</doc>