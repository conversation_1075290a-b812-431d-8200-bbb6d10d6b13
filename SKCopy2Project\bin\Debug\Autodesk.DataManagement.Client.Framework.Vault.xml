﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Autodesk.DataManagement.Client.Framework.Vault</name>
  </assembly>
  <members>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Library">
      <summary>Provides a gateway to all features and services in Autodesk.DataManagement.Client.Framework.Vault</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Resource">
      <summary>A strongly-typed resource class, for looking up localized strings, etc.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource">
      <summary>A strongly-typed resource class, for looking up localized strings, etc.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Library.Initialize">
      <summary>Initializes the services in this assembly. This should be called on application startup.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Library.SetCulture(System.Globalization.CultureInfo)">
      <summary>Sets the culture that this assembly and its dependencies will use when returning resources.</summary>
      <param>The culture to set</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Library.ApplicationConfiguration">
      <summary>Gets the service which provides the ability to configure global characteristics for the Autodesk.DataManagement.Client.Framework.Vault Dll.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Library.ConnectionManager">
      <summary>Gets a service which encapsulates all connections to a vault server.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Library.EntityIDGenerator">
      <summary>Gets a service which is used to generate an Entity Master ID for
entities associated with programmatic <see cref="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClass">Entity Classes</see></summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Library.EntityOperationRegistration">
      <summary>Gets a service which allows you to register an  which can be used to
provide customized business logic for one or more entities. A custom provider should be registered if a new
<see cref="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClass" /> is introduced into the system.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Library.LocalFileLocation">
      <summary>Gets a service which provides location of files ranging from vault specific preferences, saved search locations, working folders, etc.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Library.PropertyExtensionRegistration">
      <summary>Gets a service which allows you to register a custom provider that can partipate in the vault property management pipeline. With this provider, you can
implement custom property definitions, create dependencies between existing property definitions, and provide custom property values for specific entities.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.AcquireProgressGatheringHeader">
      <summary>Looks up a localized string similar to Preparing Files.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.AppNameVault">
      <summary>Looks up a localized string similar to Autodesk Vault Basic.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.AppNameVaultCollab">
      <summary>Looks up a localized string similar to Autodesk Vault Collaboration.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.AppNameVaultMfg">
      <summary>Looks up a localized string similar to Autodesk Vault Professional.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.AppNameVaultWorkgroup">
      <summary>Looks up a localized string similar to Autodesk Vault Workgroup.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.Attachments_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.BlankImage_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CaseSensitivityConstraint_RestrictionMessage">
      <summary>Looks up a localized string similar to The text should be in '{0}'..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CaseSensitivityConstraintValue_Lower">
      <summary>Looks up a localized string similar to lower case.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CaseSensitivityConstraintValue_Name">
      <summary>Looks up a localized string similar to Name Case.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CaseSensitivityConstraintValue_None">
      <summary>Looks up a localized string similar to None.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CaseSensitivityConstraintValue_Sentence">
      <summary>Looks up a localized string similar to Sentence case.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CaseSensitivityConstraintValue_Upper">
      <summary>Looks up a localized string similar to UPPER CASE.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CCLibraryR_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CCLibraryRMask_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.ChangeOrder_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CheckOutFileRestrictionCheckedOutInAnotherDirectory">
      <summary>Looks up a localized string similar to File is already checked out to you in another directory: {0}.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CheckOutFileRestrictionCheckedOutOnAnotherMachine">
      <summary>Looks up a localized string similar to File is already checked out to you in another machine: {0}.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CheckOutFileRestrictionCheckedOutToSomeoneElsedOutTo">
      <summary>Looks up a localized string similar to File is checked out to: {0}.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CheckOutFileRestrictionNotLatestVersion">
      <summary>Looks up a localized string similar to File is not the latest version. Only the latest version can be checked out..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CheckoutProgressCaption">
      <summary>Looks up a localized string similar to Check Out.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CloakedData">
      <summary>Looks up a localized string similar to Inaccessible.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CO_128">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CO_32">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CO_64">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.ComputeLocalFileStatusProgressCaption">
      <summary>Looks up a localized string similar to Reading Local Files.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.ConfigFactory_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.ConfigMember_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesign_UpdatedFileCategory">
      <summary>Looks up a localized string similar to Category updated by Copy Design..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesign_UpdatedProperties">
      <summary>Looks up a localized string similar to Properties updated by Copy Design..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignCommentForCircularRefCheckin">
      <summary>Looks up a localized string similar to This might not be latest version of the file. Refer to History to get the correct version..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignDuplicateObjectInLocation">
      <summary>Looks up a localized string similar to Can not have two objects with the same name at '{0}'.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignFileAlreadyCheckedOutRestriction">
      <summary>Looks up a localized string similar to The file {0} is already checked out..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignFileDoesNotExistInVaultRestriction">
      <summary>Looks up a localized string similar to Source file '{0}' does not exist in the vault..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignFileExistsInVaultRestriction">
      <summary>Looks up a localized string similar to A file already exists in the vault at '{0}'.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignFileRetrievalRestriction">
      <summary>Looks up a localized string similar to Problem accessing the file '{0}'.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignFolderCreation">
      <summary>Looks up a localized string similar to Folder Creation.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.copyDesignFolderRestrictionMessage">
      <summary>Looks up a localized string similar to Some folder(s) could not be created in vault.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignMissingDestinationLocation">
      <summary>Looks up a localized string similar to Missing destination location.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.copyDesignProgressBarAnalyzingData">
      <summary>Looks up a localized string similar to Analyzing Data.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.copyDesignProgressBarCopyingFiles">
      <summary>Looks up a localized string similar to Copying Files.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.copyDesignProgressBarCreatingFolders">
      <summary>Looks up a localized string similar to Creating Folders.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignPropertyBehaviorAction_MappedTo">
      <summary>Looks up a localized string similar to Mapped to &lt;{0}&gt;.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignPropertyBehaviorAction_Reset">
      <summary>Looks up a localized string similar to Reset.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignPropertyBehaviorAction_SetBlank">
      <summary>Looks up a localized string similar to Set Blank.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignPropertyBehaviorAction_SetValueAs">
      <summary>Looks up a localized string similar to Set Value As .</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignPropertyValue_FileName">
      <summary>Looks up a localized string similar to File Name.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignPropertyValue_OrigFileName">
      <summary>Looks up a localized string similar to Original File Name.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignPropertyValue_RemoveILogicRules">
      <summary>Looks up a localized string similar to Remove iLogic Rules.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignPropertyValue_User">
      <summary>Looks up a localized string similar to User.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignRuleConditionContainsEntry">
      <summary>Looks up a localized string similar to contains.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignRuleConditionDoesNotContainEntry">
      <summary>Looks up a localized string similar to does not contain.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignRuleConditionDoesNotEndWith">
      <summary>Looks up a localized string similar to does not end with.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignRuleConditionDoesNotStartWith">
      <summary>Looks up a localized string similar to does not start with.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignRuleConditionEndsWith">
      <summary>Looks up a localized string similar to ends with.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignRuleConditionFalse">
      <summary>Looks up a localized string similar to is false.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignRuleConditionGreaterThanEntry">
      <summary>Looks up a localized string similar to &gt;.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignRuleConditionGreaterThanOrEqualEntry">
      <summary>Looks up a localized string similar to &gt;=.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignRuleConditionIsEmptyEntry">
      <summary>Looks up a localized string similar to is empty.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignRuleConditionIsExactlyEntry">
      <summary>Looks up a localized string similar to is.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignRuleConditionIsNotEmptyEntry">
      <summary>Looks up a localized string similar to is not empty.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignRuleConditionLastWeekEntry">
      <summary>Looks up a localized string similar to Last Week.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignRuleConditionLessThanEntry">
      <summary>Looks up a localized string similar to &lt;.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignRuleConditionLessThanOrEqualEntry">
      <summary>Looks up a localized string similar to &lt;=.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignRuleConditionNextWeekEntry">
      <summary>Looks up a localized string similar to Next Week.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignRuleConditionNotEqualEntry">
      <summary>Looks up a localized string similar to is not.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignRuleConditionStartsWith">
      <summary>Looks up a localized string similar to starts with.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignRuleConditionThisWeekEntry">
      <summary>Looks up a localized string similar to This Week.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignRuleConditionTodayEntry">
      <summary>Looks up a localized string similar to Today.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignRuleConditionTomorrowEntry">
      <summary>Looks up a localized string similar to Tomorrow.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignRuleConditionTrue">
      <summary>Looks up a localized string similar to is true.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignRuleConditionYesterdayEntry">
      <summary>Looks up a localized string similar to Yesterday.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyDesignTitle">
      <summary>Looks up a localized string similar to Copy Design.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.CopyFileComment">
      <summary>Looks up a localized string similar to Copy of file '{0}' version '{1}'. ({2}).</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.Cross_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.Culture">
      <summary>Overrides the current thread's CurrentUICulture property for all resource lookups using this strongly typed resource class.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.dateOnlyCustomProperty">
      <summary>Looks up a localized string similar to {0} (Date Only).</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.DefaultCustomEntityIcon">
      <summary>Looks up a localized resource of type System.Drawing.Icon similar to (Icon).</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.DeleteRestrictReason_Assigned">
      <summary>Looks up a localized string similar to Assigned to one or more categories.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.DeleteRestrictReason_Default">
      <summary>Looks up a localized string similar to Assigned as default.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.DeleteRestrictReason_Used">
      <summary>Looks up a localized string similar to In use by active objects.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.DMServicesNotFound">
      <summary>Looks up a localized string similar to {0} could not find the Data Management Services on {1}..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.DocumentIcon_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.DownloadFileCheckedOutInSharedLocationRestriction">
      <summary>Looks up a localized string similar to File '{0}' is checked out to another user. You cannot overwrite a file that is checked out to somebody else while in a
shared working folder..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.DownloadFileLockedRestriction">
      <summary>Looks up a localized string similar to '{0}' is in use by another process and cannot be accessed..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.DownloadingAndCheckoutProgressCaption">
      <summary>Looks up a localized string similar to Downloading and Checking Out.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.DownloadingProgressCaption">
      <summary>Looks up a localized string similar to Downloading.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.EditExactLengthError">
      <summary>Looks up a localized string similar to Value '{0}' is invalid. It must be exactly {1} character(s) in length.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.EditFileComment">
      <summary>Looks up a localized string similar to Edited by Copy Design..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.EditMaxLengthError">
      <summary>Looks up a localized string similar to Value '{0}' is invalid. It cannot contain more than {1} character(s).</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.EditMinLengthError">
      <summary>Looks up a localized string similar to Value '{0}' is invalid. It must be at least {1} character(s).</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.EditNotInPredefinedList">
      <summary>Looks up a localized string similar to Value '{0}' is invalid.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.EmptyItemThumbnail">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.EmptyThumbnail">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.entityDescriptionChangeOrder">
      <summary>Looks up a localized string similar to Change Order.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.entityDescriptionFolder">
      <summary>Looks up a localized string similar to Folder.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.entityDescriptionItem">
      <summary>Looks up a localized string similar to Item.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.entityDescriptionLibraryFolder">
      <summary>Looks up a localized string similar to Library Folder.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.File_Store_128">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.File_Store_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.FileClassSubstitute_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.FolderClosed_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.FolderClosedLocked_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.FolderClosedLockedMask_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.FolderClosedMask_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.FutureFolder_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.GeneralExceptionDialogTitle">
      <summary>Looks up a localized string similar to Error.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.GeneralExceptionWithErrorCodeDialogTitle">
      <summary>Looks up a localized string similar to Error {0}.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.imageDescriptionCategoryPostfix">
      <summary>Looks up a localized string similar to ({0}).</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.IncompatibleServerMissingRequiredApps">
      <summary>Looks up a localized string similar to {0} {1} is missing the following required applications from {2} .</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.IncompatibleServerNoVersion">
      <summary>Looks up a localized string similar to {0} ({1}) is not compatible with the Data Management Server found on {2}..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.IncompatibleServerWithIncompatibleApps">
      <summary>Looks up a localized string similar to {0} {1} is not compatible with the following applications on {2} .</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.IncompatibleServerWithVersion">
      <summary>Looks up a localized string similar to {0} {1} is not compatible with the following applications on {2}:.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.InvalidDirectoryPathException">
      <summary>Looks up a localized string similar to Invalid directory path.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.Item_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.item_32">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.Item_64">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.LargeFolder128">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.LargeFolderLocked128">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.LargeFolderLockedMask128">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.LargeFolderMask128">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.LargeLibraryFolder128">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.LargeLibraryFolderLocked128">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.LargeLibraryFolderMask128">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.Library_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.LibraryMask_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.LifecycleStateLabel">
      <summary>Looks up a localized string similar to State:.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.ListOfValuesConstraint_RestrictionMessage">
      <summary>Looks up a localized string similar to The value is not in the list of possible values..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.LoginStateAuthenticating">
      <summary>Looks up a localized string similar to Authenticating with server....</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.LoginStateConnectivity">
      <summary>Looks up a localized string similar to Testing server connectivity....</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.LoginStateSucessful">
      <summary>Looks up a localized string similar to Login successful..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.LoginStateValidating">
      <summary>Looks up a localized string similar to Validating user information....</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.MaximumDateConstraint_RestrictionMessage">
      <summary>Looks up a localized string similar to Latest date allowed is '{0}'..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.MaximumLengthConstraint_RestrictionMessage">
      <summary>Looks up a localized string similar to Maximum number of characters allowed is '{0}'..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.MaximumValueConstraint_RestrictionMessage">
      <summary>Looks up a localized string similar to Maximum value allowed is '{0}'..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.MinimumDateConstraint_RestrictionMessage">
      <summary>Looks up a localized string similar to Earliest date allowed is '{0}'..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.MinimumLengthConstraint_RestrictionMessage">
      <summary>Looks up a localized string similar to Minimum number of characters allowed is '{0}'..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.MinimumValueConstraint_RestrictionMessage">
      <summary>Looks up a localized string similar to Minimum value allowed is '{0}'..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.MissingFieldError">
      <summary>Looks up a localized string similar to Field must be filled in..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.MyDocuments">
      <summary>Looks up a localized string similar to My Documents.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.notLoggedIn">
      <summary>Looks up a localized string similar to Your account is logged out of the Autodesk data managment server. You must log in to access the vault..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.NullRevisionDisplayName">
      <summary>Looks up a localized string similar to (Null).</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.ProductNameAndVersion">
      <summary>Looks up a localized string similar to {0} {1}.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.propDefAttachmentsDisplayName">
      <summary>Looks up a localized string similar to Attachments.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.propDefEntityDescriptionDisplayName">
      <summary>Looks up a localized string similar to Type Description.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.propDefEntityIconDisplayName">
      <summary>Looks up a localized string similar to Entity Icon.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.propDefEntityPathDisplayName">
      <summary>Looks up a localized string similar to Path.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.propDefEntityTypeDisplayName">
      <summary>Looks up a localized string similar to Entity Type.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.propDefEntityTypeIDDisplayName">
      <summary>Looks up a localized string similar to Entity Type ID.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.propDefFolderCreateDateDisplayName">
      <summary>Looks up a localized string similar to Folder Create Date.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.propDefFullPathDisplayName">
      <summary>Looks up a localized string similar to Full Path.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.propDefLinkPathDisplayName">
      <summary>Looks up a localized string similar to Link Target Path.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.propDefVaultStatusDisplayName">
      <summary>Looks up a localized string similar to Vault Status.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.propDefVaultStatusModifierDisplayName">
      <summary>Looks up a localized string similar to Vault Status Modifier.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.PropertyCompliant">
      <summary>Looks up a localized string similar to Compliant.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.PropertyCompliantError">
      <summary>Looks up a localized string similar to Compliance evaluation failed.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.PropertyCompliantNotCalculated">
      <summary>Looks up a localized string similar to Not Calculated.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.PropertyComplientPending">
      <summary>Looks up a localized string similar to Pending.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.PropertyNonCompliantFormat">
      <summary>Looks up a localized string similar to Noncompliant policies.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.PropertyNonCompliantWriteBack">
      <summary>Looks up a localized string similar to Noncompliant equivalence.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.PropertyNonCompliantWriteBackAndFormat">
      <summary>Looks up a localized string similar to Noncompliant policies and equivalence.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.RequiresValueConstraint_RestrictionMessage">
      <summary>Looks up a localized string similar to This property requires a value..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.ResourceManager">
      <summary>Returns the cached ResourceManager instance used by this class.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.RevisionLabel">
      <summary>Looks up a localized string similar to Revision:.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.ServerNotFoundErrorText">
      <summary>Looks up a localized string similar to Server could not be found..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.SignInExceptionUsePSAuth">
      <summary>Looks up a localized string similar to The user name appears to be a Vault user and not a Vault Domain user; please turn off the Windows Authentication option
and try again..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.SignInExceptionUseWinAuth">
      <summary>Looks up a localized string similar to The user name appears to be a Vault Domain user and not a Vault user; please turn on the Windows Authentication option
and try again..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.SignInInvalidDatabaseName">
      <summary>Looks up a localized string similar to The database name appears to be invalid; please try again..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.SignInInvalidServerName">
      <summary>Looks up a localized string similar to The server name appears to be invalid; please try again..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.SignInWinAuthNotSupported">
      <summary>Looks up a localized string similar to Windows Authentication is not supported by this version of the Autodesk data management server..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.Tick_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.timeOnlyCustomProperty">
      <summary>Looks up a localized string similar to {0} (Time Only).</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.TotalFilesProgressLabel">
      <summary>Looks up a localized string similar to Total Files:.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.TotalSizeProgressLabel">
      <summary>Looks up a localized string similar to Total Size:.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.UpgradeExceptionDatabaseMigrationRequired">
      <summary>Looks up a localized string similar to This vault must be migrated. Logging in to the server console on the server will automatically complete the migration..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.UpgradeExceptionVersionLabel">
      <summary>Looks up a localized string similar to This server is incompatible with the database. This server must be upgraded to version {0} and is currently version {1}.
.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.UpgradeExceptionVersionLabelSimple">
      <summary>Looks up a localized string similar to This server is incompatible with the database. The server version is {0} and must be upgraded..</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.visualizationCompliantLegacy">
      <summary>Looks up a localized string similar to Legacy.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.visualizationCompliantNone">
      <summary>Looks up a localized string similar to None.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.visualizationCompliantNotSynchronized">
      <summary>Looks up a localized string similar to Not Synchronized.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.visualizationCompliantSynchronized">
      <summary>Looks up a localized string similar to Synchronized.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Resource.visualizationCompliantUserVerified">
      <summary>Looks up a localized string similar to User Verified.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.Add_Large">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.Add_small">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.changeOrderVaultStatusGoldLock">
      <summary>Looks up a localized string similar to Local, Read Only.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.changeOrderVaultStatusSilverLock">
      <summary>Looks up a localized string similar to Not Local, Read Only.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.Culture">
      <summary>Overrides the current thread's CurrentUICulture property for all resource lookups using this strongly typed resource class.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.Empty">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.fileStatusCheckedOut">
      <summary>Looks up a localized string similar to {0} Checked out.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.fileStatusCheckedOutDesc">
      <summary>Looks up a localized string similar to Checked out.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.fileStatusCouldNotCalculateChecksum">
      <summary>Looks up a localized string similar to Error: Unable to identify local file.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.fileStatusCouldNotCalculateChecksumDesc">
      <summary>Looks up a localized string similar to Error: Unable to identify local file.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.fileStatusEditedOutOfTurn">
      <summary>Looks up a localized string similar to {0} Edited out of turn.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.fileStatusEditedOutOfTurnDesc">
      <summary>Looks up a localized string similar to Edited out of turn.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.fileStatusNewerEditsAvailable">
      <summary>Looks up a localized string similar to {0} Incorrect version Refresh the file.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.fileStatusNewerEditsAvailableDesc">
      <summary>Looks up a localized string similar to Incorrect version Refresh the file.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.fileStatusNewerEditsAvailableWithFileNameStateAndRev">
      <summary>Looks up a localized string similar to {0} State: {2} Revision: {3} Incorrect version Refresh the file.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.fileStatusNewerNonReleasedAvailableDesc">
      <summary>Looks up a localized string similar to Newer non-released changes available.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.fileStatusNewerNonReleasedAvailableWithFileNameStateAndRev">
      <summary>Looks up a localized string similar to {0} State: {2} Revision: {3} Newer non-released changes available.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.fileStatusNoLocalFile">
      <summary>Looks up a localized string similar to No local file.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.fileStatusNoLocalFileDesc">
      <summary>Looks up a localized string similar to No local file.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.fileStatusNone">
      <summary>Looks up a localized string similar to None.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.fileStatusNone1">
      <summary>Looks up a localized string similar to None.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.fileStatusNormalWithFileNameStateAndRev">
      <summary>Looks up a localized string similar to {0} State: {2} Revision: {3}.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.fileStatusNotCheckedOut">
      <summary>Looks up a localized string similar to {0}.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.fileStatusNotCheckedOutDesc">
      <summary>Looks up a localized string similar to Available for check out.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.fileStatusNoWorkingFolder">
      <summary>Looks up a localized string similar to Error: You have not set a working folder.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.fileStatusNoWorkingFolderDesc">
      <summary>Looks up a localized string similar to Error: You have not set a working folder.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.fileStatusOnDiskButNotInVault">
      <summary>Looks up a localized string similar to On disk but not in Vault.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.fileStatusOnDiskButNotInVaultDesc">
      <summary>Looks up a localized string similar to On disk but not in Vault.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.fileStatusPathTooLong">
      <summary>Looks up a localized string similar to Error: File path is too long.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.fileStatusPathTooLongDesc">
      <summary>Looks up a localized string similar to Error: File path is too long.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.fileStatusVersionNotFound">
      <summary>Looks up a localized string similar to Unknown Status.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.fileStatusVersionNotFoundDesc">
      <summary>Looks up a localized string similar to Unknown Status.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.Green_Circle_Checked_Large">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.Green_Circle_Checked_Small">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.Green_Circle_Large">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.Green_Circle_Small">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.itemVaultStatusGoldLock">
      <summary>Looks up a localized string similar to Local, Read Only.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.itemVaultStatusSilverLock">
      <summary>Looks up a localized string similar to Not Local, Read Only.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.propertyLockedTooltip">
      <summary>Looks up a localized string similar to Locked.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.Red_Refresh_Large">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.Red_Refresh_Small">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.ResourceManager">
      <summary>Returns the cached ResourceManager instance used by this class.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.revisionNone">
      <summary>Looks up a localized string similar to None.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.VaultStatusCheckedOutCurrent_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.VaultStatusCheckedOutCurrent_Bigger_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.VaultStatusCheckedOutMissing_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.VaultStatusCheckedOutMissing_Bigger_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.VaultStatusCheckedOutNew_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.VaultStatusCheckedOutNew_Bigger_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.VaultStatusCheckedOutOld_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.VaultStatusCheckedOutOld_Bigger_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.VaultStatusLocked_Missing_Bigger_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.VaultStatusLocked_Missing_Smaller_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.VaultStatusLockedBigger_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.VaultStatusLockedSmaller_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.vaultStatusModifierLocalFileModifiedTooltip">
      <summary>Looks up a localized string similar to Local file modified.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.VaultStatusMyCheckOutCurrent_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.VaultStatusMyCheckOutCurrent_Bigger_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.VaultStatusMyCheckOutMissing_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.VaultStatusMyCheckOutMissing_Bigger_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.VaultStatusMyCheckOutNew_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.VaultStatusMyCheckOutNew_Bigger_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.VaultStatusMyCheckOutOld_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.VaultStatusMyCheckOutOld_Bigger_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.VaultStatusNotCheckedOutCurrent_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.VaultStatusNotCheckedOutCurrent_Bigger_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.VaultStatusNotCheckedOutMissing_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.VaultStatusNotCheckedOutMissing_Bigger_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.VaultStatusNotCheckedOutNew_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.VaultStatusNotCheckedOutNew_Bigger_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.VaultStatusNotCheckedOutOld_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.VaultStatusNotCheckedOutOld_Bigger_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.vaultStatusUnknown">
      <summary>Looks up a localized string similar to Vault status is unknown.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.VaultStatusUnknown_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.VaultStatusUnknown_Bigger_16">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.White_Circle_Checked_Large">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.White_Circle_Checked_Small">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.White_Circle_Large">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.White_Circle_Small">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.White_Half_Circle_Large">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.White_Half_Circle_Small">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.White_Triangle_Large">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.White_Triangle_Small">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.Yellow_Circle_Info_Large">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.VaultStatusResource.Yellow_Circle_Info_small">
      <summary>Looks up a localized resource of type System.Drawing.Bitmap.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.IFileAcquisitionOperation">
      <summary>Acquisition information for a single file</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.IFileAcquisitionOperation.AcquisitionOption">
      <summary>The acquisition option (download and/or checkout) for the file</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.IFileAcquisitionOperation.File">
      <summary>The file iteration to acquire</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.IFileAcquisitionOperation.LocalPath">
      <summary>The download path for the file</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.VersionGatheringOption">
      <summary>Specifies the algorithm for choosing a version when gathering relationships.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.AuthenticationFlags">
      <summary>Identifies a type of connection</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.CachedObjects">
      <summary>The types of Caches in a Vault Connection</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.CommonPreferenceVersionRequirements">
      <summary>Used to indicate the type of path returned by GetVaultCommonConnectionPath</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.LoginStates">
      <summary>The various states of the Log In workflow. This states are used to report the progress of a Log In.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.VaultProduct.Requirements">
      <summary>Describes the clients requirements for this Vault Product</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection">
      <summary>Represents a connection to a vault server.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.ConnectionEventArgs">
      <summary>Event argument for connection related events (ie. ConnectionEstablished, ConnectionReleased)</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.VaultProduct">
      <summary>Represents a product installed on the Vault Server and the clients requirements of that product.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.VaultProductRequirements">
      <summary>The class represents the clients requirements on server capabilities. A log in will fail if it doesn't meet the requirements configured by this object.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection.#ctor(Autodesk.Connectivity.WebServicesTools.WebServiceManager,System.String,System.Int64,System.String,Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.AuthenticationFlags)">
      <summary>Creates a Connection object that doesn't contain a userName and userPassword.</summary>
      <param>The low level connection to the vault server.</param>
      <param>The name of the vault that the <paramref name="mgr" /> is connected to.</param>
      <param>The id of the user that has logged in.</param>
      <param>The name of the server that the <paramref name="mgr" /> is connected to.</param>
      <param>Identifies the type of connection to the server.</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection.#ctor(Autodesk.Connectivity.WebServicesTools.WebServiceManager,System.String,System.String,System.String,System.Int64,System.String,Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.AuthenticationFlags)">
      <summary>Creates a Connection object.</summary>
      <param>The low level connection to the vault server.</param>
      <param>The name of the user that has logged in. This is used so that the framework can Re-login if the connection times out.</param>
      <param>The password of the user that has logged in. This is used so that the framework can Re-login if the connection times out.</param>
      <param>The name of the vault that the <paramref name="mgr" /> is connected to.</param>
      <param>The id of the user that has logged in.</param>
      <param>The name of the server that the <paramref name="mgr" /> is connected to.</param>
      <param>Identifies the type of connection to the server.</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection.ClearCache(Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.CachedObjects)">
      <summary>Clears the specified cached data from the connection</summary>
      <param>The cached objects to clear.</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection.Equals(System.Object)">
      <summary>Tests if <paramref name="obj" /> is equal to this connection object.</summary>
      <param>The object to test for equality.</param>
      <returns>True if <paramref name="obj" /> is equal to this object.</returns>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection.CategoryManager">
      <summary>Gets an object which encapsulates all access to Vault Categories</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection.ChangeOrderManager">
      <summary>Gets an object which encapsulates all access to Vault Change Orders</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection.ConfigurationManager">
      <summary>Gets an object which manages configuration data on the vault server.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection.CustomObjectManager">
      <summary>Gets an object which encapsulates all access to Vault Custom Objects</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection.EntityOperations">
      <summary>Gets an object which provides a set of workflows that can be applied to any entity object.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection.FileManager">
      <summary>Gets an object which encapsulates all access to Vault Files</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection.FolderManager">
      <summary>Gets an object which encapsulates all access to Vault Folders</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection.IdentityKey">
      <summary>Gets a string which uniquely identifies this connection.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection.IsAnonymousConnection">
      <summary>Gets a value which identifies whether or not we have an anonymous connection to the server.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection.IsConnected">
      <summary>Gets a values which tells if this connection object has an active connection. If a logout occurs, then the connection will essentially be invalid.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection.IsReadOnly">
      <summary>Gets a value which identifies whether or not this is a read only connection to the server</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection.IsServerOnlyConnection">
      <summary>Gets a value which identifies whether or not we have a connection to a server but not to a specific vault.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection.IsWindowsAuthenticatedConnection">
      <summary>Gets a value which identifies whether or not the connection to the server used windows authentication.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection.ItemManager">
      <summary>Gets an object which encapsulates all access to Vault Items</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection.LinkManager">
      <summary>Gets an object which encapsulates all access to Links</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection.PersistableIdManager">
      <summary>Gets an object which encapsulates all interaction with persistable ids</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection.PropertyManager">
      <summary>Gets an object which encapsulates all access to vault property definitions and property values.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection.Server">
      <summary>Gets the name of the server that we are connected to.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection.Ticket">
      <summary>Gets an encrypted ticket that represents the unique connection to the server.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection.UserID">
      <summary>Gets the unique identity of the authenticated user on the vault server.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection.UserName">
      <summary>Gets the name of the authenticated user.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection.Vault">
      <summary>Gets the name of the vault that we are connected to.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection.WebServiceManager">
      <summary>Gets the low level Web Service Manager which stores the underlying physical connection to the server. This can be used to make direct calls to the web service
layer for any functionality that is not provided by the Framework.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection.WorkingFoldersManager">
      <summary>Gets an object which encapsulates all access to a vaults working folders</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.ConnectionEventArgs.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection)">
      <summary>Creates a ConnectionEventArgs object</summary>
      <param>The connection object that the event is related to.</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.ConnectionEventArgs.Connection">
      <summary>Gets the Connection object related to this event.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.VaultProduct.ID">
      <summary>Gets the server-based ID of this product</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.VaultProduct.Name">
      <summary>Gets the display name for this product.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.VaultProduct.Requirement">
      <summary>Gets or sets the client requirements for this product.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.VaultProductRequirements.#ctor">
      <summary>Creates a VaultProductRequirements object.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.VaultProductRequirements.Collaboration">
      <summary>Gets the requirements for the Vault Collaboration Product.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.VaultProductRequirements.Professional">
      <summary>Gets the requirements for the Vault Professional Product.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.VaultProductRequirements.Vault">
      <summary>Gets the requirements for the Vault Product.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.VaultProductRequirements.Workgroup">
      <summary>Gets the requirements for the Vault Workgroup Product.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity">
      <summary>Represents an entity in the vault data base.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntityImageProvider">
      <summary>An interface implemented by entities that can optionally provide an associated image</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity.EntityClass">
      <summary>Gets the entity class associated with this object.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity.EntityIterationId">
      <summary>Gets the unique id for the iteration. If the object only supports masters, then this and the
 should be the same</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity.EntityMasterId">
      <summary>Gets the the database id for the master record for this entity.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity.EntityName">
      <summary>Gets a display name that can be used to describe this entity</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity.IsCloaked">
      <summary>Gets whether or not this entity is cloaked (ie. the user can know about the existence of an object but not the details behind that object).</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity.LinkInfo">
      <summary>Gets if this entity isn't a real entity, but instead if is a link to the entity. If so, then this value is non-null and returns information about the link to
this object.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntityImageProvider.GetImage(Autodesk.DataManagement.Client.Framework.Currency.ShellIconSize)">
      <summary>Gets an image for the entity</summary>
      <param>The size of the image that is requested</param>
      <returns>An object containing information about the resulting image</returns>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ChangeOrder">
      <summary>A Change Order entity.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.CustomObject">
      <summary>A Custom Object entity.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.CustomObjectDefinition">
      <summary>A classification of a . Each CustomObject has an underlying definition that
describes its attributes and capabilities.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory">
      <summary>The EntityCategory represents a category that can be associated with entity objects. Entities such as Files, Folders, and Items all support categories.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClass">
      <summary>An EntityClass represents information about a type of object that can be manipulated by the framework. The properties of the EntityClass provide information
about the capabilities of that entity class.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClassIds">
      <summary>A list of constants that represent well known Entity Class IDS</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityRelationship">
      <summary>An object that describes a parent-child relationship between two entities</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityRevision">
      <summary>Describes revision information about an Entity</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration">
      <summary>Represents a specific iteration of a file in the vault.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileLifecycleInfo">
      <summary>Contains information about the Life Cycle of a file.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileRelationship">
      <summary>An object that describes a parent-child relationship between two files</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileRevisionInfo">
      <summary>Contains information about the revision of a file.  will be
true if the installed product does not support file revisions.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder">
      <summary>Represents information about a folder in the vault.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ItemRevision">
      <summary>Represents a version of an Item Revision in the vault.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Link">
      <summary>A link is an object that points to another . A link typically lives in a
<see cref="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder" />, but other objects could also be link hosts.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.PropertyDefinitionToEntityClassAssociation">
      <summary>This class represents an association between an entity class and a propdef.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ChangeOrder.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection,Autodesk.Connectivity.WebServices.ChangeOrder)">
      <summary>Creates a Change Order entity object</summary>
      <param>A connection to a vault that this ChangeOrder belongs to.</param>
      <param>A low level ChangeOrder object as returned by the Web Services.</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ChangeOrder.Clone">
      <summary>Creates a clone of this Change Order object.</summary>
      <returns>A clone of this Change Order object.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ChangeOrder.Equals(System.Object)">
      <summary>Tests if <paramref name="obj" /> is equal to this Change Order object.</summary>
      <param>The object to test for equality.</param>
      <returns>True if <paramref name="obj" /> is equal to this object.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ChangeOrder.op_Implicit(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ChangeOrder)~Autodesk.Connectivity.WebServices.ChangeOrder">
      <summary>Casts this ChangeOrder object to a Web Services Change Order.</summary>
      <param>The ChangeOrder to cast</param>
      <returns>The Web Services representation of <paramref name="changeOrder" /></returns>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ChangeOrder.ApprovalDeadline">
      <summary>Gets or sets the date and time when the Change Order needs to be approved by.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ChangeOrder.CreatedByUserId">
      <summary>Gets the ID of the user that created the Change Order</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ChangeOrder.CreatedByUserName">
      <summary>Gets the name of the user that created the Change Order</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ChangeOrder.CreatedDate">
      <summary>Gets the date and time the Change Order was created.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ChangeOrder.Description">
      <summary>Gets or sets the description of the Change Order.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ChangeOrder.EntityClass">
      <summary>Gets the entity class associated with this Change Order.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ChangeOrder.EntityIterationId">
      <summary>Gets the Iteration ID of this Change Order. Since Change Orders are not iteration based, this will always have the same value as the EntityMasterId.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ChangeOrder.EntityMasterId">
      <summary>Gets the Master Id of this Change Order. The Master Id uniquely identifies this Change Order object</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ChangeOrder.EntityName">
      <summary>Gets the descriptive name for this Change Order. The Change Order Number will always be returned for the EntityName.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ChangeOrder.IsCloaked">
      <summary>Gets if this Change Order is cloaked. A cloaked object is one that the caller does not have permissions to view.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ChangeOrder.IsLocked">
      <summary>Gets if the Change Order is currently locked.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ChangeOrder.LastModDate">
      <summary>Gets the last time that the Change Order was modified.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ChangeOrder.LinkInfo">
      <summary>Gets if this is a Link to a Change Order. If the value is not null, then LinkInfo provides information about the Link to this Change Order</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ChangeOrder.Number">
      <summary>Gets or sets the Change Order number. Numbers are generated based on the Numbering Scheme.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ChangeOrder.StateDisplayName">
      <summary>Gets the display name of the Change Order State.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ChangeOrder.StateEnteredDate">
      <summary>Gets the date and time that the change order state was set.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ChangeOrder.StateId">
      <summary>Gets the ID of the Change Order State.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ChangeOrder.StateName">
      <summary>Gets the name of the Change Order State.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ChangeOrder.SubmitEnteredDate">
      <summary>Gets the date and time that the Change Order was last submitted. This value has no meaning if the Change Order has not been submitted and the SubmittedByUserID
is -1.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ChangeOrder.SubmittedByUserId">
      <summary>Gets the ID of the user who submitted the Change Order. This value will be -1 if the Change Order has not been submitted.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ChangeOrder.SubmittedByUserName">
      <summary>Gets the name of the user who last submitted the Change Order. This value has no meaning if the Change Order has not been submitted.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ChangeOrder.Title">
      <summary>Gets or sets the title of the Change Order.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ChangeOrder.VaultConnection">
      <summary>Gets the Vault Connection that this ChangeOrder is associated with.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.CustomObject.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection,Autodesk.Connectivity.WebServices.CustEnt)">
      <summary>Creates a Change Order entity object</summary>
      <param>A connection to a vault that this ChangeOrder belongs to.</param>
      <param>A low level ChangeOrder object as returned by the Web Services.</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.CustomObject.Clone">
      <summary>Creates a clone of this Change Order object.</summary>
      <returns>A clone of this Change Order object.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.CustomObject.Equals(System.Object)">
      <summary>Tests if <paramref name="obj" /> is equal to this Change Order object.</summary>
      <param>The object to test for equality.</param>
      <returns>True if <paramref name="obj" /> is equal to this object.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.CustomObject.op_Implicit(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.CustomObject)~Autodesk.Connectivity.WebServices.CustEnt">
      <summary>Casts this CustomObject object to a Web Services Custom Object.</summary>
      <param>The CustomObject to cast</param>
      <returns>The Web Services representation of <paramref name="customObject" /></returns>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.CustomObject.Category">
      <summary>Gets the category that is assigned to this Custom Object</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.CustomObject.Definition">
      <summary>Gets the Custom Object Definition which contains specifics about the type of custom object this is</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.CustomObject.EntityClass">
      <summary>Gets the entity class associated with this CustomObject.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.CustomObject.EntityIterationId">
      <summary>Gets the Iteration ID of this Change Order. Since Change Orders are not iteration based, this will always have the same value as the EntityMasterId.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.CustomObject.EntityMasterId">
      <summary>Gets the Master Id of this Change Order. The Master Id uniquely identifies this Change Order object</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.CustomObject.EntityName">
      <summary>Gets the descriptive name for this Custom Object. .</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.CustomObject.IsCloaked">
      <summary>Gets if this Custom Object is cloaked. A cloaked object is one that the caller does not have permissions to view.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.CustomObject.LinkInfo">
      <summary>Gets if this is a Link to a Custom Object. If the value is not null, then LinkInfo provides information about the Link to this CustomObject</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.CustomObject.VaultConnection">
      <summary>Gets the Vault Connection that this ChangeOrder is associated with.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.CustomObjectDefinition.#ctor(System.Int64,System.String,System.String,System.String)">
      <summary>Creates an instance of a CustomObjectDefinition object</summary>
      <param>The unique identifier of the CustomObjectDefinition</param>
      <param>The system name of the CustomObjectDefinition</param>
      <param>The display name of the CustomObjectDefinition</param>
      <param>The plural display name of the CustomObjectDefinition</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.CustomObjectDefinition.GetImage(Autodesk.DataManagement.Client.Framework.Currency.ShellIconSize)">
      <summary>Gets an image associated with the CustomObjectDefinition</summary>
      <param>The size of the image to return</param>
      <returns>An image of the specified size - or null if there is not an Icon configured for this CustomObjectDefinition</returns>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.CustomObjectDefinition.DisplayName">
      <summary>Gets or Sets the display name of the CustomObjectDefinition</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.CustomObjectDefinition.Icon">
      <summary>Gets or Sets an icon associated with the CustomObjectDefinition Returns the icon or returns null if there is not an Icon configured for this
CustomObjectDefinition</summary>
      <returns>Returns the icon or returns null if there is not an Icon configured for this CustomObjectDefinition</returns>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.CustomObjectDefinition.Id">
      <summary>Gets the unique identifier of the CustomObjectDefinition</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.CustomObjectDefinition.PluralDisplayName">
      <summary>Gets or Sets the plural display name of the CustomObjectDefinition</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.CustomObjectDefinition.SystemName">
      <summary>Gets the system name of the CustomObjectDefinition</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory.#ctor(System.Int64,System.Drawing.Color,System.String,System.String,System.String,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClass,System.Boolean,System.Boolean)">
      <summary>Creates an EntityCategory object</summary>
      <param>The id that uniquely identifies this category object.</param>
      <param>The System.Drawing.Color representation of the RGB color associated with this category</param>
      <param>The unique system name associated with this category</param>
      <param>The display name for this category</param>
      <param>The optional description associated with this category</param>
      <param>The Entity Class that this category is associated with. A category can not be shared across multiple entity classes.</param>
      <param>True if this is the default category for it's entity class. False otherwise.</param>
      <param>True if this category is enabled. False otherwise. Disabled categories will generally not be available for user selection in the Change Category workflows.</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory)">
      <summary>Creates a copy of an EntityCategory</summary>
      <param>The EntityCategory to copy</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory.Equals(System.Object)">
      <summary>Tests if <paramref name="obj" /> is equal to this EntityCategory.</summary>
      <param>The object to test for equality.</param>
      <returns>True if <paramref name="obj" /> is equal to this object.</returns>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory.AssociatedEntityClass">
      <summary>Gets the entity class associated with this category. Categories can not be shared across multiple entity classes.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory.Color">
      <summary>Gets or sets the color of this category. The value is the integer representation of an RGB Color.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory.Description">
      <summary>Gets or sets the description of this category.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory.EmptyCategory">
      <summary>Gets a static category that is not associated with a real server category. This is used when managing base property policies.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory.ID">
      <summary>Gets or sets the unique ID associated with this category. -1 if this is an unsaved object.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory.IsDefault">
      <summary>Gets whether this is the default category for it's associated entity class. Each Entity Class can have one default category.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory.IsEnabled">
      <summary>Gets or sets whether this category is enabled. Disabled categories are generally hidden from the user.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory.Name">
      <summary>Gets or sets the display name for this category.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory.SystemName">
      <summary>Gets the unique System Name associated with this category.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClass.#ctor(Autodesk.Connectivity.WebServices.EntClassCfg,System.Boolean)">
      <summary>Creates an EntityClass object</summary>
      <param>A webservices object representing the entity class</param>
      <param>True if entity class should be shown to end users when they select from a list of entities to work with</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClass.#ctor(System.String,System.String,System.String,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
      <summary>Creates an EntityClass object.</summary>
      <param>An id that uniquely identifies this entity class. See EntityClassIds for a
list of well known ids.</param>
      <param>The actual server ID for this entity class. Some entity classes have multiple sub-types which all have the same EntityClass backing. Other Entity Classes are
client only concepts that have no server backing at all.</param>
      <param>The display name for this entity class.</param>
      <param>True if this entity class supports property administration.</param>
      <param>True if this entity class can have categories associated with it</param>
      <param>True if this entity class supports revisions</param>
      <param>True if this entity class supports lifecylces.</param>
      <param>True if this entity class supports property mapping.</param>
      <param>True if this entity class supports property policies.</param>
      <param>True if this entity class supports equivalence.</param>
      <param>True if this entity class supports searching</param>
      <param>True if this entity class supports links.</param>
      <param>True if entity class should be shown to end users when they select from a list of entities to work with</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClass.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClass)">
      <summary>Creates an EntityClass object that is a duplicate of another EntityClass.</summary>
      <param>The EntityClass to copy from.</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClass.DisplayName">
      <summary>Gets a display name for this entity class.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClass.Id">
      <summary>Gets the id that uniquely identifies this entity class. See  for a list of
well known ids.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClass.IsContainer">
      <summary>Gets whetheror not this entity class acts as a container for other entities. For example, a Folder can contain files.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClass.IsUserPresentable">
      <summary>Gets whether or not this entity class should be shown to end users when they select from a list of entities to work with</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClass.ServerId">
      <summary>Gets the actual server ID for this entity class. Some entity classes have multiple sub-types which all have the same EntityClass backing. Other Entity Classes
are client only concepts that have no server backing at all.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClass.SupportsCategories">
      <summary>Gets whether or not this entity class supports categories.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClass.SupportsConstraints">
      <summary>Gets whether or not this entity class supports property policies</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClass.SupportsContentSourceMappings">
      <summary>Gets whether or not this entity class supports mappings between user defined and content source property definitions.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClass.SupportsEquivalence">
      <summary>Gets whether or not this entity class supports equivalence.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClass.SupportsLifecycle">
      <summary>Gets whether or not this entity class supports lifecycles.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClass.SupportsLinks">
      <summary>Gets whether or not this entity class supports Links.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClass.SupportsPropertyAdmin">
      <summary>Gets whether or not this entity class supports property administration.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClass.SupportsRevisions">
      <summary>Gets whether or not this entity class supports revisions.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClass.SupportsSearch">
      <summary>Gets whether or not this entity class supports searching</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClassIds.ChangeOrders">
      <summary>The Change Order entity class id ("CO")</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClassIds.CustomObject">
      <summary>The CustomObject class id ("CUSTENT")</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClassIds.Files">
      <summary>The File entity class id ("FILE")</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClassIds.Folder">
      <summary>The Folder entity class id ("FLDR")</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClassIds.ForumMessage">
      <summary>The Forum Message entity class id ("FRMMSG")</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClassIds.ItemReferenceDesignators">
      <summary>The Item Reference Designators entity class id ("ITEMRDES")</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClassIds.Items">
      <summary>The Item entity class id ("ITEM")</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClassIds.Link">
      <summary>The Link entity class id ("LINK")</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClassIds.Root">
      <summary>The Root Folder ($) entity class id ("ROOT")</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityRelationship.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity,System.Boolean,System.String,System.String)">
      <summary>Constructs an instance of a EntityRelationship object</summary>
      <param>The parent in a parent-child relationship (ie. an assembly is a parent to a part)</param>
      <param>The child in a parent-child relationship (ie. a part is a child of an assembly)</param>
      <param>An option string which identifies the type of relationship. It is often the name of the application that created the relationship. For example, in the case of
an Assembly that is the parent of a Part, the relationshipType will be "INVENTOR"</param>
      <param>Determines whether or not the relationship is a manual one (ie. an attachment) vs a true dependency</param>
      <param>An ID that uniquely identifies the parent-child relationship</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityRelationship.Child">
      <summary>Gets the child in a parent-child relationship (ie. a part is a child of an assembly)</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityRelationship.IsAttachment">
      <summary>Gets if the relationship was manually created by the user (ie. an attachment) as opposed to a dependency relationship where file A depends on file B</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityRelationship.Key">
      <summary>Gets the key of this relationship, which is an identifier of this relationship.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityRelationship.Parent">
      <summary>Gets the parent in a parent-child relationship (ie. an assembly is a parent to a part)</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityRelationship.RefId">
      <summary>Gets an ID that uniquely identifies the parent-child relationship</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityRelationship.RelationshipType">
      <summary>An option string which identifies the type of relationship. It is often the name of the application that created the relationship. For example, in the case of
an Assembly that is the parent of a Part, the relationshipType will be "INVENTOR"</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityRevision.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity,System.String,System.String)">
      <summary>Constructs an instance of an EntityRevision object</summary>
      <param>The entity object associated with this revision</param>
      <param>The name of the Revision associated with the entity</param>
      <param>The name of the Lifecycle State that the entity is in</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityRevision.Entity">
      <summary>Gets the Entity associated with the revision information</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityRevision.LifecycleStateName">
      <summary>Gets the name of the Lifecycle State associated with </summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityRevision.RevisionName">
      <summary>Gets the name of the Revision associated with </summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection,Autodesk.Connectivity.WebServices.File)">
      <summary>Creates a File Iteration entity object</summary>
      <param>A connection to a vault that this FileIteration belongs to.</param>
      <param>A low level File object as returned by the Web Services.</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder,Autodesk.Connectivity.WebServices.File)">
      <summary>Creates a File Iteration entity object</summary>
      <param>A connection to a vault that this FileIteration belongs to.</param>
      <param>The folder that this file lives in.</param>
      <param>A low level File object as returned by the Web Services.</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.Equals(System.Object)">
      <summary>Tests if <paramref name="obj" /> is equal to this File object.</summary>
      <param>The object to test for equality.</param>
      <returns>True if <paramref name="obj" /> is equal to this object.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.op_Implicit(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration)~Autodesk.Connectivity.WebServices.File">
      <summary>Casts this FileIteration object to a Web Services File.</summary>
      <param>The FileIteration to cast</param>
      <returns>The Web Services representation of <paramref name="file" /></returns>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.Category">
      <summary>Gets the category that is assigned to this file</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.CheckedOutMachine">
      <summary>Gets the network ID of the computer that checked out the file. This property is only valid if
 is true.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.CheckedOutSpec">
      <summary>Gets the path on the client's computer that the file was checked out to. This property is only valid if
 is true.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.CheckedOutUserId">
      <summary>Gets the ID of the user that has the file checked out. This property is only valid if
 is true.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.CheckInDate">
      <summary>Gets the date and time that the file was checked in. This property is only valid if
 is false.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.Checksum">
      <summary>Gets the physical file's checksum. This is only valid if
IsCheckedOut is false.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.Comment">
      <summary>Gets a comment string set by the user. If  is true, then this
property contains the comment set during checkout. Otherwise, it contains the comment set during check-in.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.CreateDate">
      <summary>Gets the date and time that this version of the file was created in the Vault. For the first version of a file, this date will match
. For later versions, this value will have the time when the
previous version was checked out.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.CreateUserName">
      <summary>Gets the name of the user who checked-in or uploaded this file.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.DesignVisualizationAttachmentStatus">
      <summary>Gets the design visualization attachment status of the file</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.EntityClass">
      <summary>Gets the entity class associated with this File.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.EntityIterationId">
      <summary>Gets the Iteration ID of this File.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.EntityMasterId">
      <summary>Gets the Master Id of this File.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.EntityName">
      <summary>Gets the descriptive name for this File. This value applies to all versions of the file.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.FileClassification">
      <summary>Gets the classification of the file.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.FolderId">
      <summary>Gets the ID of the folder that the file was checked-out from. This property is only valid if
 is true.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.IsCheckedOut">
      <summary>Gets whether the latest version of this file is in the checked-out state. Whenever a file is checked-out, a new version is created as a placeholder.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.IsCheckedOutToCurrentUser">
      <summary>Gets whether or not this file is checked out to the currently logged in vault user</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.IsCloaked">
      <summary>Gets if this file is cloaked. A cloaked object is one that the caller does not have permissions to view. The only valid data in a cloaked file is the
 and the
<see cref="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.EntityMasterId" />. All other data will be null or 0.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.IsControlledByChangeOrder">
      <summary>Gets whether or not the file is controlled by a change order.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.IsHidden">
      <summary>Gets whether this file should be hidden to the user.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.IsLatestVersion">
      <summary>Gets whether or not this file is the latest version.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.IsLocked">
      <summary>Gets whether or not the file can be modified by the logged in user.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.IsOnSite">
      <summary>Gets whether or not the file is on the local site (in a multi-site environment). This value will always be true on a single-site environment.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.IsOutOfDate">
      <summary>Gets whether or not this file needs to be updated.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.LastModifiedDate">
      <summary>Gets the last modified date of the physical file. This value is set by the client that uploaded the file.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.LifecycleInfo">
      <summary>Gets lifecycle information about this file.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.LinkInfo">
      <summary>Gets if this is a Link to a File. If the value is not null, then LinkInfo provides information about the Link to this File</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.MaxCheckedInVersionNumber">
      <summary>Gets the maximimum version of the file currently checked-in to the vault.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.Parent">
      <summary>Gets or sets the parent folder for this FileIteration.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.RevisionInfo">
      <summary>Gets revision information about this file.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.Size">
      <summary>Gets the size of this file (in bytes)</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.VaultConnection">
      <summary>Gets the Vault Connection that this file is associated with.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration.VersionNumber">
      <summary>Gets the version of the file. A file that has just been added ot the Vault will have a VerNum of 1.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileLifecycleInfo.#ctor(Autodesk.Connectivity.WebServices.File)">
      <summary>Create a FileLifecycleInfo object</summary>
      <param>A file object obtained from the Web Services layer</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileLifecycleInfo.DefinitionId">
      <summary>Get the ID of the life cycle definition for the latest file within the same revision</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileLifecycleInfo.IsConsumable">
      <summary>Gets whether or not the current file version is in a consumable (released) state.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileLifecycleInfo.StateId">
      <summary>Get the ID of the life cycle state for the latest file within the same revision</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileLifecycleInfo.StateName">
      <summary>Gets the name of the life cycle state for the current file version</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileRelationship.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration,System.String,System.String,System.String,System.Boolean,System.Boolean)">
      <summary>Constructs an instance of a FileRelationship object</summary>
      <param>The parent in a parent-child relationship (ie. an assembly is a parent to a part)</param>
      <param>The child in a parent-child relationship (ie. a part is a child of an assembly)</param>
      <param>The path of the child file. The may be a relative path or an absolute path, depending on how the host application maintains relationships</param>
      <param>An ID that uniquely identifies the parent-child relationship</param>
      <param>An option string which identifies the type of relationship. It is often the name of the application that created the relationship. For example, in the case of
an Assembly that is the parent of a Part, the relationshipType will be "INVENTOR"</param>
      <param>Determines whether or not the relationship is a manual one (ie. an attachment) vs a true dependency</param>
      <param>Determines whether or not the location of the parent or child in the relationship has changed. If so, then the parent needs to have it's references updated.</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileRelationship.Child">
      <summary>Gets the child in a parent-child relationship (ie. a part is a child of an assembly)</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileRelationship.ChildFile">
      <summary>Gets the child file in a parent-child relationship (ie. a part is a child of an assembly)</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileRelationship.ChildPath">
      <summary>The path of the child file. The may be a relative path or an absolute path, depending on how the host application maintains relationships</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileRelationship.HasPathChanged">
      <summary>Gets whether or not the location of the parent or child in the relationship has changed. If so, then the parent needs to have it's references updated.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileRelationship.IsAttachment">
      <summary>Gets if the relationship was manually created by the user (ie. an attachment) as opposed to a dependency relationship where file A depends on file B</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileRelationship.Key">
      <summary>Gets the key of this relationship, which is an identifier of this relationship.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileRelationship.Parent">
      <summary>Gets the parent in a parent-child relationship (ie. an assembly is a parent to a part)</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileRelationship.ParentFile">
      <summary>Gets the parent file in a parent-child relationship (ie. an assembly is a parent to a part)</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileRelationship.RefId">
      <summary>Gets an ID that uniquely identifies the parent-child relationship</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileRelationship.RelationshipType">
      <summary>An option string which identifies the type of relationship. It is often the name of the application that created the relationship. For example, in the case of
an Assembly that is the parent of a Part, the relationshipType will be "INVENTOR"</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileRevisionInfo.#ctor(Autodesk.Connectivity.WebServices.File)">
      <summary>Create a FileRevisionInfo object</summary>
      <param>A file object obtained from the Web Services layer</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileRevisionInfo.GetTargetVersionId(System.Boolean)">
      <summary>Gets the tip version id or tip released version id from current revision</summary>
      <param>True if we should getthe tip released version id, false if we should get the tip version id</param>
      <returns>Target version id</returns>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileRevisionInfo.HasReleasedVersion">
      <summary>Gets whether the tip version in current revision is released.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileRevisionInfo.IsNullRevision">
      <summary>Get whether or not this is a Null Revision. A Null Revision indicates that either the file does not have a revision associated with it, or the application does
not support file revisions.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileRevisionInfo.IsTipReleasedOfCurrentRevision">
      <summary>Gets whether this file is the tip released version of the current revision.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileRevisionInfo.IsTipReleasedOfHistoricalRevision">
      <summary>Gets whether this file is the tip released version of a historical revision.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileRevisionInfo.IsTipReleasedOfTipRevision">
      <summary>Gets whether this file is the tip released version of the tip revision.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileRevisionInfo.IsTipRevision">
      <summary>Gets whether this revision is this tip revision.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileRevisionInfo.IsTipVersionOfCurrentRevision">
      <summary>Gets whether this file is the tip version of current revision.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileRevisionInfo.IsTipVersionOfHistoricalRevision">
      <summary>Gets whether this file is the tip version of a historical revision.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileRevisionInfo.IsTipVersionOfTipRevision">
      <summary>Gets whether this file is the tip version of the tip revision</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileRevisionInfo.MaxConsumeFileId">
      <summary>Gets the ID of the file with the highest version that is in a consumable life cycle state. -1 will be returned if there is no consumable version.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileRevisionInfo.MaxFileId">
      <summary>Gets the ID of the file with the highest version for this revision</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileRevisionInfo.Order">
      <summary>Gets a value which indicates how the revision should be ordered in relation to other revisions. Lower order revisions should be listed before higher order
revisions.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileRevisionInfo.RevisionId">
      <summary>Gets a unique identifier for this file revision</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileRevisionInfo.RevisionLabel">
      <summary>Gets the revision label of the file.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileRevisionInfo.RevSchemaId">
      <summary>Gets the id of the revision schema for this file.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection,Autodesk.Connectivity.WebServices.Folder)">
      <summary>Creates a Folder entity object</summary>
      <param>A connection to a vault that this Folder belongs to.</param>
      <param>A low level Folder object as returned by the Web Services.</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder.Equals(System.Object)">
      <summary>Tests if <paramref name="obj" /> is equal to this Folder object.</summary>
      <param>The object to test for equality.</param>
      <returns>True if <paramref name="obj" /> is equal to this object.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder.op_Implicit(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder)~Autodesk.Connectivity.WebServices.Folder">
      <summary>Casts this Folder object to a Web Services Folder.</summary>
      <param>The Folder to cast</param>
      <returns>The Web Services representation of <paramref name="folder" /></returns>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder.Category">
      <summary>Gets the category that is assigned to this folder. This can be null as not all products support categories.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder.CreateDate">
      <summary>Gets the date and time that the folder was created.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder.CreateUserId">
      <summary>Gets the ID of the user who crated this folder.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder.CreateUserName">
      <summary>Gets the name of the user who created this folder.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder.EntityClass">
      <summary>Gets the entity class associated with this Folder.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder.EntityIterationId">
      <summary>Gets the Iteration ID of this Folder. Since Folders are not iteration based, this will always have the same value as the
.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder.EntityMasterId">
      <summary>Gets the Master Id of this Folder. Folders are not iteration based and the MasterID is always the sole unique identifier for a folder.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder.EntityName">
      <summary>Gets the descriptive name for this Folder. This is always the Folder Name without the full path.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder.FolderPath">
      <summary>Gets the path of the location of this folder. If this folder is $/Folder1/Folder2, then the path would be $/Folder1 In the case where this folder is the root
($), the returned path is the empty string.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder.FullName">
      <summary>Gets the full vault path for this folder (ie. "$/Folder1")</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder.FullUncName">
      <summary>Gets the full UNC path for this folder, or null if no UNC path exists.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder.Id">
      <summary>Gets the unique identifier for this folder</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder.IsCloaked">
      <summary>Gets if this folder is cloaked. A cloaked object is one that the caller does not have permissions to view. The only valid data in a cloaked folder is the
. All other data will be null or 0.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder.IsLibraryFolder">
      <summary>Gets whether or not this folder is a library folder</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder.IsVaultRoot">
      <summary>Gets whether or not this folder is the vault root folder</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder.LinkInfo">
      <summary>Gets if this is a Link to a Folder. If the value is not null, then LinkInfo provides information about the Link to this Folder</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder.Locked">
      <summary>Gets whether this folder can be modified by the logged in user.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder.NumberOfChildren">
      <summary>Gets the number of immediate child folders.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder.Parent">
      <summary>Gets or sets the parent folder. It is important to note that this CAN be null. A folder always has a parent ID but the full blown object might not have been
loaded.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder.ParentId">
      <summary>Gets the Id of this folders parent. A value of -1 means that this folder has no parent</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ItemRevision.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection,Autodesk.Connectivity.WebServices.Item)">
      <summary>Creates an ItemRevision entity object</summary>
      <param>A connection to a vault that this ItemRevision belongs to.</param>
      <param>A low level Item object as returned by the Web Services.</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ItemRevision.Equals(System.Object)">
      <summary>Tests if <paramref name="obj" /> is equal to this Item object.</summary>
      <param>The object to test for equality.</param>
      <returns>True if <paramref name="obj" /> is equal to this object.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ItemRevision.op_Implicit(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ItemRevision)~Autodesk.Connectivity.WebServices.Item">
      <summary>Casts this ItemRevision object to a Web Services Item.</summary>
      <param>The ItemRevision to cast</param>
      <returns>The Web Services representation of <paramref name="itemRev" /></returns>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ItemRevision.Category">
      <summary>Gets the category that is assigned to this Item</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ItemRevision.ControlledByCO">
      <summary>Gets if the item is being controlled by a Change Order. If so, then certain properties cannot be manually changed.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ItemRevision.Description">
      <summary>Gets or sets a description for this item</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ItemRevision.EntityClass">
      <summary>Gets the entity class associated with this Item.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ItemRevision.EntityIterationId">
      <summary>Gets the Iteration ID of this Item.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ItemRevision.EntityMasterId">
      <summary>Gets the Master Id of this Item.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ItemRevision.EntityName">
      <summary>Gets the descriptive name for this Item. The  property value is
used for the EntityName</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ItemRevision.IsCloaked">
      <summary>Gets if this Item is cloaked. A cloaked object is one that the caller does not have permissions to view. The only valid data in a cloaked item is the
 and the
<see cref="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ItemRevision.EntityMasterId" />. All other data will be null or 0.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ItemRevision.ItemNumber">
      <summary>Gets or sets the Item Number associated with this Item.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ItemRevision.LastModDate">
      <summary>Gets the last time the item revision was modified</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ItemRevision.LastModUserId">
      <summary>Gets the id of the user who last modified this item</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ItemRevision.LastModUserName">
      <summary>Gets the name of the user who last modified this item</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ItemRevision.LifecycleID">
      <summary>Gets the ID of the Life Cycle State associated with this Item Revision</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ItemRevision.LinkInfo">
      <summary>Gets if this is a Link to an Item. If the value is not null, then LinkInfo provides information about the Link to this Item</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ItemRevision.Locked">
      <summary>Gets whether this Item Revision is locked or not.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ItemRevision.NumberingSchemeId">
      <summary>Gets or sets the ID of the Numbering Scheme used to generate the 
for this Item</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ItemRevision.Revision">
      <summary>Gets or sets the display value for the item revision</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ItemRevision.RevisionID">
      <summary>Gets the ID of this Item Revision. This is different from 
as a Revision can have multiple iterations and a master can have multiple revisions.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ItemRevision.Title">
      <summary>Gets or sets the Title for this item.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ItemRevision.Unit">
      <summary>Gets or sets the units for this item</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ItemRevision.UnitOfMeasureId">
      <summary>Gets or sets the unit of measure id for this item</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ItemRevision.VaultConnection">
      <summary>Gets the Vault Connection that this item is associated with.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.ItemRevision.Version">
      <summary>Gets the items version number, which is a sequential number given to each version. For example, if this value is 5, then the object is the 5th version.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Link.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity,System.Int64,System.String,System.Int64,System.String)">
      <summary>Creates a new Link object</summary>
      <param>The container that owns this link object</param>
      <param>The unique identifier for this link object</param>
      <param>The link number for this link object</param>
      <param>The unique iteration id for the entity that this link points to</param>
      <param>The entity class id of the entity that this link points to</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Link.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Link)">
      <summary>Creates a copy of a link object</summary>
      <param>The link that is to be copied</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Link.LinkId">
      <summary>Gets the unique identifier for this link object</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Link.LinkNumber">
      <summary>Gets the link number for this link object</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Link.Parent">
      <summary>Gets or sets the entity that owns the link.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Link.TargetEntityClassId">
      <summary>Gets the entity class id for the entity that this link points to.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Link.TargetId">
      <summary>Gets the iteration id for the entity that this link points to.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.PropertyDefinitionToEntityClassAssociation.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClass,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.SupportedMappingDirection)">
      <summary>Creates a PropertyDefinitionToEntityClassAssociation object.</summary>
      <param>The entity class to associate with a </param>
      <param>The mapping directions that the  can create.</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.PropertyDefinitionToEntityClassAssociation.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClass)">
      <summary>Creates a PropertyDefinitionClassAssociation object with ReadWrite mapping capabilities.</summary>
      <param>The entity class to associate with a </param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.PropertyDefinitionToEntityClassAssociation.EntityClass">
      <summary>Gets the entity class that is associated with a .</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.PropertyDefinitionToEntityClassAssociation.SupportedMappingDir">
      <summary>Gets the mapping directions that the  can create.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.FileSystem.FileReferenceUpdateStatus.UpdateStatus">
      <summary>The status of each reference describing whether or not the reference has been updated</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.FileSystem.FileReference">
      <summary>This class represents a reference to a physical file</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.FileSystem.FileReferenceUpdateStatus">
      <summary>This class stores information about how a particular reference inside a file was updated</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.FileSystem.FileReference.#ctor(System.String,Autodesk.DataManagement.Client.Framework.Currency.FilePathAbsolute,System.String)">
      <summary>Creates a new instance of a FileReference object</summary>
      <param>An id which uniquely identifies the reference within the source file</param>
      <param>The physical location of the file that is referenced</param>
      <param>An optional value which identifies the application that owns the reference. For example, a DWG file can have references written by Inventor and other
references written by Autocad</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.FileSystem.FileReference.Path">
      <summary>Gets the physical location of the file that is referenced</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.FileSystem.FileReference.RefId">
      <summary>Gets a value which uniquely identifies this reference within the source file</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.FileSystem.FileReference.SourceTag">
      <summary>Gets an optional value that is set by a host application which describes the association type and/or owner. For example, a DWG file can have references written
by Inventor and other references written by Autocad.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.FileSystem.FileReferenceUpdateStatus.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.FileSystem.FileReference)">
      <summary>Constructs an instance of the FileReferenceUpdateStatus class</summary>
      <param>The file reference that this object is reporting status on</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.FileSystem.FileReferenceUpdateStatus.Reference">
      <summary>Gets the file reference that has been updated</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.FileSystem.FileReferenceUpdateStatus.ResultDescription">
      <summary>Gets details which describe (for logging purposes) the results of the update</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.FileSystem.FileReferenceUpdateStatus.Status">
      <summary>Gets the status of the update</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.PersistentId.PersistableIdEntInfo">
      <summary>Wrapper for the server ACW.Ent object that contains some extra information</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.PersistentId.PersistableIdEntInfo.#ctor(System.String,System.Int64,System.Boolean,System.Boolean)">
      <summary>An extension of the ACW.Ent class with some extra data indicated if it repesents the master(sliding latest version) of the object</summary>
      <param>The entity class of the entity.</param>
      <param>The id of the entity.</param>
      <param>Whether this object represents the master(sliding latest version) or a particular version of the entity.</param>
      <param>Tells whether this is a version id or a master id.</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.PersistentId.PersistableIdEntInfo.FromString(System.String)">
      <summary>Converts from the string representation to an instance of a ServerEntWrapper object</summary>
      <returns>Returns null if there was an error parsing the string</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.PersistentId.PersistableIdEntInfo.ToString">
      <summary>Builds a string which can be used as a key in the cache</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.PersistentId.PersistableIdEntInfo.IsCachable">
      <summary>To be cachable, the Ent Info cannot represent the latest and be populated with a version id</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.CaseSensitivityConstraint.CaseSensitivity">
      <summary>Case Sensitivity policy values</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourcePropertyDefinition.ClassificationEnum">
      <summary>The type of the ContentSourcePropertyDefinition from the content source perspective.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus.CheckoutStateEnum">
      <summary>Represents a state which detals if an entity is checked out and if so, to whom</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus.ConsumableStateEnum">
      <summary>Represents a state which describes if the entity on disk is the latest consumable entity.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus.ErrorStateEnum">
      <summary>Represents a state which describes possible reasons why the EntityStatus could not be computed.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus.LocalEditsStateEnum">
      <summary>Represents a state which details if a file on disk has untracked Vault edits.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus.LockStateEnum">
      <summary>Represents a state which describes whether or not the entity is locked.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus.RevisionStateEnum">
      <summary>Represents a state which describes how the Revision of the entity on disk relates to the latest Revision of the entity in the vault</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus.VersionStateEnum">
      <summary>Represents a state which describes how the version of the entity on disk relates to the version of the entity in the vault</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MappingDirection">
      <summary>The mapping direction for a mapping between a  and a
<see cref="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourcePropertyDefinition" /></summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyCacheClearOptions">
      <summary>Options for clearing property values out of the cache</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyConstraint.ResolveResult">
      <summary>The result of a  operation on a
contraint object.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyConstraintType">
      <summary>This type represents the list of available Property Polcies that can be assigned to
 objects.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.EBasicSearch">
      <summary>The search capabilities of a .</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.ERetrievalAlgorithm">
      <summary>This type specifies the relative cost of retrieving values for a </summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.PropertyDataType">
      <summary>The data type of a </summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionFilter">
      <summary>This enum determins which PropertyDefinitions should be retrieved from
</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyMappings.DesiredMappingTypes">
      <summary>This determines the types of mappings to retrieve from
.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.SupportedMappingDirection">
      <summary>The allowed mappings between a  and a
<see cref="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourcePropertyDefinition" />. There can be a different SupportedMappingDirection
value for each <see cref="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClass" /> that the PropertyDefinition is associated with.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ThumbnailInfo.ThumbnailType">
      <summary>The type of image contains in the ThumbnailInfo</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.CaseSensitivityConstraint">
      <summary>This policy is used to created a case sensitivity policy for a Text property. For example, a text property value may be in All Capps</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourcePropertyDefinition">
      <summary>Describes a property definition that lives in a content file (ie. An office document, an inventor document, an autocad document)</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourcePropertyMapping">
      <summary>This class represents an individual mapping between a  and a
<see cref="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourcePropertyDefinition" /></summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourceProvider">
      <summary>A content source provider is an object that provides the ability to extract properties from a source and make those properties available for mapping to object
properties in the vault. An example of a content source provider would be Inventor. The Inventor content source provider knows how to extract properties from
Inventor files.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EnforceListOfValuesConstraint">
      <summary>This policy is used to created an enforcement policy for properties that contain a List Of Values. The policy allows you to specify whether property values
must be part of the list of values. For example, if a property has a List of Values of A, B, and C, and the property has a value of D, then this determines
whether or not that should be considered a policy violation.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus">
      <summary>Represents the status of a local file relative to its related vault file.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatusImageInfo">
      <summary>This is a specialized  that bundles an
<see cref="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus" /> with the rest of the image details.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ImageInfo">
      <summary>This object is returned by

for properties of DataType=<see cref="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.PropertyDataType.ImageInfo" />. This object
contains an image and information about that image.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumDateConstraint">
      <summary>This policy is used to created a maxmimum value for a Date property. For example, a date property value may not be greater than 01/01/2020</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumLengthConstraint">
      <summary>This policy is used to created a maxmimum length for a text property. For example, a text property value may not be greater than 10 characters long.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumValueConstraint">
      <summary>This policy is used to created a maxmimum value for a number property. For example, a numeric property value may not be greater than 1000</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumDateConstraint">
      <summary>This policy is used to created a minimum value for a Date property. For example, a date property value must be at least 01/01/2010</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumLengthConstraint">
      <summary>This policy is used to created a minimum length for a text property. For example, a text property value must be at least 5 characters long.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumValueConstraint">
      <summary>This policy is used to created a minimum value for a number property. For example, a numeric property must have a value of at least 10</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyConstraint">
      <summary>This abstract base class represents a policy on the value of a property. For example, "the property value must be at least 6 characters long". It can be used
to test the value of a property to see if it violates the constraint.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition">
      <summary>Represents the definition of Property. A  represents a physical property
value. For example, a file has an Author property value of "Michael". In this example, "Michael" is the property value and Author is the PropertyDefinition.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.DefinitionConstraints">
      <summary>This class manages the collection of constraints associated with a
property Definition.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.EnumeratedValue">
      <summary>Represent a possible value that can occur in a  (or List Of
Values). A List Of Values are the recommended (and sometimes enforced) values that the user should select from when entering a property value.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionDictionary">
      <summary>This class is a collection (dictionary) of PropertyDefinition objects with a dual key. Each item in the collection has a "long" ID key and a "string" key. The
long key is typically a database id, and can not be used to persist from session to session because migrations can cause the value to change. The "string" key
is a system name that is unique across all PropertyDefinitions and can be persisted from session to session.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds">
      <summary>This class contains constants for well-known System Names of PropertyDefinitions</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Client">
      <summary>System names for Client PropertyDefinitions. Client PropertyDefinitions are ones that are computed on the client, but the server doesn't know about them. These
PropertyDefinitions can be shown in any UI, but they can not be searched or otherwise administered.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server">
      <summary>System names for well know server Property definitions. The comments for each property will indicate the
 of the PropertyDefinition</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyMappings">
      <summary>This class stores all of the mappings for a single </summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValue">
      <summary>Represents a property value for a particular entity</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValues">
      <summary>This class represents several Property Values bundles together in a single object.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValueSettings">
      <summary>Optional settings that can be passed to the

api</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.RequiresValueConstraint">
      <summary>This policy is used to created a policy that determines whether or not a property value is required. For example, if a required policy is sent, then a property
without a value will be considered a policy violation.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ThumbnailInfo">
      <summary>This object is returned by

for properties of DataType=<see cref="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.PropertyDataType.Image" />, which is typically
used for Thumbnails. This object contains the image and information about that image.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.CaseSensitivityConstraint.#ctor(System.Object,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory,System.Int64)">
      <summary>Creates a CaseSensitivityConstraint object</summary>
      <param>The  policy</param>
      <param>If specified, then this policy only applies to entites that match this category. Passing in Null or
 means the the policy applies to all entities.</param>
      <param>The ID of the property that this policy applies to</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.CaseSensitivityConstraint.#ctor(System.Int64,System.Object,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory,System.Int64)">
      <summary>Creates a CaseSensitivityConstraint object</summary>
      <param>The unique identifier for this policy</param>
      <param>The  policy</param>
      <param>If specified, then this policy only applies to entites that match this category. Passing in Null or
 means the the policy applies to all entities.</param>
      <param>The ID of the property that this policy applies to</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.CaseSensitivityConstraint.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.CaseSensitivityConstraint)">
      <summary>Creates a copy of a CaseSensitivityConstraint object</summary>
      <param>The object to copy</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.CaseSensitivityConstraint.ConstraintType">
      <summary>Gets the type of constraint represented by this object.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.CaseSensitivityConstraint.ConstraintValue">
      <summary>Gets or sets the case sensitivity policy enforced by this constraint</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.CaseSensitivityConstraint.EntityCategory">
      <summary>Gets the category that this constraint is associated with.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.CaseSensitivityConstraint.ID">
      <summary>Gets the unique identifies of this constraint.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.CaseSensitivityConstraint.PropDefID">
      <summary>Gets the ID of the  that this constraint is associated with</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.CaseSensitivityConstraint.RestrictionMessage">
      <summary>Gets the restriction message of this constraint. If
 returns a value of
<see cref="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyConstraint.ResolveResult.Failed" />, then the RestrictionMessage will contain a
string which describes the failure.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourcePropertyDefinition.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourceProvider,System.String,System.String,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.SupportedMappingDirection,System.Boolean,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.PropertyDataType,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourcePropertyDefinition.ClassificationEnum)">
      <summary>Creates an instance of a ContentSourcePropertyDefinition</summary>
      <param>The provider that was used to pull this definition out of a content file.</param>
      <param>The display name of the property definition</param>
      <param>A string which uniquely identifies this property definition</param>
      <param>Describes the allowed mappings between this ContentSourcePropertyDefinition and Vault
PropertyDefinition's</param>
      <param>If true, then when writing properties to the content file, we will create the property if it doesn't exist. If false, then we will only modify existing
properties but can't create new ones. AutoCAD Block attributes are an example of properties that can only be modified, but not created.</param>
      <param>The data type of this property.</param>
      <param>Describes the type of the ContentSourcePropertyDefinition from the content files perspective.</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourcePropertyDefinition.Classification">
      <summary>Gets a value that describes the type of the ContentSourcePropertyDefinition from the content source perspective.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourcePropertyDefinition.DataType">
      <summary>Gets the data type of the ContentSourcePropertyDefinition</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourcePropertyDefinition.DisplayName">
      <summary>Gets the display name of the ContentSourcePropertyDefinition</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourcePropertyDefinition.Moniker">
      <summary>Gets a string which uniquely identifies thie ContentSourcePropertyDefinition</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourcePropertyDefinition.Provider">
      <summary>Gets the ContentSourceProvider that was used to pull thie ContentSourcePropertyDefinition out of a content file.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourcePropertyDefinition.SupportCreate">
      <summary>Gets a value which describes whether new properties can be created in the content source. If true, then when writing properties to the content file, we will
create the property if it doesn't exist. If false, then we will only modify existing properties but can't create new ones. AutoCAD Block attributes are an
example of properties that can only be modified, but not created.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourcePropertyDefinition.SupportedMappingDir">
      <summary>Gets a value which describes the allowed mappings between this ContentSourcePropertyDefinition and Vault
PropertyDefinition's</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourcePropertyDefinition.SupportWrite">
      <summary>Gets a value which indicates whether this content source property can be written to. If false, then the property is read only.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourcePropertyMapping.#ctor(System.Int64,System.String,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourcePropertyDefinition,System.Int32,System.Int32,System.Boolean,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MappingDirection,System.Boolean)">
      <summary>Creates a new instance of a ContentSourcePropertyMapping</summary>
      <param>The unique id of the  that is being mapped</param>
      <param>The ID of the Entity Class that this mapping applies to (ie. Applies to
FILE entities)</param>
      <param>The ContentSourcePropertyDefinition that is being mapped</param>
      <param>When there are multiple ContentSourcePropertyDefinitions with read mappings to a single PropertyDefinition, this is the order that the mappings should be
evaluated. Lower priority mappings are evaluated first.</param>
      <param>When there are multiple PropertyDefinitions with write mappings to a single ContentSourcePropertyDefinition, this is the order that the mappings should be
evaluated. Lower priority mappings are evaluated first.</param>
      <param>If True, then this mapping only applies to how default values are assigned. If False, then this mapping applies to standard property editing, check-in and
check-out scenarios</param>
      <param>Determines the mapping direction between the PropertyDefinition and the ContentSourcePropertyDefinition</param>
      <param>For mappings which allow writing to content source files, this determines if a property can be created if it doesn't exist. If False, then properties can be
modified but not created.</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourcePropertyMapping.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourcePropertyMapping)">
      <summary>Creates a copy of a ContentSourcePropertyMapping</summary>
      <param>The object to copy</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourcePropertyMapping.ContentPropertyDefinition">
      <summary>Gets the ContentSourcePropertyDefinition that is mapped</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourcePropertyMapping.CreateNew">
      <summary>Gets whether or not properties can be created in content source files if they don't already exist. If false, then properties can be modified but not created.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourcePropertyMapping.EntityClassId">
      <summary>Gets the ID of the Entity Class that this mapping applies to (ie. Applies
to FILE entities)</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourcePropertyMapping.IsDefaultValueMap">
      <summary>Gets if this this mapping only applies to how default values are assigned. If True, then this mapping applies to the creation of default values. If False, then
this mapping applies to standard property editing, check-in and check-out scenarios</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourcePropertyMapping.IsWriteMapping">
      <summary>Gets whether or not the mapping supports outward writing to the file</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourcePropertyMapping.MappingDirection">
      <summary>Gets the mapping direction between the PropertyDefinition and the ContentSourcePropertyDefinition</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourcePropertyMapping.PropertyDefinitionId">
      <summary>Gets the unique id of the  that is mapped</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourcePropertyMapping.ReadPriority">
      <summary>Gets or sets the Read Priority. When there are multiple ContentSourcePropertyDefinitions with read mappings to a single PropertyDefinition, this is the order
that the mappings should be evaluated. Lower priority mappings are evaluated first.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourcePropertyMapping.WritePriority">
      <summary>Gets or sets the Write Priority. When there are multiple PropertyDefinitions with write mappings to a single ContentSourcePropertyDefinition, this is the order
that the mappings should be evaluated. Lower priority mappings are evaluated first.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourceProvider.#ctor(System.Int64,System.String,System.String,System.String[],System.String[],Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.SupportedMappingDirection,System.String,System.String,System.Boolean)">
      <summary>Creates an instance of a ContentSourceProvider object</summary>
      <param>The unique identifier of the content source provider.</param>
      <param>The well known system name of the content source provider. This should be used instead of <paramref name="id" /> because it is guaranteed to have the same value for any
deployment scenario.</param>
      <param>The user viewable name of the content source provider</param>
      <param>A list of file extensions that the provider can read from.</param>
      <param>A list of file extensions that the provider can write to.</param>
      <param>Specifies what direction(s) of mapping this provider supports</param>
      <param>The assembly to use for reading properties</param>
      <param>The assembly to use for writing properties</param>
      <param>True if the provider is registered on this ADMS</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourceProvider.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourceProvider)">
      <summary>Creates a copy of a ContentSourceProvider object</summary>
      <param>The object to copy</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourceProvider.ToString">
      <summary>Gets a standard visual representation of the provider that can be used by File Open filter dialogs (ie. Office Documents (*.docx) )</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourceProvider.DisplayName">
      <summary>Gets the user viewable name of provider.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourceProvider.Id">
      <summary>Gets the unique identifier of this provider. Whenever possible, the
 should be used instead of Id.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourceProvider.IsAllProvider">
      <summary>Gets a flag which determines if this provider is configured for ALL files (as opposed to a specific file extension)</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourceProvider.IsRegistered">
      <summary>Gets whether this provider is registered on this ADMS. Typically this value is true as most providers register properly. This value will be false if a provider
is no longer loading/registering on this ADMS even though it once was.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourceProvider.ReadAssembly">
      <summary>Gets the name of the assembly which can be used to read property values</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourceProvider.ReadSources">
      <summary>Gets a list of file extensions that the provider can read from.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourceProvider.SupportsWrite">
      <summary>Gets whether or not the provider supports writing back to the content source (i.e. Property writeback)</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourceProvider.SystemName">
      <summary>Gets the well known system name of the content source provider. This should be used instead of
 because it is guaranteed to have the same value for any
deployment scenario.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourceProvider.WriteAssembly">
      <summary>Gets the name of the assembly which can be used to write property values</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourceProvider.WriteSources">
      <summary>Gets a list of file extensions that the provider can write to</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EnforceListOfValuesConstraint.#ctor(System.Object,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory,System.Int64)">
      <summary>Creates a EnforceListOfValuesConstraint object</summary>
      <param>A bool indicating whether or not list of values should be enforced</param>
      <param>If specified, then this policy only applies to entites that match this category. Passing in Null or
 means the the policy applies to all entities.</param>
      <param>The ID of the property that this policy applies to</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EnforceListOfValuesConstraint.#ctor(System.Int64,System.Object,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory,System.Int64,System.Collections.Generic.IList{Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.EnumeratedValue})">
      <summary>Creates a EnforceListOfValuesConstraint object</summary>
      <param>The unique identifier for this policy</param>
      <param>A bool indicating whether or not list of values should be enforced</param>
      <param>If specified, then this policy only applies to entites that match this category. Passing in Null or
 means the the policy applies to all entities.</param>
      <param>The ID of the property that this policy applies to</param>
      <param>The list of values that are compared to when enforcing the policy</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EnforceListOfValuesConstraint.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EnforceListOfValuesConstraint)">
      <summary>Creates a copy of a EnforceListOfValuesConstraint object</summary>
      <param>The object to copy</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EnforceListOfValuesConstraint.ConstraintType">
      <summary>Gets the type of constraint represented by this object.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EnforceListOfValuesConstraint.ConstraintValue">
      <summary>Gets or sets whether or not property values must be one of the List Of Values</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EnforceListOfValuesConstraint.EntityCategory">
      <summary>Gets the category that this constraint is associated with.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EnforceListOfValuesConstraint.ID">
      <summary>Gets the unique identifies of this constraint.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EnforceListOfValuesConstraint.PropDefID">
      <summary>Gets the ID of the  that this constraint is associated with</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EnforceListOfValuesConstraint.RestrictionMessage">
      <summary>Gets the restriction message of this constraint. If
 returns a value of
<see cref="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyConstraint.ResolveResult.Failed" />, then the RestrictionMessage will contain a
string which describes the failure.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus.#ctor">
      <summary>Creates an instance of an EntityStatus object with the ErrorState set to None and all other states set to Unknown.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus.#ctor(System.String)">
      <summary>Creates an instance of an EntityStatus object with it's contents initialized from a formatted string</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus.ErrorStateEnum,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus.CheckoutStateEnum,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus.VersionStateEnum,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus.LockStateEnum,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus.LocalEditsStateEnum)">
      <summary>Creates an instance of an EntityStatus object with the ErrorState set to None, the RevisionState and ConsumableState states set to Unknown, and all other
states as specified.</summary>
      <param>The error state</param>
      <param>The checkout state</param>
      <param>The version state</param>
      <param>The lock state</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus.ErrorStateEnum,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus.CheckoutStateEnum,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus.VersionStateEnum,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus.RevisionStateEnum,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus.ConsumableStateEnum,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus.LockStateEnum,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus.LocalEditsStateEnum)">
      <summary>Creates an instance of an EntityStatus object</summary>
      <param>The error state</param>
      <param>The checkout state</param>
      <param>The version state</param>
      <param>The revision state</param>
      <param>The consumable state</param>
      <param>The lock state</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus.CheckoutState">
      <summary>Gets or sets the Checkout State which provides information detailing if the entity is checked out, and to whom</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus.ConsumableState">
      <summary>Gets or sets a state which describes if the entity on disk is the latest consumable entity</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus.ErrorState">
      <summary>Gets or sets a state which describes reasons why the EntityStatus could not be computed.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus.LocalEditsState">
      <summary>Gets or sets a state which describes if the entity on disk is has untracked edits.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus.LockState">
      <summary>Gets or sets a state which describes whether or not the entity is locked.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus.RevisionState">
      <summary>Gets or sets a state which describes how the Revision of the entity on disk relates to the latest Revision of the entity in the vault</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus.VersionState">
      <summary>Gets or sets a state which describes how the version of the entity on disk relates to the version of the entity in the vault</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatusImageInfo.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus,System.String,System.Drawing.Image)">
      <summary>Creates an instance of an EntityStatusImageInfo object</summary>
      <param>The EntityStatus of the entity associated with this image</param>
      <param>A value that uniquely identifies this image. This can be used to prevent storing the same image in an ImageList or other container multiple times.</param>
      <param>The image object managed by this ImageInfo object.</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatusImageInfo.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatus,System.String,System.Drawing.Image,System.String)">
      <summary>Creates an instance of an EntityStatusImageInfo object</summary>
      <param>The EntityStatus of the entity associated with this image</param>
      <param>A value that uniquely identifies this image. This can be used to prevent storing the same image in an ImageList or other container multiple times.</param>
      <param>The image object managed by this ImageInfo object.</param>
      <param>An optional description of this image that can be used for tooltips (ie. "Library Folder")</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatusImageInfo.GetImage">
      <summary>Gets a copy of the image managed by this ImageInfo.</summary>
      <returns>A copy of the image. The caller is reponsible for calling Dispose on the copied image.</returns>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatusImageInfo.Description">
      <summary>Gets or sets an optional textual description of this image. This might be used as a tooltip (ie. "Library Folder")</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatusImageInfo.Image">
      <summary>Sets the image managed by this ImageInfo.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatusImageInfo.Status">
      <summary>Gets the EntityStatus associated with this image.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.EntityStatusImageInfo.Tag">
      <summary>Gets or sets a unique tag that represents this image. The tag should be somewhat human readable as this is the value that will often be store in grid cells for
this image.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ImageInfo.#ctor">
      <summary>Creates an ImageInfo object.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ImageInfo.#ctor(System.String,System.Drawing.Image)">
      <summary>Creates an ImageInfo object</summary>
      <param>A value that uniquely identifies this image. This can be used to prevent storing the same image in an ImageList or other container multiple times.</param>
      <param>The image object managed by this ImageInfo object.</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ImageInfo.#ctor(System.String,System.Drawing.Image,System.String)">
      <summary>Creates an ImageInfo object</summary>
      <param>A value that uniquely identifies this image. This can be used to prevent storing the same image in an ImageList or other container multiple times.</param>
      <param>The image object managed by this ImageInfo object.</param>
      <param>An optional description of this image that can be used for tooltips (ie. "Library Folder")</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ImageInfo.Equals(System.Object)">
      <summary>Tests if <paramref name="obj" /> is equal to this ImageInfo object.</summary>
      <param>The object to test for equality.</param>
      <returns>True if <paramref name="obj" /> is equal to this object.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ImageInfo.GetImage">
      <summary>Gets a copy of the image managed by this ImageInfo.</summary>
      <returns>A copy of the image. The caller is reponsible for calling Dispose on the copied image.</returns>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ImageInfo.Description">
      <summary>Gets or sets an optional textual description of this image. This might be used as a tooltip (ie. "Library Folder")</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ImageInfo.Image">
      <summary>Sets the image managed by this ImageInfo.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ImageInfo.Tag">
      <summary>Gets or sets a unique tag that represents this image. The tag should be somewhat human readable as this is the value that will often be store in grid cells for
this image.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumDateConstraint.#ctor(System.Object,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory,System.Int64)">
      <summary>Creates a MaximumDateConstraint object</summary>
      <param>The maximum date that is allowed</param>
      <param>If specified, then this policy only applies to entites that match this category. Passing in Null or
 means the the policy applies to all entities.</param>
      <param>The ID of the property that this policy applies to</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumDateConstraint.#ctor(System.Int64,System.Object,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory,System.Int64)">
      <summary>Creates a MaximumDateConstraint object</summary>
      <param>The unique identifier for this policy</param>
      <param>The maximum date that is allowed</param>
      <param>If specified, then this policy only applies to entites that match this category. Passing in Null or
 means the the policy applies to all entities.</param>
      <param>The ID of the property that this policy applies to</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumDateConstraint.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumDateConstraint)">
      <summary>Creates a copy of a MaximumDateConstraint object</summary>
      <param>The object to copy</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumDateConstraint.ConstraintType">
      <summary>Gets the type of constraint represented by this object.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumDateConstraint.ConstraintValue">
      <summary>Gets or sets the maximum date enforced by this constraint</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumDateConstraint.EntityCategory">
      <summary>Gets the category that this constraint is associated with.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumDateConstraint.ID">
      <summary>Gets the unique identifies of this constraint.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumDateConstraint.PropDefID">
      <summary>Gets the ID of the  that this constraint is associated with</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumDateConstraint.RestrictionMessage">
      <summary>Gets the restriction message of this constraint. If
 returns a value of
<see cref="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyConstraint.ResolveResult.Failed" />, then the RestrictionMessage will contain a
string which describes the failure.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumLengthConstraint.#ctor(System.Object,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory,System.Int64)">
      <summary>Creates a MaximumLengthConstraint object</summary>
      <param>The maximum length that is allowed</param>
      <param>If specified, then this policy only applies to entites that match this category. Passing in Null or
 means the the policy applies to all entities.</param>
      <param>The ID of the property that this policy applies to</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumLengthConstraint.#ctor(System.Int64,System.Object,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory,System.Int64)">
      <summary>Creates a MaximumLengthConstraint object</summary>
      <param>The unique identifier for this policy</param>
      <param>The maximum length that is allowed</param>
      <param>If specified, then this policy only applies to entites that match this category. Passing in Null or
 means the the policy applies to all entities.</param>
      <param>The ID of the property that this policy applies to</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumLengthConstraint.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumLengthConstraint)">
      <summary>Creates a copy of a MaximumLengthConstraint object</summary>
      <param>The object to copy</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumLengthConstraint.ConstraintType">
      <summary>Gets the type of constraint represented by this object.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumLengthConstraint.ConstraintValue">
      <summary>Gets or sets the maximum length enforced by this constraint.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumLengthConstraint.EntityCategory">
      <summary>Gets the category that this constraint is associated with.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumLengthConstraint.ID">
      <summary>Gets the unique identifies of this constraint.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumLengthConstraint.PropDefID">
      <summary>Gets the ID of the  that this constraint is associated with</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumLengthConstraint.RestrictionMessage">
      <summary>Gets the restriction message of this constraint. If
 returns a value of
<see cref="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyConstraint.ResolveResult.Failed" />, then the RestrictionMessage will contain a
string which describes the failure.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumValueConstraint.#ctor(System.Object,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory,System.Int64)">
      <summary>Creates a MaximumValueConstraint object</summary>
      <param>The maximum value that is allowed</param>
      <param>If specified, then this policy only applies to entites that match this category. Passing in Null or
 means the the policy applies to all entities.</param>
      <param>The ID of the property that this policy applies to</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumValueConstraint.#ctor(System.Int64,System.Object,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory,System.Int64)">
      <summary>Creates a MaximumValueConstraint object</summary>
      <param>The unique identifier for this policy</param>
      <param>The maximum value that is allowed</param>
      <param>If specified, then this policy only applies to entites that match this category. Passing in Null or
 means the the policy applies to all entities.</param>
      <param>The ID of the property that this policy applies to</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumValueConstraint.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumValueConstraint)">
      <summary>Creates a copy of a MaximumValueConstraint object</summary>
      <param>The object to copy</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumValueConstraint.ConstraintType">
      <summary>Gets the type of constraint represented by this object.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumValueConstraint.ConstraintValue">
      <summary>Gets or sets the maximum value enforced by this constraint</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumValueConstraint.EntityCategory">
      <summary>Gets the category that this constraint is associated with.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumValueConstraint.ID">
      <summary>Gets the unique identifies of this constraint.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumValueConstraint.PropDefID">
      <summary>Gets the ID of the  that this constraint is associated with</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MaximumValueConstraint.RestrictionMessage">
      <summary>Gets the restriction message of this constraint. If
 returns a value of
<see cref="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyConstraint.ResolveResult.Failed" />, then the RestrictionMessage will contain a
string which describes the failure.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumDateConstraint.#ctor(System.Object,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory,System.Int64)">
      <summary>Creates a MinimumDateConstraint object</summary>
      <param>The minimum date that is allowed</param>
      <param>If specified, then this policy only applies to entites that match this category. Passing in Null or
 means the the policy applies to all entities.</param>
      <param>The ID of the property that this policy applies to</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumDateConstraint.#ctor(System.Int64,System.Object,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory,System.Int64)">
      <summary>Creates a MinimumDateConstraint object</summary>
      <param>The unique identifier for this policy</param>
      <param>The minimum date that is allowed</param>
      <param>If specified, then this policy only applies to entites that match this category. Passing in Null or
 means the the policy applies to all entities.</param>
      <param>The ID of the property that this policy applies to</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumDateConstraint.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumDateConstraint)">
      <summary>Creates a copy of a MinimumDateConstraint object</summary>
      <param>The object to copy</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumDateConstraint.ConstraintType">
      <summary>Gets the type of constraint represented by this object.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumDateConstraint.ConstraintValue">
      <summary>Gets or sets the minimum date enforced by this constraint</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumDateConstraint.EntityCategory">
      <summary>Gets the category that this constraint is associated with.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumDateConstraint.ID">
      <summary>Gets the unique identifies of this constraint.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumDateConstraint.PropDefID">
      <summary>Gets the ID of the  that this constraint is associated with</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumDateConstraint.RestrictionMessage">
      <summary>Gets the restriction message of this constraint. If
 returns a value of
<see cref="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyConstraint.ResolveResult.Failed" />, then the RestrictionMessage will contain a
string which describes the failure.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumLengthConstraint.#ctor(System.Object,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory,System.Int64)">
      <summary>Creates a MinimumLengthConstraint object</summary>
      <param>The minimum length for a text property value</param>
      <param>If specified, then this policy only applies to entites that match this category. Passing in Null or
 means the the policy applies to all entities.</param>
      <param>The ID of the property that this policy applies to</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumLengthConstraint.#ctor(System.Int64,System.Object,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory,System.Int64)">
      <summary>Creates a MinimumLengthConstraint object</summary>
      <param>The unique identifier for this policy</param>
      <param>The minimum length for a text property value</param>
      <param>If specified, then this policy only applies to entites that match this category. Passing in Null or
 means the the policy applies to all entities.</param>
      <param>The ID of the property that this policy applies to</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumLengthConstraint.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumLengthConstraint)">
      <summary>Creates a copy of a MinimumLengthConstraint object</summary>
      <param>The object to copy</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumLengthConstraint.ConstraintType">
      <summary>Gets the type of constraint represented by this object.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumLengthConstraint.ConstraintValue">
      <summary>Gets or sets the value of this constraint.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumLengthConstraint.EntityCategory">
      <summary>Gets the category that this constraint is associated with.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumLengthConstraint.ID">
      <summary>Gets the unique identifies of this constraint.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumLengthConstraint.PropDefID">
      <summary>Gets the ID of the  that this constraint is associated with</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumLengthConstraint.RestrictionMessage">
      <summary>Gets the restriction message of this constraint. If
 returns a value of
<see cref="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyConstraint.ResolveResult.Failed" />, then the RestrictionMessage will contain a
string which describes the failure.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumValueConstraint.#ctor(System.Object,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory,System.Int64)">
      <summary>Creates a MinimumValueConstraint object</summary>
      <param>The minumum value that is allowed</param>
      <param>If specified, then this policy only applies to entites that match this category. Passing in Null or
 means the the policy applies to all entities.</param>
      <param>The ID of the property that this policy applies to</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumValueConstraint.#ctor(System.Int64,System.Object,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory,System.Int64)">
      <summary>Creates a MinimumValueConstraint object</summary>
      <param>The unique identifier for this policy</param>
      <param>The minumum value that is allowed</param>
      <param>If specified, then this policy only applies to entites that match this category. Passing in Null or
 means the the policy applies to all entities.</param>
      <param>The ID of the property that this policy applies to</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumValueConstraint.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumValueConstraint)">
      <summary>Creates a copy of a MinimumValueConstraint object</summary>
      <param>The object to copy</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumValueConstraint.ConstraintType">
      <summary>Gets the type of constraint represented by this object.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumValueConstraint.ConstraintValue">
      <summary>Gets or sets the minimum value enforced by this constraint</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumValueConstraint.EntityCategory">
      <summary>Gets the category that this constraint is associated with.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumValueConstraint.ID">
      <summary>Gets the unique identifies of this constraint.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumValueConstraint.PropDefID">
      <summary>Gets the ID of the  that this constraint is associated with</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.MinimumValueConstraint.RestrictionMessage">
      <summary>Gets the restriction message of this constraint. If
 returns a value of
<see cref="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyConstraint.ResolveResult.Failed" />, then the RestrictionMessage will contain a
string which describes the failure.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyConstraint.Equals(System.Object,System.Boolean)">
      <summary>Determines whether two PropertyConstraints is the same.</summary>
      <param>The object that we are comparing</param>
      <param>If true, then this returns true if the two constraints are the same instance as well as have the same ConstraintValue. If the includeValue parameter is set to
false, then it functions just the same as the 'Equals(object obj)' method.</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyConstraint.ConstraintType">
      <summary>Gets the type of constraint represented by this object.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyConstraint.ConstraintValue">
      <summary>Gets or sets the value of this constraint.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyConstraint.EntityCategory">
      <summary>Gets the category that this constraint is associated with.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyConstraint.ID">
      <summary>Gets the unique identifies of this constraint.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyConstraint.PropDefID">
      <summary>Gets the ID of the  that this constraint is associated with</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyConstraint.RestrictionMessage">
      <summary>Gets the restriction message of this constraint. If
 returns a value of
<see cref="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyConstraint.ResolveResult.Failed" />, then the RestrictionMessage will contain a
string which describes the failure.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.#ctor">
      <summary>Creates a PropertyDefinition object</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.#ctor(System.Int64,System.Collections.Generic.IEnumerable{Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.PropertyDefinitionToEntityClassAssociation},System.String,System.String,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.PropertyDataType,System.Boolean,System.Object,System.Collections.Generic.IList{Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.EnumeratedValue},System.Boolean,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.EBasicSearch)">
      <summary>Creates a PropertyDefinition object</summary>
      <param>The unique identifier for a server-based property definition. This will have a value of
 if this PropertyDefinition is not associated with
a server object.</param>
      <param>The entity classes that this PropertyDefinition is associated with.</param>
      <param>The unique system name associated with this PropertyDefinition</param>
      <param>The display name for this PropertyDefinition</param>
      <param>The data type for this PropertyDefinition</param>
      <param>Whether or not this is a System property definition (as opposed to a User Defined property definition)</param>
      <param>The value that should be used for entities that do not have a specific value for this PropertyDefinition</param>
      <param>An optional List Of Values that should be associated with this PropertyDefinition</param>
      <param>Whether or not this PropertyDefinition is active. If False, then properties associated with this definition should not be displayed in an application</param>
      <param>Whether or not this PropertyDefinition can take part in Basic Search operations</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.#ctor(System.Int64,System.Collections.Generic.List{Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.PropertyDefinitionToEntityClassAssociation},System.String,System.String,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.PropertyDataType)">
      <summary>Creates a PropertyDefinition object</summary>
      <param>The unique identifier for a server-based property definition. This will have a value of
 if this PropertyDefinition is not associated with
a server object.</param>
      <param>The entity classes that this PropertyDefinition is associated with.</param>
      <param>The unique system name associated with this PropertyDefinition</param>
      <param>The display name for this PropertyDefinition</param>
      <param>The data type for this PropertyDefinition</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.#ctor(System.String)">
      <summary>Creates a PropertyDefinition object</summary>
      <param>The unique system name associated with this PropertyDefinition</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition)">
      <summary>Creates a copy of a PropertyDefinition object</summary>
      <param>The PropertyDefinition to copy</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.AddDependency(Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition)">
      <summary>Makes this Property Definitions value dependent on the value of another Property Definition</summary>
      <param>The property definition that this depends on</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.Equals(System.Object)">
      <summary>Tests if <paramref name="obj" /> is equal to this PropertyDefinition object.</summary>
      <param>The object to test for equality.</param>
      <returns>True if <paramref name="obj" /> is equal to this object.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.GetDoNotCache(System.String)">
      <summary>Gets whether the framework can cache property values associated with this Property Definition</summary>
      <param>The name of the entity class that the cache status applies to. A property can have a different cache algorithm for each entity class.</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.GetRetrievalAlgorithm(System.String)">
      <summary>Gets the Retrieval Algorithm for this PropertyDefinition, which is used to determine how property values for this definition should be retrieved.</summary>
      <param>The name of the entity class that we are retrieving the algorithm for. A property can have a different retrieval algorithm for each entity class.</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.SetDoNotCache(System.Boolean,System.String)">
      <summary>Sets whether the framework can cache property values associated with this Property Definition</summary>
      <param>The cache algorithm that is assigned to the PropertyDefinition</param>
      <param>The optional name of the entity class that this cache algorithm applies to. If null, then this will apply to all entity classes. There are properties that have
different cache algorithms for each entity class</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.SetRetrievalAlgorithm(Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.ERetrievalAlgorithm,System.String)">
      <summary>Gets the Retrieval Algorithm for this PropertyDefinition, which is used to determine how property values for this definition should be retrieved.</summary>
      <param>The algorithm that is assigned to the PropertyDefinition</param>
      <param>The optional name of the entity class that this algorithm applies to. If null, then this will apply to all entity classes. There are properties that have
different retrieval algorithms for each entity class</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.SupportsEntityClass(System.String)">
      <summary>Gets whether or not this PropertyDefinition is associated with the specified entity class</summary>
      <param>The name of the entity class that we are testing</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.Active">
      <summary>Gets or sets whether this Property Definition is Active. Inactive PropertyDefinitions and the associated PropertyValues should not be displayed in any GUI</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.AssociatedEntityClasses">
      <summary>Gets the list of Entity Classes that use this PropertyDefinition</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.BasicSearch">
      <summary>Gets whether or not this Property Definition can take part in a Basic Search operation</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.Constraints">
      <summary>Gets or sets the constraints associated with this Property Definition.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.DataType">
      <summary>Gets or sets the Data Type for this Property Definition</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.DefaultValue">
      <summary>Gets or sets the Default Value that will be applied to entities that do not have an existing value associated with this PropertyDefinition</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.Dependencies">
      <summary>Gets a list of PropertyDefinitions that this PropertyDefinition depends on to compute it's value. This can be used for internal PropertyDefinitions where the
value for one property is computed based on the value of another</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.DisplayName">
      <summary>Gets or sets the Display name for this Property Definition</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.HasListValues">
      <summary>Gets whether or not this PropertyDefinition has a List Of Values associated with it</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.Id">
      <summary>Gets the unique identifier for a server-based property definition. This will have a value of
 if this PropertyDefinition is not associated with
a server object.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.IsCalculated">
      <summary>Gets if this propdef represents properties that are calculated on the client (not based on any server entity, in which case the systemName is the point of
uniqueness)</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.IsDynamic">
      <summary>Gets or sets whether or not this propdef is dynamic. A "dynamic propdef" is one whose value can change for any one iteration of an entity (regardless of tip or
not). For example, the file compliance property is dynamic in that compliance can change within one iteration of a file.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.IsSearchable">
      <summary>Gets or sets if this property is searchable via Advanced Search. The default value is that all non-internal properties are searchable.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.IsSystem">
      <summary>Gets whether or not this is a System Property (as opposed to a UserDefined property)</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.ManagedDataType">
      <summary>Gets the .NET data type that will be returned from

for properties associated with this PropertyDefinition</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.Mappings">
      <summary>Gets or sets the mappings for this Property Definition. The mappings determine the relationship between PropertyDefinitions and
's</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.SystemName">
      <summary>Gets the System Name for this Property Definition.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.ValueList">
      <summary>Gets or sets an array of possible values for properties based on this property definition.</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.InternalPropertyId">
      <summary>The value of PropertyDefinitions that are not associated with a server object</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.DefinitionConstraints.#ctor">
      <summary>Creates a DefinitionConstraints object</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.DefinitionConstraints.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.DefinitionConstraints)">
      <summary>Creates a copy of a DefinitionConstraints object</summary>
      <param>The object to copy</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.DefinitionConstraints.Add(Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyConstraint)">
      <summary>Adds a specific constraint to this Property Definition.</summary>
      <param>the constraint to add</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.DefinitionConstraints.Clear">
      <summary>Removes all constraints from this </summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.DefinitionConstraints.GetAllConstraints">
      <summary>Retrieves a flat list of all of the constraint objects associated with a
</summary>
      <returns>A list of constraint objects</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.DefinitionConstraints.Remove(Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyConstraint)">
      <summary>Removes a specific constraint from the Property Definition</summary>
      <param>the constraint to remove</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.DefinitionConstraints.Find(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyConstraintType,System.Boolean)">
      <summary>Retrieves a specific constraint type for a specific category (ie. Fetch the MinimumLength restriction for the Assemblies category)</summary>
      <param>The category to retrieve the constraint for</param>
      <param>The constraint type to retrieve a value for</param>
      <param>If true and there is no constraint for the specified category, we will return the constraint on the PropertyDefinition itself</param>
      <returns>The PropertyConstraint object, or null if not found</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.DefinitionConstraints.Find(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyConstraintType)">
      <summary>Retrieves a specific constraint type for a specific category (ie. Fetch the MinimumLength restriction for the Assemblies category)</summary>
      <param>The category to retrieve the constraint for</param>
      <param>The constraint type to retrieve a value for</param>
      <returns>The PropertyConstraint object, or null if not found</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.EnumeratedValue.#ctor(System.Object)">
      <summary>Creates an EnumeratedValue object</summary>
      <param>The value that will be presented to the user</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.EnumeratedValue.#ctor(System.Object,System.String)">
      <summary>Creates an EnumeratedValue object</summary>
      <param>The underlying data value that will be sent to the server</param>
      <param>The value that will be presented to the user</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.EnumeratedValue.Display">
      <summary>Gets or sets the value that is displayed to the user in a combobox or other ui</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition.EnumeratedValue.Value">
      <summary>Gets or sets the value of this object. This is typically a value that is searched on, but not necessarily displayed to the user</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionDictionary.#ctor">
      <summary>Creates an instance of a PropertyDefinitionDictionary</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionDictionary.Clear">
      <summary>Removes all of the contents of the dictionary</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionDictionary.Contains(System.Collections.Generic.KeyValuePair{System.String,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition})">
      <summary>Tests if a PropertyDefinition is in the dictionary by a Key-Value Pair</summary>
      <param>A key-value pair where the Key is the  and the Value is the
PropertyDefinition</param>
      <returns>True if the PropertyDefinition is in the dictionary. False otherwise.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionDictionary.TryGetValue(System.String,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition@)">
      <summary>Tries to retrieve a PropertyDefinition from the dictionary by it's
</summary>
      <param>The system name of the PropertyDefinition to retrieve.</param>
      <param>The returned PropertyDefinition object if a match was found</param>
      <returns>True if the PropertyDefinition was found. False otherwise.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionDictionary.Add(System.String,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition)">
      <summary>Adds a PropertyDefinition to the dictionary</summary>
      <param>The unique system name of the PropertyDefinition</param>
      <param>The PropertyDefinition to add</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionDictionary.Add(System.Collections.Generic.IEnumerable{Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition})">
      <summary>Adds a collection of PropertyDefinition's to the dictionary</summary>
      <param>The collection of PropertyDefinition objects to add</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionDictionary.Add(System.Collections.Generic.KeyValuePair{System.String,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition})">
      <summary>Adds a PropertyDefinition to the dictionary by a Key-Value Pair</summary>
      <param>A key-value pair where the Key is the  and the Value is the
PropertyDefinition</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionDictionary.Add(Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition)">
      <summary>Adds a PropertyDefinition to the dictionary</summary>
      <param>The PropertyDefinition to add</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionDictionary.ContainsKey(System.String)">
      <summary>Tests if a PropertyDefinition with the specified  exists in
the dictionary.</summary>
      <param>The system name to lookup</param>
      <returns>True if a PropertyDefinition with the <paramref name="systemName" /> is in the dictionary. False otherwise.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionDictionary.ContainsKey(System.Int64)">
      <summary>Tests if a PropertyDefinition with the specified  exists in the
dictionary.</summary>
      <param>The ID to lookup</param>
      <returns>True if a PropertyDefinition with the <paramref name="id" /> is in the dictionary. False otherwise.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionDictionary.Remove(System.String)">
      <summary>Removes a PropertyDefinition from the dictionary by it's </summary>
      <param>The system name of the PropertyDefinition to remove</param>
      <returns>True if the PropertyDefinition was successfully remove. False otherwise</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionDictionary.Remove(System.Int64)">
      <summary>Removes a PropertyDefinition from the dictionary by it's </summary>
      <param>The id of the PropertyDefinition to remove</param>
      <returns>True if the PropertyDefinition was successfully remove. False otherwise</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionDictionary.Remove(System.Collections.Generic.KeyValuePair{System.String,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition})">
      <summary>Removed a PropertyDefinition from the dictionary by Key-Value Pair</summary>
      <param>A key-value pair where the Key is the  and the Value is the
PropertyDefinition</param>
      <returns>True if the PropertyDefinition has been removed. False otherwise.</returns>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionDictionary.Count">
      <summary>Gets the number of PropertyDefinitions stored in the dictionary.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionDictionary.Item(System.String)">
      <summary>Gets PropertyDefinition from the dictionary, or inserts a PropertyDefinition into the dictionary, by it's
</summary>
      <param>The system name of the PropertyDefinition to retrieve.</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionDictionary.Keys">
      <summary>Gets all of the  keys in the dictionary</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionDictionary.Values">
      <summary>Gets all of the PropertyDefinition values stored in the dictionary</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionDictionary.Item(System.String)">
      <summary>Gets PropertyDefinition from the dictionary, or inserts a PropertyDefinition into the dictionary, by it's
</summary>
      <param>The system name of the PropertyDefinition to retrieve.</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionDictionary.Item(System.Int64)">
      <summary>Gets PropertyDefinition from the dictionary, or inserts a PropertyDefinition into the dictionary, by it's
</summary>
      <param>The ID of the PropertyDefinition to retrieve.</param>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Client.EntityClass">
      <summary>The display name of the  associated an entity</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Client.EntityClassID">
      <summary>The unique id of the  associated with an entity</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Client.EntityDescription">
      <summary>A textual description of the entity (ie "Folder", "Library Folder", "Office Document")</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Client.EntityIcon">
      <summary>The small 16x16 icon associated with an entity</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Client.EntityPath">
      <summary>The path (folder) that the entity lives in</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Client.FolderCreateDate">
      <summary>Folder creation date</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Client.FullPath">
      <summary>The full path (folder) that the entity lives in (including the entity name)</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Client.HasAttachments">
      <summary>A property that indicates whether or not an entity has any attachments</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Client.LinkTargetPath">
      <summary>For linked objects, this is the path of the entity that the link points to (not the directory that the link lives in)</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Client.VaultStatus">
      <summary>A small 16x16 icon that indicates the status of a local file relative to its related vault file.</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Client.VaultStatusModifier">
      <summary>A small 16x16 icon that indicates the status of a local file relative to its related vault file.</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.Author">
      <summary>The "Author" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.CategoryGlyph">
      <summary>The "CategoryGlyph" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.CategoryGlyphVer">
      <summary>The "CategoryGlyph(Ver)" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.CategoryName">
      <summary>The "CategoryName" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.CategoryNameVer">
      <summary>The "CategoryName(Ver)" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.ChangeOrderReviewStatus">
      <summary>The Change Order "ReviewStatus" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.ChangeOrderState">
      <summary>The "ChangeOrderState" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.CheckInDate">
      <summary>The "CheckInDate" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.CheckOutLocalSpec">
      <summary>The "CheckoutLocalSpec" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.CheckOutUserName">
      <summary>The "CheckoutUserName" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.Classification">
      <summary>The "Classification" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.Comment">
      <summary>The "Comment" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.ControlledByCO">
      <summary>The "ControlledByChangeOrder" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.CONumFileAttachments">
      <summary>The "NumFileAttachments" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.CreateUserName">
      <summary>The "CreateUserName" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.Description">
      <summary>The "Description" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.EntityName">
      <summary>The "Name" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.FileCompliance">
      <summary>The "Compliance" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.FileComplianceVer">
      <summary>The "Compliance(Ver)" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.FileName">
      <summary>The "ClientFileName" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.FileReplicated">
      <summary>The "FileReplicated" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.FileSize">
      <summary>The "FileSize" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.FileStatus">
      <summary>The "Status" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.FolderPath">
      <summary>The "FolderPath" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.Hidden">
      <summary>The "Hidden" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.ILogicRuleStatus">
      <summary>The "iLogicRuleStatus" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.ItemDescription">
      <summary>The "Item Description(Item,CO)" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.ItemEffectivity">
      <summary>The Item "ItemEffectivity" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.ItemEffectivityEndDate">
      <summary>The Item "ItemEffectivityEnd" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.ItemEffectivityStartDate">
      <summary>The Item "ItemEffectivityStart" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.ItemEquivalence">
      <summary>The Item "Equivalence" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.ItemLinked">
      <summary>The "ItemLinked" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.ItemNumber">
      <summary>The Item "Number" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.ItemTitle">
      <summary>The Item "Title(Item,CO)" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.ItemUnits">
      <summary>The Item "Units" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.Keywords">
      <summary>The "Keywords" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.LatestReleasedRevision">
      <summary>The "LatestReleasedRevision" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.LatestVersion">
      <summary>The "LatestVersion" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.LifeCycleDefinition">
      <summary>The "LifeCycleDefinition" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.LifeCycleDefinitionVersion">
      <summary>The "LifeCycleDefinition(Ver)" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.LifeCycleState">
      <summary>The "State" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.LifeCycleStateVersion">
      <summary>The "State(Ver)" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.ModifiedDate">
      <summary>The "ModDate" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.NumAttachments">
      <summary>The "NumManualAttachments" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.ReleasedRevision">
      <summary>The "ReleasedRevision" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.ReplicationCurrentOwner">
      <summary>The "ReplicationCurrentOwner" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.ReplicationLeasedUntil">
      <summary>The "ReplicationLeasedUntil" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.Revision">
      <summary>The "Revision" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.ThumbnailSystem">
      <summary>The "Thumbnail" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.Title">
      <summary>The "Title" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.TitleItemCO">
      <summary>The "Title(Item,CO)" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.VersionNumber">
      <summary>The "VersionNumber" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.VisualizationAttachment">
      <summary>The "VisualizationAttachment" property</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionIds.Server.VisualizationCompliance">
      <summary>The "VisualizationCompliance" property</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyMappings.#ctor">
      <summary>Creates an instance of a PropertyMappings object</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyMappings.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyMappings)">
      <summary>Creates a copy of a PropertyMappings object</summary>
      <param>The object to copy</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyMappings.AddMapping(System.String,System.String,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourcePropertyMapping)">
      <summary>Adds a property mapping to a PropertyDefinition</summary>
      <param>The ID of the Entity Class to create a mapping for</param>
      <param>The unique id of the Content Source Provider that we are mapping with (ie. an Inventor provider would be used to create a mapping to .IPT files)</param>
      <param>The individual mapping to be applied to this PropertyDefinition</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyMappings.GetAllContentSourcePropertyMapping">
      <summary>Gets all of the property mappings associated with a PropertyDefinition</summary>
      <returns>A list of property mappings. The list will be empty if there are no mappings</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyMappings.GetContentSourcePropertyMappings(System.String,System.String)">
      <summary>Gets a list of all of the property mappings to the PropertyDefinition for a particular type of entity and
</summary>
      <param>The ID of the Entity Class to retrieve mappings for</param>
      <param>The unique id of the Content Source Provider that we are retrieving mapping for</param>
      <returns>A list of all of the mappings that meet the specified criteria. An empty list is returned if there are no matches.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyMappings.GetContentSourcePropertyMappingsGroupedByProvider(System.String,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyMappings.DesiredMappingTypes)">
      <summary>Gets a dictionary of mappings to this PropertyDefinition grouped by
</summary>
      <param>The ID of the Entity Class to retrieve mappings for</param>
      <param>The type of mappings to retrieve</param>
      <returns>A dictionary of mappings that meet the specified criteria. The key to the dictionary is the ContentSourceProvider ID, and the value is list of mappings that
belong to that provider</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyMappings.RemoveMappings(System.String)">
      <summary>Removed all mappings for a specific type of  (ie. remove all FILE mappings)</summary>
      <param>The ID of the Entity Class to remove mappings for</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyMappings.HasDefualtValueMappings">
      <summary>Gets whether or not there are any Default Value mappings</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyMappings.HasMappings">
      <summary>Gets whether or not there are any mappings</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValue.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition,System.Object)">
      <summary>Creates an instance of a PropertyValue object</summary>
      <param>The entity that this value applies to</param>
      <param>The PropertyDefinition that the value applies to</param>
      <param>The actual property value</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValue.Clone">
      <summary>Creates a copy of this property value</summary>
      <returns>A copy of this property value</returns>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValue.Entity">
      <summary>Gets the entity that this value is associated with</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValue.PropertyDefinition">
      <summary>Gets the PropertyDefinition that the value is associated with</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValue.Value">
      <summary>Gets or sets the actual property value</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValues.GetNumberOfValues">
      <summary>Gets the number of property values stored in this collection</summary>
      <returns>The number of property values stored in this collection</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValues.GetValue(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition)">
      <summary>Gets a property value from this collection for a specific entity and property definition</summary>
      <param>The Entity to retrieve a value for</param>
      <param>The PropertyDefinition to retrieve a value for</param>
      <returns>The property value associated with the entity and PropertyDefinition. Null is returned if there is no value stored in this collection</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValues.MergeValues(Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValues)">
      <summary>Copies the values from one PropertyValues collection into this collection</summary>
      <param>The values to copy into this collection</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValues.RemoveValue(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition)">
      <summary>Removes a property value from this collection</summary>
      <param>The entity representing the property value to remove</param>
      <param>The PropertyDefinition associated with thte property value to remove</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValues.GetValues(Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition)">
      <summary>Gets all of the property values in this collection for a particular PropertyDefinition</summary>
      <param>The PropertyDefinition to retrieve values for</param>
      <returns>A dictionary containing all of the values for the specified PropertyDefinition. The key to the dictionary is the entity that the value belongs to, and the
value of the dictionary is the actual property value</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValues.GetValues(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity)">
      <summary>Gets all of the property values in this collection for a particular entity</summary>
      <param>The entity to retrieve values for</param>
      <returns>A dictionary containing all of the values for the specified entity. The key to the dictionary is the PropertyDefinition that the value belongs to, and the
value of the dictionary is the actual property value</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValues.GetValues">
      <summary>Gets all of the property values stored in this collection</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValues.SetValue(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition,System.Object)">
      <summary>Adds a property value to this collection.</summary>
      <param>The entity that the property value belongs to</param>
      <param>The PropertyDefinition that the value is associated with</param>
      <param>The actual property value</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValues.SetValue(Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValue)">
      <summary>Adds a property value to this collection</summary>
      <param>The property value to add</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValues.Entities">
      <summary>Gets a list of all of the entities that have values in this collection</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValueSettings.#ctor">
      <summary>Creates an instance of a PropertyValueSettings object</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValueSettings.AddPropertyExtensionProvider(Autodesk.DataManagement.Client.Framework.Vault.Interfaces.IPropertyExtensionProvider)">
      <summary>Configures an additional property provider to be used in the property retrieval pipeline during a

request</summary>
      <param>The provider to add to the pipeline</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValueSettings.GetPropertyIconSize(System.String)">
      <summary>Gets the icon size that should be retrieved for a particular property</summary>
      <param>The  of the property to get the icon isze for</param>
      <returns>The icon size. If no icon size has been set for <paramref name="propertySystemName" /> then a default icon size will be returned</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValueSettings.SetPropertyIconSize(System.String,Autodesk.DataManagement.Client.Framework.Currency.ShellIconSize)">
      <summary>Sets the size of the icon that should be returned for a particular property</summary>
      <param>The  of the property to set the icon isze for</param>
      <param>The icon size that should be returned</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValueSettings.UseLargeVaultStatusIcons">
      <summary>Gets or sets if vault status icons should be large or small.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.RequiresValueConstraint.#ctor(System.Object,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory,System.Int64)">
      <summary>Creates a RequiresValueConstraint object</summary>
      <param>A bool indicating whether or not a property value is required</param>
      <param>If specified, then this policy only applies to entites that match this category. Passing in Null or
 means the the policy applies to all entities.</param>
      <param>The ID of the property that this policy applies to</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.RequiresValueConstraint.#ctor(System.Int64,System.Object,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory,System.Int64)">
      <summary>Creates a RequiresValueConstraint object</summary>
      <param>The unique identifier for this policy</param>
      <param>A bool indicating whether or not a property value is required</param>
      <param>If specified, then this policy only applies to entites that match this category. Passing in Null or
 means the the policy applies to all entities.</param>
      <param>The ID of the property that this policy applies to</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.RequiresValueConstraint.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.RequiresValueConstraint)">
      <summary>Creates a copy of a RequiresValueConstraint object</summary>
      <param>The object to copy</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.RequiresValueConstraint.ConstraintType">
      <summary>Gets the type of constraint represented by this object.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.RequiresValueConstraint.ConstraintValue">
      <summary>Gets or sets whether or not property values are required</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.RequiresValueConstraint.EntityCategory">
      <summary>Gets the category that this constraint is associated with.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.RequiresValueConstraint.ID">
      <summary>Gets the unique identifies of this constraint.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.RequiresValueConstraint.PropDefID">
      <summary>Gets the ID of the  that this constraint is associated with</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.RequiresValueConstraint.RestrictionMessage">
      <summary>Gets the restriction message of this constraint. If
 returns a value of
<see cref="F:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyConstraint.ResolveResult.Failed" />, then the RestrictionMessage will contain a
string which describes the failure.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ThumbnailInfo.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ThumbnailInfo.ThumbnailType,System.Byte[])">
      <summary>Creates an instance of a ThumbnailInfo object</summary>
      <param>The type of image managed by this ThumbnailInfo</param>
      <param>The byte representation of the thumbnail image</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ThumbnailInfo.GetImage">
      <summary>Gets a copy of the thumbnail represented as an Image object.</summary>
      <returns>A thumbnail in Image format. The caller is responsible for disposing the image when done.</returns>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ThumbnailInfo.Image">
      <summary>Gets or sets the thumbnail image.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ThumbnailInfo.Source">
      <summary>Gets or sets the type of thumbnail image.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Restrictions.FileAcquisitionRestriction.RestrictionCode">
      <summary>Describes the type of restriction</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Restrictions.EntityRestriction">
      <summary>Extension of  that contains the
<see cref="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity" /> affected by the restriction.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Restrictions.FileAcquisitionRestriction">
      <summary>This is a specialized  that can occur during an attempt to acquire
(get or checkout) a file from the vault.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Restrictions.EntityRestriction.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity,System.String)">
      <summary>Constructor for the EntityRestriction class.</summary>
      <param>The affected entity</param>
      <param>The description of the reason for the restriction</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Restrictions.EntityRestriction.Entity">
      <summary>The entity affected by this restriction.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Restrictions.EntityRestriction.IsOverrideable">
      <summary>Gets if the resriction can be overridden by the user.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Restrictions.EntityRestriction.RestrictedObjectName">
      <summary>Gets the name of the object that this restriction affects.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Restrictions.EntityRestriction.RestrictionText">
      <summary>Gets the reson for this restriction.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Currency.Restrictions.FileAcquisitionRestriction.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration,System.Boolean,Autodesk.DataManagement.Client.Framework.Currency.FilePathAbsolute,Autodesk.DataManagement.Client.Framework.Vault.Currency.Restrictions.FileAcquisitionRestriction.RestrictionCode,System.String)">
      <summary>Constructs an instance of the FileAcquisitionRestriction object</summary>
      <param>The file that this restriction applies to</param>
      <param>True if this restriction is related to checking out a file. Otherwise, it is assumed to be download related</param>
      <param>The physical location on the file on disk</param>
      <param>Describes the type of restriction</param>
      <param>A human readable description of the reason for the failure.</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Restrictions.FileAcquisitionRestriction.Code">
      <summary>Gets the type of restriction</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Restrictions.FileAcquisitionRestriction.DownloadPath">
      <summary>Gets the physical location on the file on disk</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Restrictions.FileAcquisitionRestriction.Entity">
      <summary>The entity affected by this restriction.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Restrictions.FileAcquisitionRestriction.File">
      <summary>Gets the file that this restriction applies to</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Restrictions.FileAcquisitionRestriction.IsCheckOutRestriction">
      <summary>Gets whether the file is a part of a checkout operation</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Restrictions.FileAcquisitionRestriction.IsOverrideable">
      <summary>Gets if the resriction can be overridden by the user.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Restrictions.FileAcquisitionRestriction.Reason">
      <summary>Gets a human readable description of the reason for the failure</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Restrictions.FileAcquisitionRestriction.RestrictedObjectName">
      <summary>Gets the name of the object that this restriction affects.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Restrictions.FileAcquisitionRestriction.RestrictionText">
      <summary>Gets the reson for this restriction.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Interfaces.ICommittableResult">
      <summary>Represent a result that can be committed</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Interfaces.ICopyDesignEntityOperationProvider">
      <summary>This interface provides a set of Copy Design specefic operations that can be generically executed for any
.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Interfaces.IEntityOperationProvider">
      <summary>This interface provides a set of operations that can be generically executed for any
. This allows for the writing of business logic that isn't file, or item, or folder
specific. An implementation of this interface represents a particular Entity class</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Interfaces.IPropertyExtensionProvider">
      <summary>This interface provides custom handling for property related operations.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Interfaces.ICommittableResult.Commit">
      <summary>Commits the result</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Interfaces.ICopyDesignEntityOperationProvider.GetRelationships(Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection,System.Collections.Generic.IEnumerable{Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity},Autodesk.DataManagement.Client.Framework.Vault.Currency.CopyDesign.CopyDesignRelationshipGatheringOptions,System.Threading.CancellationToken)">
      <summary>Get the entity relationships that are appropriate for Copy Design based on the gathering options.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Interfaces.IEntityOperationProvider.CanBrowseChildren(Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity)">
      <summary>Determines if the specified entity is browsable. This just means that it CAN have children, not that it necessarily does</summary>
      <param>A connection to a vault server</param>
      <param>The entity to retrieve children for</param>
      <returns>True of the entity can have children. False otherwise</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Interfaces.IEntityOperationProvider.CanDownloadOrCheckoutEntities(Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection,System.Collections.Generic.IEnumerable{Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity})">
      <summary>Determines if the list of entities are eligible for the Get/Checkout workflow.</summary>
      <param>A connection to a vault server</param>
      <param>List of entities to evaluate</param>
      <returns>True if every entity is eligible. False if at least one isnt</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Interfaces.IEntityOperationProvider.ConvertEntInfosToEntities(Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection,System.Collections.Generic.IEnumerable{Autodesk.DataManagement.Client.Framework.Vault.Currency.PersistentId.PersistableIdEntInfo})">
      <summary>Converts a list of PersistableIdEntInfo objects to the IEntity implementation of the entities that they represent</summary>
      <param>A connection to a vault server</param>
      <param>A list of PersistableIdEntInfo objects to convert</param>
      <returns>A dictionary mapping a PersistableIdEntInfo to the Entity that it points to</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Interfaces.IEntityOperationProvider.ConvertLinksToEntities(Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection,System.Collections.Generic.IEnumerable{Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Link})">
      <summary>Converts a list of links to the entities that they point to</summary>
      <param>A connection to a vault server</param>
      <param>A list of links to convert</param>
      <returns>A dictionary mapping a the link to the Entity that the link points to</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Interfaces.IEntityOperationProvider.GetBrowseChildren(Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity,System.Boolean)">
      <summary>Gets all of the children for a specific entity for a browse workflow. A Browse Vault workflow is one where the user is presented with a list of entities, and
they double click on the entity to drill down to it's children. A folder would return a list of child folders, files and links. An Assembly file may return a
list of all parts for that assembly.</summary>
      <param>A connection to a vault server</param>
      <param>The entity to retrieve children for</param>
      <param>Whether to include hidden children in the returned results.</param>
      <returns>A list of children of <paramref name="entity" /></returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Interfaces.IEntityOperationProvider.GetParent(Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity)">
      <summary>Gets the parent of an entity.</summary>
      <param>A connection to a vault server</param>
      <param>The entity to retrieve the parent for</param>
      <returns>The parent of <paramref name="entity" /> or Null if there is no parent</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Interfaces.IEntityOperationProvider.GetSelectableRevisions(Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity)">
      <summary>Gets a list of revisions that can be selected by a user for a given entity</summary>
      <param>A connection to a vault server</param>
      <param>The entity to retrieve the revisions for</param>
      <returns>A list of information about available revisions for the specified entity</returns>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Interfaces.IEntityOperationProvider.SupportedEntityClasses">
      <summary>Gets a list of Entity class IDs for the entity classes that are provided
for by this Provider</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Interfaces.IPropertyExtensionProvider.DecoratePropertyDefinitions(Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionDictionary)">
      <summary>This method allows the characteristics of any of the Property Definitions to be modified. For example, you can add a dependency between multiple property
definitions.</summary>
      <param>A connection to a vault server</param>
      <param>The set of property definitions that have already been gathered by other extension providers</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Interfaces.IPropertyExtensionProvider.GetCustomPropertyDefinitions(Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionDictionary)">
      <summary>Gets a list of all custom property definitions. These are Property Definitions that are created on the client that represent computed values or values that are
custom to a particular use case.</summary>
      <param>A connection to a vault server</param>
      <param>The set of property definitions that have already been gathered by other extension providers</param>
      <returns>A list of custom PropertyDefinitions, or null if there are none.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Interfaces.IPropertyExtensionProvider.PostGetPropertyValues(Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection,System.Collections.Generic.IEnumerable{Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity},Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionDictionary,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValues,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValueSettings)">
      <summary>This method is called to retrieve the values for any property definitions after the server is called to retrieve property values. This can be used to either
compute a value based on another property value that was already retrieved or to modify a value that was returned from the server</summary>
      <param>A connection to a vault server</param>
      <param>The list of entity objects (ie. FileIterations, ItemRevisions) that we are retrieving values for</param>
      <param>The list of propdefs that we are retrieving values for.</param>
      <param>The results object to store any valued computed in the implementation of this method. This also contains any values already computed by other providers.</param>
      <param>Optional settings which configure how some property values should be computed.</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Interfaces.IPropertyExtensionProvider.PreGetPropertyValues(Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection,System.Collections.Generic.IEnumerable{Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity},Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionDictionary,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValues,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValueSettings)">
      <summary>This method is called to retrieve the values for any property definitions before we call the server. This can be used to provide values for Custom Property
Definitions, or in cases where the value can be pulled directly off of the entity object and thus there is no reason to ask the server (ie. a FileIteration
entity object already knows it's name, so there is no need to ask the server for the FileName property value)</summary>
      <param>A connection to a vault server</param>
      <param>The list of entity objects (ie. FileIterations, ItemRevisions) that we are retrieving values for</param>
      <param>The list of propdefs that we are retrieving values for.</param>
      <param>The results object to store any valued computed in the implementation of this method. This also contains any values already computed by other providers.</param>
      <param>Optional settings which configure how some property values should be computed.</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Interfaces.IPropertyExtensionProvider.SupportedEntityClasses">
      <summary>Gets a list of entity classes supported by this provider.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Interfaces.ExtensionHandlers.IExtensionHandlerProvider">
      <summary>This is the base interface for all types of extension handlers.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Interfaces.ExtensionHandlers.IFileReferencesProvider">
      <summary>This interface is used to manipulate file references in physical files on disk</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Interfaces.ExtensionHandlers.IFileReferencesProvider.UpdateReferences(Autodesk.DataManagement.Client.Framework.Vault.Models.ExtensionHandlers.UpdateFileReferencesModel)">
      <summary>Updates the references in a physical file.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Models.UpdateFileReferencesModel">
      <summary>This model manages the workflow of updating file references for one or more files.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Models.UpdateFileReferencesModel.#ctor">
      <summary>Constructs and instance of the UpdateFileReferencesModel class</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Models.UpdateFileReferencesModel.SetTargetFilePath(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration,Autodesk.DataManagement.Client.Framework.Currency.FilePathAbsolute)">
      <summary>Sets the location on disk where a vault file lives (or will be downloaded to)</summary>
      <param>The vault file that we are setting the location for</param>
      <param>The full path on disk where the vault file lives, including both the path and the file name</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Models.UpdateFileReferencesModel.UpdateRefsInLocalFile(Autodesk.DataManagement.Client.Framework.Currency.FilePathAbsolute,System.Collections.Generic.IEnumerable{Autodesk.DataManagement.Client.Framework.Vault.Currency.FileSystem.FileReference},Autodesk.DataManagement.Client.Framework.Vault.Currency.FileSystem.FileIdentity.Identity)">
      <summary>Updates a local file's references.</summary>
      <param>The local path of the file to update.</param>
      <param>The references within the file that need updating.</param>
      <param>The identity of the file being updated. This includes the checksum of the file before file references udpates and the original create and modification times.</param>
      <returns>The results of updating the file references</returns>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Models.UpdateFileReferencesModel.FileOpened">
      <summary>Whether or not the file is already opened by the parent application.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Models.UpdateFileReferencesModel.ForceUpdateOfTargetFilePaths">
      <summary>Gets or sets whether references for all configured Target File Paths will be written back to the source file regardless of whether or not the references are
out of date</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Models.UpdateFileReferencesModel.Properties">
      <summary>Gets an optional bag of properties which can be used by the handler to determine how it should interpret the references</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Models.UpdateFileReferencesModel.UpdateVaultStatus">
      <summary>Gets or sets whether or not the meta data used to compute the Vault File Status will be updated along with the file references</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Models.ExtensionHandlers.UpdateFileReferencesModel">
      <summary>This model manages the workflow of updating file references via

for a specific type of file</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Models.ExtensionHandlers.UpdateFileReferencesModel.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection,Autodesk.DataManagement.Client.Framework.Currency.FilePathAbsolute,System.Collections.Generic.IEnumerable{Autodesk.DataManagement.Client.Framework.Vault.Currency.FileSystem.FileReference},System.Boolean,System.Collections.Generic.IDictionary{System.String,System.Object})">
      <summary>Constructs a UpdateFileReferencesModel object</summary>
      <param>Connection to a vault server</param>
      <param>The file that is being updated</param>
      <param>The references to update in the <paramref name="sourceFile" /></param>
      <param>True if the file is already opened by the parent application and therefore, the handler does not need to open or close it</param>
      <param>An optional bag of properties which can be used by the handler to determine how it should interpret the references</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Models.ExtensionHandlers.UpdateFileReferencesModel.GetReference(System.String)">
      <summary>Gets a specific reference from the model</summary>
      <param>The value that uniquely identifies the reference</param>
      <returns>A  object, or null if there is no matching reference</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Models.ExtensionHandlers.UpdateFileReferencesModel.GetStatus(Autodesk.DataManagement.Client.Framework.Vault.Currency.FileSystem.FileReference)">
      <summary>Gets the update status of a file reference.</summary>
      <param>The file reference to get the status of.</param>
      <returns>The current status of the file reference.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Models.ExtensionHandlers.UpdateFileReferencesModel.GetUnprocessedReferences">
      <summary>Gets a list of all references that have not yet been processed</summary>
      <returns>A list of references</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Models.ExtensionHandlers.UpdateFileReferencesModel.SetUpdateReferenceResult(System.String,Autodesk.DataManagement.Client.Framework.Vault.Currency.FileSystem.FileReferenceUpdateStatus.UpdateStatus,System.String)">
      <summary>Updates the model with the results of how a particular reference was updated</summary>
      <param>A value that uniquely identifies the reference that was updated</param>
      <param>A value that determines how the reference was updated</param>
      <param>An optional description of how the reference was updated, or errors that occurred during the update</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Models.ExtensionHandlers.UpdateFileReferencesModel.FileOpened">
      <summary>Gets whether or not the file is already opened by the parent application. If so, extension handler does not need to open or close the file.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Models.ExtensionHandlers.UpdateFileReferencesModel.Properties">
      <summary>Gets an optional bag of properties which can be used by the handler to determine how it should interpret the references</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Models.ExtensionHandlers.UpdateFileReferencesModel.SourceFile">
      <summary>Gets the file that is having it's references updated</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Models.ExtensionHandlers.UpdateFileReferencesModel.VaultConnection">
      <summary>Gets the Vault Connection that the Update File Reference work flow is associated with.</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Models.ExtensionHandlers.UpdateFileReferencesModel.RefIdFailure">
      <summary>A special value for a Reference ID which signifies that a reference is broken.</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Models.ExtensionHandlers.UpdateFileReferencesModel.RefIdIgnore">
      <summary>A special value for a Reference ID which signifies that a reference should be ignored.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Results.FileAcquisitionResult.AcquisitionStatus">
      <summary>A value representing the status of the File Acquisition</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Results.LogInResult.LogInErrors">
      <summary>Categorizes the type of error that can be returned</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Results.SiteCompatibilityResult.ResultType">
      <summary>Describes the possible types of failure from the
 method.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Results.AcquireFilesResults">
      <summary>The results of a File Acquisition operation</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Results.FileAcquisitionResult">
      <summary>The results of an individual file in a File Acquisition operation</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Results.GetAvailableVaultsResult">
      <summary>Return object of the  method.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Results.GetPropertyValuesResults">
      <summary>A class representing the status of property value retrieval.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Results.LogInResult">
      <summary>Return object of the  LogIn method.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Results.SiteCompatibilityResult">
      <summary>Return object of the
 method.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Results.SiteCompatibilityResult.DatabaseOutOfDateErrorInfo">
      <summary>Detailed error information when
 fails
because the site is newer than the vault database</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Results.SiteCompatibilityResult.SiteOutOfDateErrorInfo">
      <summary>Detailed error information when
 fails
because the site either has missing facilities or products, or has a lower version than the system.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Results.AcquireFilesResults.FileResults">
      <summary>Gets the results of each file that was acquired.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Results.AcquireFilesResults.IsCancelled">
      <summary>Gets whether or not the File Acquisition operation was cancelled</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Results.AcquireFilesResults.CancelledResult">
      <summary>Result representing a cancelled File Acquisition operation.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Results.FileAcquisitionResult.AcquisitionOption">
      <summary>Gets whether the file was downloaded, checked out, or both</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Results.FileAcquisitionResult.Exception">
      <summary>Gets any exception that occured during the file acquisition. This will be populate if
 equals
<see cref="F:Autodesk.DataManagement.Client.Framework.Vault.Results.FileAcquisitionResult.AcquisitionStatus.Exception" />.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Results.FileAcquisitionResult.File">
      <summary>Gets the file represented by this results object</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Results.FileAcquisitionResult.FileReferenceUpdates">
      <summary>Gets a list of file references that were updated within the acquired .</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Results.FileAcquisitionResult.LocalPath">
      <summary>Gets the path where the file is downloaded. This is only valid if
 is configured for downloads</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Results.FileAcquisitionResult.NewFileIteration">
      <summary>Gets the new iteration of the file that results from a Checkout operation</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Results.FileAcquisitionResult.Restrictions">
      <summary>Gets any restriction that prevented the file from being acquired. This will be populate if
 equals
<see cref="F:Autodesk.DataManagement.Client.Framework.Vault.Results.FileAcquisitionResult.AcquisitionStatus.Restriction" />.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Results.FileAcquisitionResult.Status">
      <summary>Gets the status of the file acquisition</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Results.GetAvailableVaultsResult.#ctor">
      <summary>Creates a new instnace of a GetAvailableVaultsResult object</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Results.GetAvailableVaultsResult.ErrorMessage">
      <summary>Gets a failure message that is applicable when  has a value of False</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Results.GetAvailableVaultsResult.Success">
      <summary>Gets whether or not the GetAvailableVaults operation was a success.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Results.GetAvailableVaultsResult.Vaults">
      <summary>Gets a list of vaults returned from the GetAvailableVaults operation</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Results.GetPropertyValuesResults.Error">
      <summary>Value containing the exception(s) that occured during property retrieval, if there was any.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Results.GetPropertyValuesResults.PropertyValues">
      <summary>A collection of property values that have been retrieved.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Results.GetPropertyValuesResults.Tag">
      <summary>A value which was passed in to

which is to help identify the request that is associated with these results.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Results.LogInResult.#ctor">
      <summary>Creates an instance of the LogInResult object</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Results.LogInResult.Connection">
      <summary>Gets the connection to the vault server. This is only valid when  is true.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Results.LogInResult.ErrorMessages">
      <summary>Gets any errors that occured while Logging In. This is populated when  is false.
The dictionary could contain several different errors.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Results.LogInResult.Exception">
      <summary>Gets an exception object if the Log In failure was caused by a server exception. This is only valid when
 is false.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Results.LogInResult.Password">
      <summary>Get the password that was used during the log in.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Results.LogInResult.Success">
      <summary>Gets whether the LogIn method was a success. If True, then  will have
information about the new conneciton.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Results.SiteCompatibilityResult.DatabaseOutOfDateError">
      <summary>Gets detailed error information when
 fails
because the site is newer than the vault database</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Results.SiteCompatibilityResult.Result">
      <summary>Gets a value describing whether or not
 succeeded
or had a specific type of failure</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Results.SiteCompatibilityResult.SiteOutOfDateError">
      <summary>Gets detailed error information when
 fails
because the site either has missing facilities or products, or has a lower version than the system.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Results.SiteCompatibilityResult.DatabaseOutOfDateErrorInfo.Message">
      <summary>Gets a description message describing the failure.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Results.SiteCompatibilityResult.SiteOutOfDateErrorInfo.Message">
      <summary>Gets a description message describing the failure.</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Results.SiteCompatibilityResult.SiteOutOfDateErrorInfo.MissingFacilities">
      <summary>Gets a list of the facilities that were missing from the Vault Site.</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Results.SiteCompatibilityResult.SiteOutOfDateErrorInfo.MissingProducts">
      <summary>Gets a list of the products that were missing from the Vault Site.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Services.IApplicationConfigurationService">
      <summary>This service provides the ability to configure global characteristics for an application using the Autodesk.DataManagement.Client.Framework.Vault Dll.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Services.IEntityIDGenerationService">
      <summary>This service is used to generate an Entity Master ID for entities
associated with programmatic <see cref="T:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClass">Entity Classes</see></summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Services.IPropertyExtensionRegistrationService">
      <summary>This service allows you to register a custom provider that can partipate in the vault property management pipeline. With this provider, you can implement
custom property definitions, create dependencies between existing property definitions, and provide custom property values for specific entities.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Services.IVaultConnectionManagerService">
      <summary>This service encapsulates all connections to a vault server.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Services.IVaultLocalFileLocationService">
      <summary>This service gives location of files ranging from vault specific preferences, saved search locations, working folders, etc.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Services.IApplicationConfigurationService.ExtensionHandlerManagerConfigFilePath">
      <summary>Gets or sets the absolute path to the configuration file containing settings for the ExtensionHandlerManager.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.IEntityIDGenerationService.Generate(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClass,System.String)">
      <summary>Gets a unique ID to associate with a programmatic entity.</summary>
      <param>The entity class that the entity is associated with</param>
      <param>A value that uniquely identifies your entity. This will vary for each entity, and it can be as simple as an entity Display Name if no two objects can have the
same name. The moniker is case and white space sensitive.</param>
      <returns>The generated ID</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.IPropertyExtensionRegistrationService.AddPropertyExtensionProvider(Autodesk.DataManagement.Client.Framework.Vault.Interfaces.IPropertyExtensionProvider)">
      <summary>Adds a custom provider the the property management pipeline</summary>
      <param>The provider to add.</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.IPropertyExtensionRegistrationService.GetPropertyExtensionProviders">
      <summary>Gets a list of all of the custom providers that were registered with the property management pipeline. The providers will be returned in the order they were
added</summary>
      <returns>The list of all registered providers, in the order they were added.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.IVaultConnectionManagerService.CloseAllConnections">
      <summary>Logs out of all existing open connections to the server.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.IVaultConnectionManagerService.GetAvailableVaults(System.String)">
      <summary>Retrieves a list of all of the vaults on a particular server.</summary>
      <param>The name of the server that we are retrieving vaults from</param>
      <returns>An object which contains the list of available vaults, or any error conditions.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.IVaultConnectionManagerService.GetExistingConnection(System.String,System.String,System.String,System.String,Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.AuthenticationFlags)">
      <summary>Attempts to get a connection object for an already existing connection</summary>
      <param>The name of the server</param>
      <param>The name of the vault</param>
      <param>The name of the user</param>
      <param>The user's password</param>
      <param>Additional options describing the type of connection</param>
      <returns>The connection object for an exisiting connection matching the input parameters or null if not matching connection is found.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.IVaultConnectionManagerService.GetSiteCompatibilityWithVault(System.String,System.String)">
      <summary>Tests a site (a server location in a multi site environment) to make sure it is compatible with the master vault database. Compatibility problems could arise
if the site is updated but the database wasn't, or vice versa)</summary>
      <param>The name of the server to test</param>
      <param>The name of the vault to test</param>
      <returns>An object which contains the compatiblity results, including any errors that might have occurred.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.IVaultConnectionManagerService.IsProductSupported(System.String,Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.VaultProduct)">
      <summary>Tests if a vault server has the capabilities to support a specific server product.</summary>
      <param>The name of the vault server</param>
      <param>The product that we are testing</param>
      <returns>True if the specified product is supported on the server, False otherwise</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.IVaultConnectionManagerService.LogOut(Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection)">
      <summary>Log out of a connection to the vault</summary>
      <param>The connection to log out of</param>
      <returns>True if Log out was a success. False otherwise.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.IVaultConnectionManagerService.SetProductRequirements(Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.VaultProductRequirements)">
      <summary>Configures the application requirements for the various known server products. For each product, the app can configure if the product is Required, Optional, or
Restricted.</summary>
      <param>The configuration for each of the server products.</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.IVaultConnectionManagerService.LogIn(System.String,System.String,System.String,System.String,Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.AuthenticationFlags,System.Func{System.String,Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.LoginStates,System.Boolean})">
      <summary>Create a new connection to a vault. Username/PW are ignored for WinAuth and Anonymous connections</summary>
      <param>The name of the server</param>
      <param>The name of the vault</param>
      <param>The user name to authenticate with. This is ignored for Win Auth or Anonymous connections.</param>
      <param>The password to authenticate with. This is ignored for Win Auth or Anonymous connections.</param>
      <param>Additional options that control the type of connections that is being requested (ie. Read Only)</param>
      <param>An optional callback method that can be used to monitor the progress of the Log In operation. The method has a syntax of bool callBack(string message,
Framework.Vault.Services.LoginStates state_enum). The implementation of the method should return false of the LogIn workflow should be cancelled. The message
variable is a string representation of the current state of the log in. This can be used to update a status bar. The state_enum communicates what the log in
workflow is currently executing. This can be used to write conditional logic based on what is currently being executed.</param>
      <returns>An object which contains the new vault Connection, or any error conditions.</returns>
    </member>
    <member name="E:Autodesk.DataManagement.Client.Framework.Vault.Services.IVaultConnectionManagerService.ConnectionEstablished">
      <summary>This event fires every time a successful login occurs to a vault</summary>
    </member>
    <member name="E:Autodesk.DataManagement.Client.Framework.Vault.Services.IVaultConnectionManagerService.ConnectionReleased">
      <summary>This event fires when the user logs out of an existing connection</summary>
    </member>
    <member name="E:Autodesk.DataManagement.Client.Framework.Vault.Services.IVaultConnectionManagerService.ConnectionReused">
      <summary>This event fires if a connection is reused. A connection is reused when an attempt is made to login to a server/vault/user combination where a connection is
already established. In that case, we will just return the existing connection</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.IVaultLocalFileLocationService.GetVaultCommonConnectionPath(Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection,System.Boolean,Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.CommonPreferenceVersionRequirements,System.Boolean)">
      <summary>Gets the location for vault data that is shared between multiple application but is specific to a particular vault or server.</summary>
      <param>The connection to a specific vault</param>
      <param>Whether or not this location is server specific.</param>
      <param>Whether or not this location is specific to a particular version of the server</param>
      <param>Whether or not this location is specific to a particular vault.</param>
      <returns>The computed file path</returns>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Services.IVaultLocalFileLocationService.VaultCommonPath">
      <summary>Gets the location for vault data that is shared between multiple applications that use the Vault SDK. For example, all apps that use the Login Dialog can share
the same login history.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.ICategoryManager">
      <summary>This interface encapsulates all access to Vault Categories. It can be referenced via the
 property.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IChangeOrderManager">
      <summary>This interface encapsulates all access to Vault Change Orders. It can be referenced via the
 property.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IConfigurationManager">
      <summary>This interface encapsulates all access to Configuration data which describes the capabilities of the Vault Server. It can be referenced via the
 property. This includes static data that isn't changed
at runtime by vault users. Configuration data includes information about EntityClasses, Behaviors, and Content Source Providers</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.ICopyDesignEntityOperationManager">
      <summary>An interface that provides access to a set of Copy Design specific operations that can be generically executed for any Entity</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.ICustomObjectManager">
      <summary>This interface encapsulates all access to Vault Custom Objects. It can be referenced via the
 property.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IEntityOperationManager">
      <summary>This interface provides access to a set of operations that can be generically executed for any Entity It can be referenced via the
 property.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IFileManager">
      <summary>This interface encapsulates all access to Vault Files. It can be referenced via the
 property.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IFolderManager">
      <summary>This interface encapsulates all access to Vault Folders. It can be referenced via the
 property.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IItemManager">
      <summary>This interface encapsulates all access to Vault Items. It can be referenced via the
 property.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.ILinkManager">
      <summary>This interface encapsulates all access to Vault Linked objects. It can be referenced via the
 property.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IPersistableIdManager">
      <summary>This interface encapsulates all access to Vault persistable ids. It can be referenced via the
 property.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IPropertyManager">
      <summary>This interface encapsulates all access to Vault Property Values and Property Definitions. It can be referenced via the
 property.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IWorkingFoldersManager">
      <summary>Provides methods for getting working folders</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.ICategoryManager.AddCategory(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityClass,System.String,System.Drawing.Color,System.String,System.Boolean,System.Boolean)">
      <summary>Adds a new category.</summary>
      <param>The entity class to associate with the new category. See </param>
      <param>The new category name.</param>
      <param>The new category color.</param>
      <param>The new category description.</param>
      <param>True if the defeault category, false if not the default.</param>
      <param>True if enabled, false if disabled.</param>
      <returns>The new category.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.ICategoryManager.CopyCategory(System.Int64,System.String,System.Drawing.Color,System.String,System.Boolean)">
      <summary>Copies an existing category.</summary>
      <param>The id of the category to copy.</param>
      <param>The new category name.</param>
      <param>The new category color.</param>
      <param>The new category description.</param>
      <param>True if enabled, false if disabled.</param>
      <returns>The new category.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.ICategoryManager.DeleteCategory(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory,System.Collections.Generic.List{Autodesk.DataManagement.Client.Framework.Currency.Restriction}@)">
      <summary>Deletes an existing category.</summary>
      <param>The category to delete.</param>
      <param>If the delete failed then this list contains the reasons for the failure.</param>
      <returns>True if the delete was successful, false if there were restrictions.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.ICategoryManager.GetCategoryGlyph(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory)">
      <summary>Retrieves the glyph object associated with a category</summary>
      <param>The category to retrieve the glyph for</param>
      <returns>An object containing information about the glyph</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.ICategoryManager.SetDefaultCategory(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory)">
      <summary>Sets a new default category. The previous default category is set to enabled.</summary>
      <param>The category to set as the default.</param>
      <param>The previous default category.</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.ICategoryManager.UpdateCategory(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory)">
      <summary>Updates an existing category with new information.</summary>
      <param>The category to be updated. The ID needs to be an existing category ID. The other properties are the new values.</param>
      <returns>The updated category.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.ICategoryManager.FindCategory(System.Int64)">
      <summary>Retrieves a category by it's unique id</summary>
      <param>The unique  of the category to retrieve</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.ICategoryManager.FindCategory(System.String,System.String,System.Boolean)">
      <summary>Retrieves a category by it's system name or display name</summary>
      <param>The entity class to get categories for. See </param>
      <param>The  or
<see cref="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory.Name" /> of the category to retrieve</param>
      <param>True if the search is by the , false if by
<see cref="P:Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory.SystemName" /></param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.ICategoryManager.GetAvailableCategories">
      <summary>Retrieves a list of all of the categories in a vault</summary>
      <returns>A list of all of the categories in a vault.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.ICategoryManager.GetAvailableCategories(System.String)">
      <summary>Retrieves a list of all categories associated with a particular entity class</summary>
      <param>The entity class to get categories for. See </param>
      <returns>A list of all of the categories in a vault assigned to <paramref name="eclassId" /></returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.ICategoryManager.GetAvailableCategories(System.String,System.Boolean)">
      <summary>Retrieves a list of all categories associated with a particular entity class</summary>
      <param>The entity class to get categories for. See </param>
      <param>If true, only enabled cateogries are returned.</param>
      <returns>A list of all of the categories in a vault assigned to <paramref name="eclassId" /></returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IChangeOrderManager.GetChangeOrdersByIds(System.Collections.Generic.IEnumerable{System.Int64})">
      <summary>Loads multiple change orders from the server at once</summary>
      <param>A list of Change Order Id's () to retrieve</param>
      <returns>A dictionary of Change Orders where the Key is the Change Order Id and the Value is the Change Order</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IConfigurationManager.GetContentSourceProviders">
      <summary>Get s a list of all content source providers registered on the vault server. A content source provider is an object that knows how to read/write property
values for a particular file extension</summary>
      <returns>A list of content source providers.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IConfigurationManager.GetEntityClass(System.String)">
      <summary>Gets an Entity Class corresponding to a specific well know entity class id</summary>
      <param>The ID of the Entity Class to retrieve. See </param>
      <returns>The Entity Class with the specified ID, or null if there is no match</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IConfigurationManager.GetEntityClasses">
      <summary>Gets a list of all Entity Classes supported by the vault server</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IConfigurationManager.AddInternalEntityClass(System.String,System.String)">
      <summary>Adds a non-server based entity class to the VDF.</summary>
      <param>The unique ID of the entity class</param>
      <param>The display name of the entity class</param>
      <returns>The newly created entity class object</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IConfigurationManager.GetContentSourceProvider(System.String)">
      <summary>Gets a Contentent Source Provider associated with the specified well know provider System name.</summary>
      <param>The system name of the Content Source Provider to retrieve. See
</param>
      <returns>The matching Content Source Provider, or null if there is no match.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IConfigurationManager.GetContentSourceProvider(System.Int64)">
      <summary>Gets a Contentent Source Provider associated with the specified provider ID</summary>
      <param>The ID of the Content Source Provider to retrieve. See </param>
      <returns>The matching Content Source Provider, or null if there is no match.</returns>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IConfigurationManager.SupportsMultiSite">
      <summary>Gets whether the server supports Multisite</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IConfigurationManager.SupportsRevisions">
      <summary>Gets whether the server configuration supports the Revisions feature</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IConfigurationManager.SupportsWindowsAuthentication">
      <summary>Gets whether the server supports windows Authentication</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.ICustomObjectManager.AddCustomObjectDefinition(System.String,System.String,System.String,System.Drawing.Icon,Autodesk.Connectivity.WebServices.ACE[])">
      <summary>Adds a CustomObjectDefinition.</summary>
      <param>A string that uniquely identifies the CustomObjectDefinition.</param>
      <param>The display name of the CustomObjectDefinition.</param>
      <param>The plural display name of the CustomObjectDefinition.</param>
      <param>An icon to associated with the CustomObjectDefinition.</param>
      <param>The access permissions object.</param>
      <returns>The added CustomObjectDefinition.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.ICustomObjectManager.DeleteCustomObjectDefinition(System.Int64)">
      <summary>Deletes a custom object definition.</summary>
      <param>The id of the CustomObjectDefinition to delete.</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.ICustomObjectManager.GetCustomObjectDefinitionById(System.Int64)">
      <summary>Gets a CustomObjectDefinition.</summary>
      <param>The id of the CustomObjectDefinition to retrieve.</param>
      <returns>A CustomObjectDefinition or null if it does not exist.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.ICustomObjectManager.GetCustomObjectDefinitions">
      <summary>Gets all CustomObjectDefinition's.</summary>
      <returns>All of the CustomObjectDefinition's.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.ICustomObjectManager.GetCustomObjectsByIds(System.Collections.Generic.IEnumerable{System.Int64})">
      <summary>Loads multiple custom objects from the server at once</summary>
      <param>A list of custom object Id's () to retrieve</param>
      <returns>A dictionary of Custom Objects where the Key is the Custom Object Id and the Value is the Custom Object</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.ICustomObjectManager.UpdateCustomObjectDefinition(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.CustomObjectDefinition,Autodesk.Connectivity.WebServices.ACE[])">
      <summary>Updates a custom object definition.</summary>
      <param>The CustomObjectDefinition with updated values.</param>
      <param>The access permissions object.</param>
      <returns>The updated CustomObjectDefinition.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IEntityOperationManager.CanBrowseChildren(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity)">
      <summary>Determines if the specified entity is browsable. This means that it can have children, not that it necessarily does.</summary>
      <param>The entity to compute whether it is browsable.</param>
      <returns>True if the entity can be browsed, False otherwise.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IEntityOperationManager.CanDownloadOrCheckoutEntities(System.Collections.Generic.IEnumerable{Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity})">
      <summary>Determines if the list of entities are eligible for the Get/Checkout workflow.</summary>
      <param>List of entities to evaluate</param>
      <returns>True if every entity is eligible. False if at least one isnt</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IEntityOperationManager.ConvertEntInfosToIEntities(System.Collections.Generic.IEnumerable{Autodesk.DataManagement.Client.Framework.Vault.Currency.PersistentId.PersistableIdEntInfo})">
      <summary>Converts a collection of web service Ent objects to the appropriate IEntity object.</summary>
      <param>if true, assumes all Ent objects supplied are the latest (master) based versions, otherwise they are all version (iteration) based. Providing an incorrect
getLatest value may result in an exception.</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IEntityOperationManager.GetBrowseChildren(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity,System.Boolean)">
      <summary>Gets all of the children for a specific entity for a browse workflow.</summary>
      <param>The entity to retrieve children for.</param>
      <param>Whether to include hidden entities in the results.</param>
      <returns>A list of children for the specified entity.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IEntityOperationManager.GetParent(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity)">
      <summary>Gets the parent of the specified entity.</summary>
      <param>The entity to retrieve the parent for</param>
      <returns>The parent of the specified entity, or null if there is not a parent.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IEntityOperationManager.GetPersistableIds(System.Collections.Generic.IEnumerable{Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity},System.Boolean)">
      <summary>Gets a persistable id for each entity in entities. This persistable id is used to uniquely identify the entity and can be safely persisted.</summary>
      <param>List of entities to retrieve persistable ids for</param>
      <param>if true, always return a persistable id which, when used, will get the latest version of the entity</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IEntityOperationManager.GetSelectableRevisions(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity)">
      <summary>Gets a list of revisions that can be selected by a user for a given entity</summary>
      <param>The entity to retrieve the revisions for</param>
      <returns>A list of information about available revisions for the specified entity</returns>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IEntityOperationManager.CopyDesign">
      <summary>Gets an interface that provides access to a set of Copy Design specific operations that can be generically executed for any Entity</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IFileManager.AcquireFiles(Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings)">
      <summary>Downloads and/or checks out files form the vault</summary>
      <param>The settings which specify the files to operate on and other options which determine how to handle the files with regards to fixing broken references,
overriding existing files on disk, etc. The settings also contain delegates where custom handlers for progress and restrictions can be plugged in.</param>
      <returns>An object which details the objects that were downloaded, any references that were fixed, and any errors that might have occurred</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IFileManager.AcquireFilesAsync(Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings)">
      <summary>Asynchronously downloads and/or checks out files from the vault.</summary>
      <param>The settings which specify the files to operate on and other options which determine how to handle the files with regards to fixing broken references,
overriding existing files on disk, etc. The settings also contain delegates where custom handlers for progress and restrictions can be plugged in.</param>
      <returns>A Task which represents the background thread that is performing the operation. When the thread is complete, the task will contian the results of each file
that was acted on.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IFileManager.GetFileAssociationLites(System.Collections.Generic.IEnumerable{System.Int64},Autodesk.DataManagement.Client.Framework.Vault.Settings.IFileRelationshipGatheringSettingsReadOnly)">
      <summary>Gets the "light" file associations (i.e. File associations which includes only file Ids) for the input fileIterationIds based on the settings.</summary>
      <param>The file Ids to retrieve associations for</param>
      <param>The gathering settings which specify what associations to gather</param>
      <returns>The "light" file associations</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IFileManager.GetFilesByIterationIds(System.Collections.Generic.IEnumerable{System.Int64})">
      <summary>Loads multiple File Iterations froms the server at once.</summary>
      <param>A list of File Iteration Id's () to retrieve</param>
      <returns>A dictionary of FileIterations where the Key is the File Iteration Id and the Value is the FileIteration object</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IFileManager.GetLatestFilesByIterationIds(System.Collections.Generic.IEnumerable{System.Int64})">
      <summary>Loads the latest version (Masters) of File Iterations froms the server at once.</summary>
      <param>A list of File Iteration Id's () to retrieve the Master
versions for.</param>
      <returns>A dictionary of FileIterations where the Key is the File Iteration Id and the Value is the FileIteration object</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IFileManager.LoadParentFolders(System.Collections.Generic.IEnumerable{Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration})">
      <summary>Ensures that all of the specified files have their  attribute set.</summary>
      <param>The list of files to examine</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IFileManager.UpdateFileReferences(Autodesk.DataManagement.Client.Framework.Vault.Models.UpdateFileReferencesModel,System.Collections.Generic.IDictionary{Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration,Autodesk.DataManagement.Client.Framework.Currency.FilePathAbsolute})">
      <summary>Updates the files references for a collection of files that are on disk.</summary>
      <param>The model used to update the files references.</param>
      <param>A map of files to be updated paired with their location on disk.</param>
      <returns>A collection of file iterations that were attempted to be updated paired with their file reference udpate results.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IFileManager.AddFile(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder,System.String,Autodesk.Connectivity.WebServices.FileAssocParam[],Autodesk.Connectivity.WebServices.BOM,Autodesk.Connectivity.WebServices.FileClassification,System.Boolean,Autodesk.DataManagement.Client.Framework.Currency.FilePathAbsolute)">
      <summary>Adds a file from disk to vault.</summary>
      <param>The parent folder to add the file to</param>
      <param>Text data to be associated with version 1 of the file</param>
      <param>A bill of materials to associate with this file. Null should be passed in if there is no BOM.</param>
      <param>The classification of the file.</param>
      <param>If true, the file should be hidden by the client.</param>
      <param>The full local path of the file including the file's name</param>
      <returns>A FileIteration object of the newly added file.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IFileManager.AddFile(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder,System.String,System.String,System.DateTime,Autodesk.Connectivity.WebServices.FileAssocParam[],Autodesk.Connectivity.WebServices.BOM,Autodesk.Connectivity.WebServices.FileClassification,System.Boolean,System.IO.Stream)">
      <summary>Adds a file to vault.</summary>
      <param>The parent folder to add the file to</param>
      <param>The name of the file including the file extension</param>
      <param>Text data to be associated with version 1 of the file</param>
      <param>The last time the file has been modified</param>
      <param>A bill of materials to associate with this file. Null should be passed in if there is no BOM.</param>
      <param>The classification of the file.</param>
      <param>If true, the file should be hidden by the client.</param>
      <param>A containing the binary contents of the file</param>
      <returns>A FileIteration object of the newly added file.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IFileManager.AddFileWithBehaviors(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder,System.String,Autodesk.Connectivity.WebServices.FileAssocParam[],Autodesk.Connectivity.WebServices.BOM,Autodesk.Connectivity.WebServices.FileClassification,System.Boolean,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration,Autodesk.DataManagement.Client.Framework.Currency.FilePathAbsolute)">
      <summary>Adds a file to vault and associates it with a set of behaviors.</summary>
      <param>The parent folder to add the file to</param>
      <param>Text data to be associated with version 1 of the file</param>
      <param>A bill of materials to associate with this file. Null should be passed in if there is no BOM.</param>
      <param>The classification of the file.</param>
      <param>If true, the file should be hidden by the client.</param>
      <param>The file that the new file will copy its Behaviors from.</param>
      <param>The full local path of the file including the file's name</param>
      <returns>A FileIteration object of the newly added file.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IFileManager.AddFileWithBehaviors(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder,System.String,System.String,System.DateTime,Autodesk.Connectivity.WebServices.FileAssocParam[],Autodesk.Connectivity.WebServices.BOM,Autodesk.Connectivity.WebServices.FileClassification,System.Boolean,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration,System.IO.Stream)">
      <summary>Adds a file to vault and associates it with a set of behaviors.</summary>
      <param>The parent folder to add the file to</param>
      <param>The name of the file including the file extension</param>
      <param>Text data to be associated with version 1 of the file</param>
      <param>The last time the file has been modified</param>
      <param>A bill of materials to associate with this file. Null should be passed in if there is no BOM.</param>
      <param>The classification of the file.</param>
      <param>If true, the file should be hidden by the client.</param>
      <param>The file that the new file will copy its Behaviors from.</param>
      <param>A containing the binary contents of the file</param>
      <returns>A FileIteration object of the newly added file.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IFileManager.CheckinFile(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration,System.String,System.Boolean,Autodesk.Connectivity.WebServices.FileAssocParam[],Autodesk.Connectivity.WebServices.BOM,System.Boolean,System.String,Autodesk.Connectivity.WebServices.FileClassification,System.Boolean,Autodesk.DataManagement.Client.Framework.Currency.FilePathAbsolute)">
      <summary>Checks in a file to the Vault.</summary>
      <param>The file to check-in.</param>
      <param>Text data to be associated with the check-in.</param>
      <param>If true, the new version gets uploaded and versioned, but the file is still checked out to the user.</param>
      <param>The associations on the file.</param>
      <param>A bill of materials to associate with this file. Null should be passed in if there is no BOM.</param>
      <param>If this value is true and the 'bom' parameter is null, the BOM from the previous version will be copied for this version.</param>
      <param>Allows the file to be renamed during check-in. Pass in null to keep the file name the same.</param>
      <param>The classification for the file.</param>
      <param>A flag to indicate if a file should be hidden.</param>
      <param>The location of the file on disk. If null, the working folder location will be used.</param>
      <returns>The newly created iteration of the file.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IFileManager.CheckinFile(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration,System.String,System.Boolean,System.DateTime,Autodesk.Connectivity.WebServices.FileAssocParam[],Autodesk.Connectivity.WebServices.BOM,System.Boolean,System.String,Autodesk.Connectivity.WebServices.FileClassification,System.Boolean,System.IO.Stream)">
      <summary>Checks in a file to the Vault.</summary>
      <param>The file to check-in.</param>
      <param>Text data to be associated with the check-in.</param>
      <param>If true, the new version gets uploaded and versioned, but the file is still checked out to the user.</param>
      <param>The associations on the file.</param>
      <param>A bill of materials to associate with this file. Null should be passed in if there is no BOM.</param>
      <param>If this value is true and the 'bom' parameter is null, the BOM from the previous version will be copied for this version.</param>
      <param>Allows the file to be renamed during check-in. Pass in null to keep the file name the same.</param>
      <param>The classification for the file.</param>
      <param>A flag to indicate if a file should be hidden.</param>
      <param>The binary contents of the new iteration of the file.</param>
      <returns>The newly created iteration of the file. An empty stream indicates a zero-length file. A null value indicates that the file data has not changed since the last
version.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IFileManager.UndoCheckoutFile(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration,System.IO.Stream)">
      <summary>Undoes a checkout operation on a file.</summary>
      <param>The checked out file.</param>
      <param>A stream to output the binary contents of the last-check-in file version to. If set to null, no download will be performed.</param>
      <returns>The file iteration representing the last-checked-in version of the file.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IFileManager.UndoCheckoutFile(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration,Autodesk.DataManagement.Client.Framework.Currency.FilePathAbsolute)">
      <summary>Undoes a checkout operation on a file.</summary>
      <param>The checked out file.</param>
      <param>The location on disk to download the last-checked-in file.</param>
      <returns>The file iteration representing the last-checked-in version of the file.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IFolderManager.CreateFolder(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder,System.String,System.Boolean,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory)">
      <summary>Creates a vault folder</summary>
      <param>The parent of the folder that we are creating</param>
      <param>The name of the folder to create</param>
      <param>True if the new folder should be a library folder</param>
      <param>The category to associate with the new folder. In the Base Vault product, this should be null as categories are not supported.</param>
      <returns>The newly created folder</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IFolderManager.EnsureFolderPathsExist(System.Collections.Generic.IEnumerable{Autodesk.DataManagement.Client.Framework.Currency.FolderPathAbsolute},Autodesk.DataManagement.Client.Framework.Interfaces.IProgressReporter)">
      <summary>Makes sure that all of the folders exist on the server and creates any that were missing</summary>
      <param>A list of paths to evaluate</param>
      <param>Progress reporter to show the progress of folder creation</param>
      <returns>A map of the folder paths to the actual folder objects</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IFolderManager.GetFoldersByIds(System.Collections.Generic.IEnumerable{System.Int64})">
      <summary>Loads multiple Folders froms the server at once.</summary>
      <param>A list of Folder Id's () to retrieve</param>
      <returns>A dictionary of Folders where the Key is the Folder Id and the Value is the Folder object</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IFolderManager.ValidateFolderName(System.String,System.String@)">
      <summary>Validates that a folder name meets all of the vault naming conventions.</summary>
      <param>The folder name to validate</param>
      <param>A corrected folder name with trimmed characters, etc.</param>
      <returns>The result of the validation. If the name could be corrected, this will return None as an error code</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IFolderManager.GetChildFolders(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder,System.Boolean,System.Boolean)">
      <summary>Retrieves a list of the child folders for a particular folder</summary>
      <param>The folder to retrieve children for</param>
      <param>If true, then folders for subdirectories will be returned too. If false, only immediate children will be returned</param>
      <param>If false, then cloaked folders are not included in the results (furthermore, if recurse is true, cloaked folders will not be recursed into)</param>
      <returns>The result set of child folders</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IFolderManager.GetChildFolders(System.Collections.Generic.IEnumerable{Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.Folder},System.Boolean,System.Boolean)">
      <summary>Retrieves a list of child folders for a bulk quantity of folders</summary>
      <param>The folders to retrieve children for</param>
      <param>If true, then folders for subdirectories will be returned too. If false, only immediate children will be returned</param>
      <param>If false, then cloaked folders are not included in the results (furthermore, if recurse is true, cloaked folders will not be recursed into)</param>
      <returns>The result set of child folders in a dictionary mapping the parent folder to it's children</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IFolderManager.GetChildFolders(System.Collections.Generic.IEnumerable{System.Int64},System.Boolean,System.Boolean)">
      <summary>Retrieves a list of child folders ids for a bulk quantity of folders ids</summary>
      <param>The id's of the folders to retrieve children for</param>
      <param>If true, then folders for subdirectories will be returned too. If false, only immediate children will be returned</param>
      <param>If false, then cloaked folders are not included in the results (furthermore, if recurse is true, cloaked folders will not be recursed into)</param>
      <returns>The result set of child folder ids in a dictionary mapping the parent folder id to it's children</returns>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IFolderManager.InvalidFolderCharacters">
      <summary>Gets the set of illegal characters for vault folder names</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IFolderManager.RootFolder">
      <summary>Gets the Root Folder in a vault</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IItemManager.GetItemsByIterationId(System.Collections.Generic.IEnumerable{System.Int64})">
      <summary>Loads multiple Item Revisions froms the server at once.</summary>
      <param>A list of Item Iteration Id's () to retrieve</param>
      <returns>A dictionary of ItemRevisions where the Key is the Item Iteration Id and the Value is the ItemRevision object</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.ILinkManager.GetLinkedChildren(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity,System.String)">
      <summary>Loads all of the links contained within an entity</summary>
      <param>The entity to retrieve links entities for</param>
      <param>If null or empty, then all types of linked entities will be retrieved. Otherwise, than only entities with this Entity class ID will be retrieved. See
.</param>
      <returns>A list of linked entities contained in the parent.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.ILinkManager.GetLinkedChildren(System.Collections.Generic.IEnumerable{Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity},System.Collections.Generic.IEnumerable{System.String})">
      <summary>Loads all of the linked entities contained in multiple parents</summary>
      <param>The parent entities to retrieve child links entities for</param>
      <param>If null or empty, then all types of linked entities will be retrieved. Otherwise, than only entities with Entity Class Ids contained in this list will be
retrieved. See .</param>
      <returns>A list of linked entities contained in the parents.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IPersistableIdManager.GetPersistableIds(System.Collections.Generic.IEnumerable{Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity},System.Boolean)">
      <summary>Gets a persistable id for each entity in entities. This persistable id is used to uniquely identify the entity and can be safely persisted.</summary>
      <param>if true, always return a persistable id which represents the latest version of the entity</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IPersistableIdManager.ResolvePersistableIds(System.Collections.Generic.IEnumerable{System.String})">
      <summary>Retrieves the entities for the supplied set of persistable ids.</summary>
      <param>The persistable ids to retrieve entities for</param>
      <returns>A dictionary which maps the supplied persistable id to the entity</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IPropertyManager.GetPropertyDefinitionById(System.Int64)">
      <summary>Gets a Property Definition associated with a specific Property Definition Server Id.</summary>
      <param>The server ID of the Property Definition to retrieve. See </param>
      <returns>The matching Property Definition, or null of there is no Property Definition with the matching ID</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IPropertyManager.GetPropertyDefinitionBySystemName(System.String)">
      <summary>Gets a Property Definition associated with a specific Property Definition System Name.</summary>
      <param>The unique system name of the Property Definition to retrieve. See
</param>
      <returns>The matching Property Definition, or null of there is no Property Definition with the matching System Name</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IPropertyManager.GetPropertyDefinitions(System.String,Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.EntityCategory,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinitionFilter)">
      <summary>Gets a list of property definitions in a vault</summary>
      <param>Retrieves only the Property Definitions associated with this entity class Id. If null, then Property Definitions for all Entity Classes are returned</param>
      <param>Retrieves only the Property Definitions that are associated with this category. If null, then all Property Definitions are returned regardless of category</param>
      <param>Optional filters to limit the types of Property Definitions that are returned</param>
      <returns>A dictionary containing the resulting set of Property Definitions</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IPropertyManager.GetPropertyDefinitionsMappedToContentSourceProperty(Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.ContentSourceProvider,System.String)">
      <summary>Retrieves a list of all associations that a Content Source PropDef has with UDP's</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IPropertyManager.GetPropertyValue(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValueSettings)">
      <summary>Gets a single property value for a specific Entity</summary>
      <param>The entity to get a property value for</param>
      <param>The Property Definition to get a property value for.</param>
      <param>Optional conifguration which determines how some property values should be computed. This can be null to use the default settings</param>
      <returns>The resulting property value, or null if there is no value</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IPropertyManager.GetPropertyValues(System.Collections.Generic.IEnumerable{Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity},System.Collections.Generic.IEnumerable{Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition},Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValueSettings)">
      <summary>Gets property values for a list of Entities and Property Definitions</summary>
      <param>The list of entities to get property values for</param>
      <param>The list of Property Definitions to get property values for. If null, then values will be retrieved for all property definitions.</param>
      <param>Optional conifguration which determines how some property values should be computed</param>
      <returns>The resulting property values</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IPropertyManager.GetPropertyValuesAsync(System.Collections.Generic.IEnumerable{Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity},System.Collections.Generic.IEnumerable{Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition},Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyValueSettings,System.Object,System.Func{Autodesk.DataManagement.Client.Framework.Vault.Results.GetPropertyValuesResults,System.Boolean})">
      <summary>Asynchronously retrieves property values for a list of Entities and Property Definitions</summary>
      <param>The list of entities to get property values for</param>
      <param>The list of Property Definitions to get property values for. If null, then values will be retrieved for all property definitions.</param>
      <param>Optional conifguration which determines how some property values should be computed</param>
      <param>A value which will be passed back to the <paramref name="resultFunc" /> which helps to identify the request that is associated with the asynchronous results.</param>
      <param>A callback function used to report incremental progress. The callback function has the syntax of <c>bool
callBack(Vault.Results.GetPropertyValuesResults results)</c>. The callback should return true to continue processing results. A return of false will cancel
the remainder of the retrieval. The PropertyValues property on the results parameter will be null to indicate that there is no more incremental data to report.</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IPropertyManager.ClearPropertyCache">
      <summary>Clears all of the property values from the cache.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IPropertyManager.ClearPropertyCache(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity,System.Collections.Generic.IEnumerable{Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyDefinition})">
      <summary>Clears property values from the cache for an entity</summary>
      <param>The entity to clear values for</param>
      <param>The property definitions to removed cached values for. If null, values will be removed for the entire entity</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IPropertyManager.ClearPropertyCache(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity,Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties.PropertyCacheClearOptions)">
      <summary>Clears property values from the cache for an entity</summary>
      <param>The entity to clear values for</param>
      <param>The type of properties to clear (all properties or only the dynamic property values)</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IWorkingFoldersManager.ClearAllWorkingFolders">
      <summary>Clears all mappings between vault folders and workings folders.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IWorkingFoldersManager.ClearWorkingFolder(System.String)">
      <summary>Removes the mapping to a working folder for a given vault folder</summary>
      <param>The path of the vault folder to remove the mapping from</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IWorkingFoldersManager.GetAllWorkingFolders(System.Boolean)">
      <summary>Returns a dictionary of all the working folder mappings</summary>
      <returns>A case-insensitive dictionary containing all mappings between vault folders and working folders.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IWorkingFoldersManager.GetPathOfFileInWorkingFolder(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration)">
      <summary>Gets the full path of the specified file in it's working folder</summary>
      <param>The file to retrieve the path for</param>
      <returns>The full path to the file (containing both the directory and file name) in it's working folder</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IWorkingFoldersManager.SetWorkingFolder(System.String,Autodesk.DataManagement.Client.Framework.Currency.FolderPathAbsolute)">
      <summary>Sets the working folder for a vault folder</summary>
      <param>The vault folder path</param>
      <param>The path of the working folder that is being mapped to the vault folder</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IWorkingFoldersManager.GetWorkingFolder(System.String)">
      <summary>Gets the working folder for the corresponding vault folder</summary>
      <param>The full path of the vault folder</param>
      <returns>The full path of the working folder</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IWorkingFoldersManager.GetWorkingFolder(System.String,System.Boolean)">
      <summary>Gets the working folder for the corresponding vault folder</summary>
      <param>The full path of the vault folder</param>
      <param>Whether or not to expanded system variables embeded in the working folder's path</param>
      <returns>The full path of the working folder</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Services.Connection.IWorkingFoldersManager.GetWorkingFolder(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration)">
      <summary>Gets the working folder that the specified file lives in</summary>
      <param>The file to retrieve the working folder for</param>
      <returns>The working folder for the specified file</returns>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Settings.IFileRelationshipGatheringSettingsReadOnly">
      <summary>An read-only interface which specifies how files should be consumed relative to other files.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.IFileRelationshipGatheringSettingsReadOnly.DateBiased">
      <summary>Gets whether we should use the "Date biased" approach of gathering dependencies.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.IFileRelationshipGatheringSettingsReadOnly.IncludeAttachments">
      <summary>Gets whether attachments of a source file should be consumed</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.IFileRelationshipGatheringSettingsReadOnly.IncludeChildren">
      <summary>Gets whether children of a source file should be consumed</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.IFileRelationshipGatheringSettingsReadOnly.IncludeHiddenEntities">
      <summary>Gets whether related files that are hidden should be consumed.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.IFileRelationshipGatheringSettingsReadOnly.IncludeLibraryContents">
      <summary>Gets whether children that are in library folders should be consumed.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.IFileRelationshipGatheringSettingsReadOnly.IncludeParents">
      <summary>Gets whether parents of a source file should be consumed</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.IFileRelationshipGatheringSettingsReadOnly.IncludeRelatedDocumentation">
      <summary>Gets whether related documentation that is associated with the entity will be gathered. For examplee, a DWG might be considered related documentation for an
assembly. The DWG is not considered an attachment or a dependent.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.IFileRelationshipGatheringSettingsReadOnly.RecurseChildren">
      <summary>Gets whether all children of a source file should be consumed, or just the immediate children. This only applies when
 is True</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.IFileRelationshipGatheringSettingsReadOnly.RecurseParents">
      <summary>Gets whether all parents of a source file should be consumed, or just the direct parents. This only applies when
 is True</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.IFileRelationshipGatheringSettingsReadOnly.ReleaseBiased">
      <summary>Gets if we should use the "Release biased" approach of gathering dependencies.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.IFileRelationshipGatheringSettingsReadOnly.VersionGatheringOption">
      <summary>Gets an option which determines which versions of related files should be gathered.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileResolutionOptions.OverwriteOptions">
      <summary>An enumeration representing the behavior to use when downloading a file from Vault to a location where a file with the same name already exists.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquisitionOption">
      <summary>Options which determine how to acquire the file</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.FileAcquisitionInfo">
      <summary>Flags which give relevant per-file information for download or checkout.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.PreFileAcquisitionResult">
      <summary>The result of the PreFileAcquire event before it is acquired.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.SyncWithRemoteSite">
      <summary>Options which determines how to handle files on the server that need to be synchronized with a remote site in a replication environment</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings">
      <summary>Configuration options for the AcquireFiles workflow that is used to download and/or checkout files from the vault</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileExtensibilityOptions">
      <summary>This class provides handlers that can be configured to augment the functionality of the Acquire Files workflow</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileExtensibilityOptions.FileWillChangeEventArgs">
      <summary>The event arguments for the FileWillChange event</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileExtensibilityOptions.PostAcquireEventArgs">
      <summary>The event arguments for the PostAcquire event</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileExtensibilityOptions.PostFileAcquireEventArgs">
      <summary>The event arguments for the PostFileAcquire event</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileExtensibilityOptions.PreAcquireEventArgs">
      <summary>The event arguments for the PreAcquire event</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileExtensibilityOptions.PreFileAcquireEventArgs">
      <summary>The event arguments for the PreFileAcquire event</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileResolutionOptions">
      <summary>This class provides a configuration for how to handle scenarios that need resolution. For example, what to do if a file exists, how to handle files not on the
remote server, how to update out of date file references, etc.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileThreadingOptions">
      <summary>This class provides configuration options that control the background processing of the file acquisition process</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Settings.FileRelationshipGatheringSettings">
      <summary>Provides the settings that control how related files should be consumed relative to other files</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Settings.LinkGatheringSettings">
      <summary>Provides the settings that control how related files should be consumed relative to other entities via Link relationships. For example, a Folder that contains
Links to Files.</summary>
    </member>
    <member name="T:Autodesk.DataManagement.Client.Framework.Vault.Settings.RelationshipGatheringSettings">
      <summary>Provides the settings that control how related files should be consumed relative to other entities</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections.Connection,System.Boolean)">
      <summary>Constructs an instance of the AcquireFileSettings class</summary>
      <param>A connection to a vault server from which the files will be acquired</param>
      <param>If true, file references will be updated after files are downloaded from the vault server.</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.IsValidConfiguration">
      <summary>Tests if the  object has been properly configured and that we can proceed with
the file acquisition</summary>
      <returns>True if the configuration is valid.</returns>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AddEntityToAcquire(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity)">
      <summary>Adds to the list of entities to be acquired. This may be an actual file, or a container object (ie. a folder) that contains files</summary>
      <param>The entity to be acquired</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AddEntityToAcquire(System.Collections.Generic.IEnumerable{Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity})">
      <summary>Adds to the list of entities to be acquired. This may be a list of actual files, container objects (ie. a folder) that contains files, or a combination of both</summary>
      <param>The entities to be acquired</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AddFileToAcquire(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration,Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquisitionOption)">
      <summary>Adds a file to the list of entities to be acquired.</summary>
      <param>The file to be acquired</param>
      <param>Whether the file should be downloaded, checked out, or both</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AddFileToAcquire(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration,Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquisitionOption,Autodesk.DataManagement.Client.Framework.Currency.FilePathAbsolute)">
      <summary>Adds a file to the list of entities to be acquired.</summary>
      <param>The file to be acquired</param>
      <param>Whether the file should be downloaded, checked out, or both</param>
      <param>The location where the file should be downloaded. If null, then it will be downloaded to it's working folder</param>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AddFileToAcquire(Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.FileIteration,Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquisitionOption,Autodesk.DataManagement.Client.Framework.Currency.FolderPathAbsolute)">
      <summary>Adds a file to the list of entities to be acquired.</summary>
      <param>The file to be acquired</param>
      <param>Whether the file should be downloaded, checked out, or both</param>
      <param>The location where the file should be downloaded. If null, then it will be downloaded to it's working folder. The full file path will be the path of the
specified folder, plus the entity's name.</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.CheckoutComment">
      <summary>Gets or sets the comment that will be applied to any files that are Checked Out.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.DefaultAcquisitionOption">
      <summary>Gets or sets how files will be acquired (whether they are downloaded, checked out, or both).</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.LocalPath">
      <summary>The path where files will be downloaded to. If null then files will be downloaded to their working folders</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.OptionsExtensibility">
      <summary>Gets an object which contains handlers that can be used to augment the functionality of the Acquire Files workflow</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.OptionsRelationshipGathering">
      <summary>Gets options which can be used to configure how the relationships for entities added via AddEntityToAcquire are evaluated to see what additional files should
be acquired</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.OptionsResolution">
      <summary>Gets an object which handles scenarios that need resolution. For example, what to do if a file exists, how to handle files not on the remote server, how to
update out of date file references, etc.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.OptionsThreading">
      <summary>Gets an object which allows for the configuration of the background processing.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.OrganizeFilesRelativeToCommonVaultRoot">
      <summary>Gets or sets how files should be organized when downloaded to a .</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.VaultConnection">
      <summary>Gets a connection to a vault server from which the files will be acquired</summary>
    </member>
    <member name="E:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.PropertyChanged">
      <summary>Event fired when one of the options in this object are changed</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileExtensibilityOptions.ProgressHandler">
      <summary>Gets or sets a handler that is used to report progress during the file acquisition.</summary>
    </member>
    <member name="E:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileExtensibilityOptions.FileWillChange">
      <summary>This event is fired prior to a local file change (e.g. downloading, change date time stamp, update file references)</summary>
    </member>
    <member name="E:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileExtensibilityOptions.PostAcquire">
      <summary>This event is fired after acquiring all files.</summary>
    </member>
    <member name="E:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileExtensibilityOptions.PostFileAcquire">
      <summary>This event is fired after acquring a file.</summary>
    </member>
    <member name="E:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileExtensibilityOptions.PreAcquire">
      <summary>This event is fired prior to acquiring any files.</summary>
    </member>
    <member name="E:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileExtensibilityOptions.PreFileAcquire">
      <summary>This event is fired prior to acquiring a file.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileExtensibilityOptions.FileWillChangeEventArgs.FilePath">
      <summary>The path of the file that will be changed</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileExtensibilityOptions.PostAcquireEventArgs.ExecuteDefaultBehavior">
      <summary>The default behavior for PostAcquire event.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileExtensibilityOptions.PostAcquireEventArgs.Results">
      <summary>The result of the of acquiring the files.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileExtensibilityOptions.PostFileAcquireEventArgs.ExecuteDefaultBehavior">
      <summary>The default behavior for PostFileAcquire event.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileExtensibilityOptions.PostFileAcquireEventArgs.FileResult">
      <summary>The result of the of acquiring the file.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileExtensibilityOptions.PreAcquireEventArgs.ExecuteDefaultBehavior">
      <summary>The default behavior for PreAcquire event.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileExtensibilityOptions.PreAcquireEventArgs.AbortAcquire">
      <summary>Whether or not the acquisition should be aborted. This can be set to true to cause the acquisition to be aborted.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileExtensibilityOptions.PreAcquireEventArgs.FileOps">
      <summary>The File Operations for all the files to be acquired.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileExtensibilityOptions.PreAcquireEventArgs.Restrictions">
      <summary>The restrictions for each of the files to be acquired.</summary>
    </member>
    <member name="F:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileExtensibilityOptions.PreAcquireEventArgs.FileIdToAssociationsLookup">
      <summary>A lookup that maps a file's iteration id to all of the file associations it is involved in (as a parent or a child)</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileExtensibilityOptions.PreFileAcquireEventArgs.ExecuteDefaultBehavior">
      <summary>The default behavior for PreFileAcquire event.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileExtensibilityOptions.PreFileAcquireEventArgs.FileAcquisitionInfo">
      <summary>The Acquisition option for the file about to be acquired.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileExtensibilityOptions.PreFileAcquireEventArgs.FileAssociations">
      <summary>All the file associations that involve the file (as a child or a parent).</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileExtensibilityOptions.PreFileAcquireEventArgs.FileOp">
      <summary>The File Operation for the file about to be acquired.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileExtensibilityOptions.PreFileAcquireEventArgs.PreFileAcquisitionResult">
      <summary>The result of the PreFileAcquireEvent. This can be set to specify how the acquire of this file should continue.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileResolutionOptions.OverwriteOption">
      <summary>Gets or sets how to handle files that are on disk and writable or newer than the latest vault version.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileResolutionOptions.SyncWithRemoteSiteSetting">
      <summary>Gets or sets a value which determines how to handle files that are missing or out of date on the connected server</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileResolutionOptions.UpdateReferencesModel">
      <summary>Gets a model which controls how file references will be udated after they are downloaded. If null, no file updates will take place.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.AcquireFilesSettings.AcquireFileThreadingOptions.CancellationToken">
      <summary>Gets or sets the cancellation token that is used to determine if the background operation has been cancelled. The token can also be used to manually force a
cancel.</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Settings.FileRelationshipGatheringSettings.#ctor">
      <summary>Constructs an instance of the FileRelationshipGatheringSettings class</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Settings.FileRelationshipGatheringSettings.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Settings.FileRelationshipGatheringSettings)">
      <summary>Constructs a copy of an instance of the FileRelationshipGatheringSettings class</summary>
      <param>The object to use as the source of the copy</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.FileRelationshipGatheringSettings.DateBiased">
      <summary>Gets or sets whether we should use the "Date biased" approach of gathering dependencies.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.FileRelationshipGatheringSettings.IncludeAttachments">
      <summary>Gets or sets whether attachments of a source file should be consumed</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.FileRelationshipGatheringSettings.IncludeChildren">
      <summary>Gets whether children of a source file should be consumed. For example, an assembly depends on it's parts.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.FileRelationshipGatheringSettings.IncludeHiddenEntities">
      <summary>Gets or sets whether entities that are hidden should be included in the gathering results.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.FileRelationshipGatheringSettings.IncludeLibraryContents">
      <summary>Gets or sets whether gathered entities that are a part of a Vault Library should be consumed.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.FileRelationshipGatheringSettings.IncludeParents">
      <summary>Gets or sets whether parents of the source entity will be gathered. For example, the Assembly associated with a part.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.FileRelationshipGatheringSettings.IncludeRelatedDocumentation">
      <summary>Gets or sets whether related documentation that is associated with the entity will be gathered. For examplee, a DWG might be considered related documentation
for an assembly. The DWG is not considered an attachment or a dependent.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.FileRelationshipGatheringSettings.RecurseChildren">
      <summary>Gets or sets whether children should be included recursively, or if just the first level of children should be included</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.FileRelationshipGatheringSettings.RecurseParents">
      <summary>Gets or sets whether parents should be included recursively, or if just the first level parent should be included</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.FileRelationshipGatheringSettings.ReleaseBiased">
      <summary>Gets or sets that we should use the "Release biased" approach of gathering dependencies.</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.FileRelationshipGatheringSettings.VersionGatheringOption">
      <summary>Gets or sets an option which determines which versions of related files should be gathered.</summary>
    </member>
    <member name="E:Autodesk.DataManagement.Client.Framework.Vault.Settings.FileRelationshipGatheringSettings.PropertyChanged">
      <summary>Event fired when one of the options in this object are changed</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Settings.LinkGatheringSettings.#ctor">
      <summary>Constructs an instance of the LinkGatheringSettings class</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Settings.LinkGatheringSettings.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Settings.LinkGatheringSettings)">
      <summary>Constructs a copy of an instance of the LinkGatheringSettings class</summary>
      <param>The object to use as the source of the copy</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.LinkGatheringSettings.IncludeLinks">
      <summary>Gets or sets if links should be included in the result set. For example, a Folder that contains a link to a file or a Folder that contains a link to another
folder which contains files. This option only applies to entities that are Link Containers (ie. Folders or Custom Objects)</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.LinkGatheringSettings.LinkTypesToInclude">
      <summary>Gets or sets a list of entity class id's which represent the entity classes whose links should be followed when gathering entities. If null, then links to all
entity classes will be included</summary>
    </member>
    <member name="E:Autodesk.DataManagement.Client.Framework.Vault.Settings.LinkGatheringSettings.PropertyChanged">
      <summary>Event fired when one of the options in this object are changed</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Settings.RelationshipGatheringSettings.#ctor">
      <summary>Constructs an instance of the RelationshipGatheringSettings class</summary>
    </member>
    <member name="M:Autodesk.DataManagement.Client.Framework.Vault.Settings.RelationshipGatheringSettings.#ctor(Autodesk.DataManagement.Client.Framework.Vault.Settings.RelationshipGatheringSettings)">
      <summary>Constructs a copy of an instance of the RelationshipGatheringSettings class</summary>
      <param>The object to use as the source of the copy</param>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.RelationshipGatheringSettings.FileRelationshipSettings">
      <summary>Gets the subset of settings specific to file relationships</summary>
    </member>
    <member name="P:Autodesk.DataManagement.Client.Framework.Vault.Settings.RelationshipGatheringSettings.IncludeLinksSettings">
      <summary>Gets the subset of settings specific to Links.</summary>
    </member>
  </members>
</doc>