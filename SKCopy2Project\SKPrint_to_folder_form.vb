﻿Imports ACW = Autodesk.Connectivity.WebServices
Imports VDF = Autodesk.DataManagement.Client.Framework
Imports Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections

Public Class SKPrint_to_folder_form

    Public Shared m_conn As Connection = SKCopy2Project.SKCopy2ProjectCommandExtension.m_conn


    Private Sub Button3_Click(sender As Object, e As EventArgs) Handles Button3.Click
        ReDim SKCopy2Project.SKCopy2ProjectCommandExtension.print2fileMatrix(SKCopy2Project.SKCopy2ProjectCommandExtension.print2fileMatrixlen, 0)
        Close()

    End Sub

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        Try



            Dim lastfolder = TextBox1.Text
            Dim fb As New FolderBrowserDialog
            fb.RootFolder = Environment.SpecialFolder.MyComputer
            fb.SelectedPath = lastfolder

            If fb.ShowDialog = Windows.Forms.DialogResult.OK Then
                lastfolder = fb.SelectedPath
            End If
            TextBox1.Text = fb.SelectedPath.ToString

        Catch ex As Exception

        End Try


        '  FolderBrowserDialog1.ShowDialog()

    End Sub

    Private Sub Button2_Click(sender As Object, e As EventArgs) Handles Button2.Click
        Dim origPath As String = TextBox1.Text & "\"

        Button2.Enabled = False
        Cursor = Cursors.WaitCursor
        If TextBox1.Text IsNot "" Then

            Try
                If TextBox1.Text = "" Then
                    MessageBox.Show("Copying folder is not set")
                    Cursor = Cursors.Default
                    Button2.Enabled = True
                    Exit Sub

                ElseIf CheckBox2.Checked = False And CheckBox1.Checked = False Then
                    MessageBox.Show("PDF or DWG is not checked ")
                    Cursor = Cursors.Default
                    Button2.Enabled = True
                    Exit Sub
                Else

                    ''Try
                    ''    'set directory to registry
                    ''    TextBox1.Text = Reg_W("skprintpath", TextBox1.Text)
                    ''Catch ex As Exception

                    ''End Try

                    'delete tempfolder



                    Dim temppath As String = (Environment.GetFolderPath(Environment.SpecialFolder.UserProfile) & "\Documents\tempdownload\")
                    If System.IO.Directory.Exists(temppath) Then
                        'get all files
                        Dim di As New IO.DirectoryInfo(temppath)
                        di.Delete(True)

                    End If
                    If CheckBox2.Checked = True Then
                        'start downloading 
                        downloaddwfFiles(temppath)
                        Label2.Text = "DWFs Downloaded"
                        Me.Refresh()
                        'print
                        Label2.Text = "Starting Printing PDFs"
                        Me.Refresh()
                        createPDFs(temppath, origPath, True, Print1stPageOnly.Checked)
                        'last print done
                        Label2.Text = "PDFs Done!"
                        Me.Refresh()
                    End If
                    If CheckBox1.Checked = True Then
                        'create Inventor DWG to AutoCAD DWG.

                        'download DWGs
                        downloaddwgFiles(temppath)
                        Label2.Text = "DWGs Downloaded"
                        Me.Refresh()


                        Label2.Text = "Creating DWGs"
                        Me.Refresh()

                        'Open DWGs to Inventor and create AutoCAD DWG

                        createDWGs(temppath, origPath)

                        Label2.Text = "DWGs Done!"
                        Me.Refresh()

                    End If





                    'open folder
                    Try
                        Do Until System.IO.Directory.Exists(TextBox1.Text) = True
                        Loop
                        Call Shell("explorer /open," & TextBox1.Text, AppWinStyle.NormalFocus)
                    Catch ex As Exception
                    End Try

                End If
            Catch ex As Exception
                Cursor = Cursors.Default
                Button2.Enabled = True

            End Try
        Else
            MessageBox.Show("The folder is empty")
            Cursor = Cursors.Default
            Button2.Enabled = True
            GoTo Loppu

        End If
        Try
            'delete tempfolder
            Dim temppath As String = (Environment.GetFolderPath(Environment.SpecialFolder.UserProfile) & "\Documents\tempdownload\")
            If System.IO.Directory.Exists(temppath) Then
                'get all files
                Dim di As New IO.DirectoryInfo(temppath)
                di.Delete(True)
            End If
        Catch ex As Exception

        End Try

        Cursor = Cursors.Default
        Button2.Enabled = False

Loppu:
    End Sub

    Private Sub DataGridView1_CellContentClick(sender As Object, e As DataGridViewCellEventArgs) Handles DataGridView1.CellContentClick

    End Sub

    Private Sub SKPrint_to_folder_form_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Try
            'let's read info from table

            For row As Integer = 0 To SKCopy2Project.SKCopy2ProjectCommandExtension.print2fileMatrix.GetUpperBound(1)
                Dim gridrow As String() = New String() {SKCopy2Project.SKCopy2ProjectCommandExtension.print2fileMatrix(0, row),
                         SKCopy2Project.SKCopy2ProjectCommandExtension.print2fileMatrix(1, row),
                         SKCopy2Project.SKCopy2ProjectCommandExtension.print2fileMatrix(3, row),
                         SKCopy2Project.SKCopy2ProjectCommandExtension.print2fileMatrix(2, row),
                          SKCopy2Project.SKCopy2ProjectCommandExtension.print2fileMatrix(5, row),
                           SKCopy2Project.SKCopy2ProjectCommandExtension.print2fileMatrix(6, row),
                            SKCopy2Project.SKCopy2ProjectCommandExtension.print2fileMatrix(4, row),
                             SKCopy2Project.SKCopy2ProjectCommandExtension.print2fileMatrix(7, row),
                              SKCopy2Project.SKCopy2ProjectCommandExtension.print2fileMatrix(8, row)}
                DataGridView1.Rows.Add(gridrow)
                If SKCopy2Project.SKCopy2ProjectCommandExtension.print2fileMatrix(4, row) = "None" Then
                    DataGridView1.Rows(DataGridView1.RowCount - 1).Cells(6).Style.BackColor = Color.Red
                End If

            Next
            Me.Text = "SK Publish 'To Yard', version " & Me.GetType.Assembly.GetName.Version.Major & "." & Me.GetType.Assembly.GetName.Version.Minor
        Catch ex As Exception

        End Try

        ''Try
        ''    'get directory from registry
        ''    TextBox1.Text = Reg_R("skprintpath", "")
        ''Catch ex As Exception

        ''End Try
    End Sub

    Private Sub downloaddwgFiles(ByVal origpath As String)
        Try
            For i As Integer = 0 To SKCopy2Project.SKCopy2ProjectCommandExtension.print2fileMatrix.GetUpperBound(1)

                Dim targetfname As String = Nothing ' = itemNumber & "-" & SKCopy2Project.SKCopy2ProjectCommandExtension.print2fileMatrix(9, i) & "_" & origExt & "." & ext
                If RadioButton1.Checked = True Then
                    targetfname = SKCopy2Project.SKCopy2ProjectCommandExtension.print2fileMatrix(8, i) & "-" & SKCopy2Project.SKCopy2ProjectCommandExtension.print2fileMatrix(5, i) & ".dwg"
                ElseIf RadioButton2.Checked = True Then
                    targetfname = SKCopy2Project.SKCopy2ProjectCommandExtension.print2fileMatrix(8, i) & "-" & SKCopy2Project.SKCopy2ProjectCommandExtension.print2fileMatrix(6, i) & ".dwg"
                End If
                Dim id As String

                id = SKCopy2Project.SKCopy2ProjectCommandExtension.print2fileMatrix(1, i)
                Dim f As ACW.File = m_conn.WebServiceManager.DocumentService.GetFileById(id)

                If f IsNot Nothing And origpath IsNot Nothing Then
                    Dim settings As New VDF.Vault.Settings.AcquireFilesSettings(m_conn)
                    settings.LocalPath = New VDF.Currency.FolderPathAbsolute(origpath)

                    Dim fileiter As VDF.Vault.Currency.Entities.FileIteration
                    fileiter = getFileIteration(f.Name, m_conn)

                    settings.AddFileToAcquire(fileiter, VDF.Vault.Settings.AcquireFilesSettings.AcquisitionOption.Download)
                    'download
                    Dim results As VDF.Vault.Results.AcquireFilesResults = m_conn.FileManager.AcquireFiles(settings)

                    Try
                        'change name
                        If System.IO.File.Exists(origpath & f.Name) Then
                            My.Computer.FileSystem.RenameFile(origpath & f.Name, targetfname)
                            'change file as writable
                            Dim attributes As System.IO.FileAttributes
                            attributes = System.IO.File.GetAttributes(origpath & targetfname)
                            If (attributes And System.IO.FileAttributes.ReadOnly = System.IO.FileAttributes.ReadOnly) Then
                                ' nake file writable.
                                attributes = RemoveAttribute(attributes, System.IO.FileAttributes.ReadOnly)
                                System.IO.File.SetAttributes(origpath & targetfname, attributes)
                            End If

                        End If
                    Catch ex As Exception
                        MessageBox.Show("Mek:40 " & ex.Message)
                    End Try
                End If
            Next
        Catch ex As Exception
            MessageBox.Show("Mek:41 " & ex.Message)

        End Try
    End Sub


    Private Sub downloaddwfFiles(ByVal origpath As String)
        Try

            'let's figure out if this is dwf?
            Dim ext As String = "dwf"


            For i As Integer = 0 To SKCopy2Project.SKCopy2ProjectCommandExtension.print2fileMatrix.GetUpperBound(1)
                If SKCopy2Project.SKCopy2ProjectCommandExtension.print2fileMatrix(4, i) <> "None" Then ''tähän dwf.
                    Dim origExt As String = SKCopy2Project.SKCopy2ProjectCommandExtension.print2fileMatrix(2, i)

                    Dim origfname As String = SKCopy2Project.SKCopy2ProjectCommandExtension.print2fileMatrix(3, i) & ".dwf"
                    Dim targetfname As String = Nothing ' = itemNumber & "-" & SKCopy2Project.SKCopy2ProjectCommandExtension.print2fileMatrix(9, i) & "_" & origExt & "." & ext
                    If RadioButton1.Checked = True Then
                        targetfname = SKCopy2Project.SKCopy2ProjectCommandExtension.print2fileMatrix(8, i) & "-" & SKCopy2Project.SKCopy2ProjectCommandExtension.print2fileMatrix(5, i) & ".dwf"
                    ElseIf RadioButton2.Checked = True Then
                        targetfname = SKCopy2Project.SKCopy2ProjectCommandExtension.print2fileMatrix(8, i) & "-" & SKCopy2Project.SKCopy2ProjectCommandExtension.print2fileMatrix(6, i) & ".dwf"
                    End If


                    'download
                    Dim id As String

                    id = SKCopy2Project.SKCopy2ProjectCommandExtension.print2fileMatrix(7, i)


                        Dim f As ACW.File = m_conn.WebServiceManager.DocumentService.GetFileById(id)

                    If f IsNot Nothing And origpath IsNot Nothing Then
                        Dim settings As New VDF.Vault.Settings.AcquireFilesSettings(m_conn)
                        settings.LocalPath = New VDF.Currency.FolderPathAbsolute(origpath)
                        
                        Dim fileiter As VDF.Vault.Currency.Entities.FileIteration
                        fileiter = getFileIteration(f.Name, m_conn)

                        settings.AddFileToAcquire(fileiter, VDF.Vault.Settings.AcquireFilesSettings.AcquisitionOption.Download)
                        'download
                        Dim results As VDF.Vault.Results.AcquireFilesResults = m_conn.FileManager.AcquireFiles(settings)





                        'change name
                        Try
                            If System.IO.File.Exists(origpath & f.Name) Then
                                My.Computer.FileSystem.RenameFile(origpath & f.Name, targetfname)
                                'change file as writable
                                Dim attributes As System.IO.FileAttributes
                                attributes = System.IO.File.GetAttributes(origpath & targetfname)
                                If (attributes And System.IO.FileAttributes.ReadOnly = System.IO.FileAttributes.ReadOnly) Then
                                    ' nake file writable.
                                    attributes = RemoveAttribute(attributes, System.IO.FileAttributes.ReadOnly)
                                    System.IO.File.SetAttributes(origpath & targetfname, attributes)


                                End If

                            End If
                        Catch ex As Exception
                            MessageBox.Show("Mek:23 " & ex.Message)

                        End Try

                    End If

                End If
            Next
        Catch ex As Exception
            MessageBox.Show("Mek:24 " & ex.Message)

        End Try
    End Sub
    Shared Function getFileIteration _
           (nameOfFile As String,
                   connection As _
              VDF.Vault.Currency.
          Connections.Connection) _
           As VDF.Vault.Currency.
            Entities.FileIteration

        Dim conditions As ACW.SrchCond()

        ReDim conditions(0)

        Dim lCode As Long = 1

        Dim Defs As ACW.PropDef() =
      connection.WebServiceManager.
                   PropertyService.
    GetPropertyDefinitionsByEntityClassId("FILE")

        Dim Prop As ACW.PropDef = Nothing

        For Each def As ACW.PropDef In Defs
            If def.DispName =
                      "File Name" Then
                Prop = def
            End If
        Next def

        Dim searchCondition As _
    ACW.SrchCond = New ACW.SrchCond()

        searchCondition.PropDefId =
                              Prop.Id

        searchCondition.PropTyp =
    ACW.PropertySearchType.SingleProperty
        searchCondition.SrchOper = lCode

        searchCondition.SrchTxt = nameOfFile

        conditions(0) = searchCondition

        ' search for files
        Dim FileList As List _
    (Of Autodesk.Connectivity.WebServices.File) =
        New List _
    (Of Autodesk.Connectivity.WebServices.File) '()
        Dim sBookmark As String = String.Empty
        Dim Status As ACW.SrchStatus = Nothing

        While (Status Is Nothing OrElse
             FileList.Count < Status.TotalHits)

            Dim files As Autodesk.Connectivity.
    WebServices.File() = connection.WebServiceManager.
    DocumentService.FindFilesBySearchConditions _
                                 (conditions,
                Nothing, Nothing, True, True,
                             sBookmark, Status)

            If (Not files Is Nothing) Then
                FileList.AddRange(files)
            End If
        End While

        Dim oFileIteration As _
            VDF.Vault.Currency.Entities.
                   FileIteration = Nothing
        For i As Integer =
                  0 To FileList.Count - 1
            If FileList(i).Name =
                          nameOfFile Then
                oFileIteration =
               New VDF.Vault.Currency.
    Entities.FileIteration(connection,
                            FileList(i))
            End If
        Next

        Return oFileIteration

    End Function
    Private Sub createDWGs(ByVal origDir As String, ByVal targetdir As String)
        Dim dwgPaths As String() = Nothing
        Try
            dwgPaths = IO.Directory.GetFiles(origDir, "*.dwg", IO.SearchOption.AllDirectories)
        Catch ex As Exception
            MessageBox.Show("Mek:42 " & ex.Message)
        End Try

        'start to print
        If dwgPaths.Length > 0 Then 'file with path
            Label2.Text = "Starting Inventor"
            Me.Refresh()
            Dim oapp As Inventor.Application
            oapp = Nothing
            'open Inventor
            Dim isOpen As Boolean = False
            Try
                ''Try
                ''    oapp = System.Runtime.InteropServices.Marshal.GetActiveObject("Inventor.Application")
                ''    oapp.SilentOperation = True
                ''    isOpen = True
                ''Catch ex As Exception
                ''    If oapp Is Nothing Then
                ''        Dim inventorAppType As Type = System.Type.GetTypeFromProgID("Inventor.Application")
                ''        oapp = System.Activator.CreateInstance(inventorAppType)
                ''        oapp.SilentOperation = True

                ''        '   Delay(60)

                ''    End If
                ''End Try
                Try
                    Dim inventorAppType As Type = System.Type.GetTypeFromProgID("Inventor.Application")
                    oapp = System.Activator.CreateInstance(inventorAppType)
                    oapp.SilentOperation = True
                Catch ex As Exception

                End Try
            Catch ex As Exception
                MessageBox.Show("Mek:42 " & ex.Message)
                Exit Sub
            End Try

            'Inventor should started
            If oapp IsNot Nothing Then
                Label2.Text = "Inventor Started"
                Me.Refresh()
                'set project file to Default
                Dim currIpj As Inventor.DesignProject = oapp.DesignProjectManager.ActiveDesignProject
                Dim tempIpj As Inventor.DesignProject = oapp.DesignProjectManager.DesignProjects.ItemByName("Default")
                tempIpj.Activate()
                Dim oDWGAddIn As Inventor.TranslatorAddIn = Nothing
                'activate DWG ADdin
                Try
                    ' Set a reference to the DWG translator add-in. 

                    Dim ii As Long
                    For ii = 1 To oapp.ApplicationAddIns.Count
                        If oapp.ApplicationAddIns.Item(ii).
                            ClassIdString =
                            "{C24E3AC2-122E-11D5-8E91-0010B541CD80}" Then
                            oDWGAddIn = oapp.
                  ApplicationAddIns.Item(ii)
                            Exit For
                        End If
                    Next

                    If oDWGAddIn Is Nothing Then
                        MessageBox.Show("The DWG add-in could not be found.")
                        Exit Sub
                    End If
                    If Not oDWGAddIn.Activated Then
                        oDWGAddIn.Activate()
                    End If
                Catch ex As Exception
                End Try


                'now let's open files from dwgpaths and make 2d- dwg

                Dim options As Inventor.NameValueMap = Nothing
                options = oapp.TransientObjects.CreateNameValueMap
                options.Add("DeferUpdates", True)


                For i As Integer = 0 To dwgPaths.Length - 1

                    Try
                        'open 
                        Dim odoc As Inventor.DrawingDocument = oapp.Documents.OpenWithOptions(dwgPaths(i), options, False)
                        Dim fName As String = odoc.FullDocumentName.Split("\").Last
                        ''Dim oNameValueMap As Inventor.NameValueMap

                        ''oNameValueMap = oapp.TransientObjects.CreateNameValueMap()
                        ''Dim strIniFile As String
                        ''strIniFile = origDir & "DWGOut.ini"
                        ''oNameValueMap.Value("Export_Acad_IniFile") = strIniFile
                        ''Dim oContext As Inventor.TranslationContext

                        ''oContext = oapp.
                        ''TransientObjects.
                        ''CreateTranslationContext
                        ''oContext.Type =
                        ''Inventor.IOMechanismEnum.kFileBrowseIOMechanism

                        ''Dim oOutputFile As Inventor.DataMedium

                        ''oOutputFile = oapp.
                        ''TransientObjects.CreateDataMedium
                        ''oOutputFile.FileName = targetdir & fName

                        ''oDWGAddIn.SaveCopyAs(oapp.ActiveDocument,
                        ''                  oContext,
                        ''oNameValueMap,
                        ''oOutputFile)

                        Dim oContext As Inventor.TranslationContext
                        oContext = oapp.TransientObjects.CreateTranslationContext
                        oContext.Type = Inventor.IOMechanismEnum.kFileBrowseIOMechanism

                        ' Create a NameValueMap object
                        Dim oOptions As Inventor.NameValueMap
                        oOptions = oapp.TransientObjects.CreateNameValueMap


                        ' Create a DataMedium object
                        Dim oDataMedium As Inventor.DataMedium
                        oDataMedium = oapp.TransientObjects.CreateDataMedium

                        ' Check whether the translator has 'SaveCopyAs' options
                        If oDWGAddIn.HasSaveCopyAsOptions(odoc, oContext, oOptions) Then

                            Dim strIniFile As String
                            strIniFile = origDir & "tempDWGOut.ini"
                            ' Create the name-value that specifies the ini file to use.
                            oOptions.Value("Export_Acad_IniFile") = strIniFile
                        End If
                        'Set the destination file name
                        oDataMedium.FileName = targetdir & fName

                        'if file exists, delete
                        If System.IO.File.Exists(targetdir & fName) Then
                            System.IO.File.Delete(targetdir & fName)
                        End If

                        'Publish document.
                        Call oDWGAddIn.SaveCopyAs(odoc, oContext, oOptions, oDataMedium)
                        odoc.Close()
                        Label2.Text = i + 1 & "/" & dwgPaths.Length & " is saved"
                        Me.Refresh()
                    Catch ex As Exception

                    End Try

                Next




                'last Inventor settings back to original
                currIpj.Activate()

                oapp.SilentOperation = False
                If isOpen = False Then
                    oapp.Quit()
                    Label2.Text = "Inventor Closed successfully"
                    Me.Refresh()
                End If

            End If


        End If

        Try

        Catch ex As Exception
            MessageBox.Show("Mek:44 " & ex.Message)
        End Try
    End Sub
    Public Shared Sub createPDFs(ByVal origDir As String, ByVal targetdir As String, ByVal deletetempfolder As Boolean, ByVal Print1stPageOnly As Boolean)
        Dim dwfPaths As String() = Nothing
        'first get all _dwg.dwfs and their paths
        ' Dim pdfpath As String = origDir & "pdftemp\"
        Dim bpjfname As String = origDir & "settings.bpj"
        Try

            dwfPaths = IO.Directory.GetFiles(origDir, "*.dwf*", IO.SearchOption.AllDirectories)   ' * added to the end




            ''If System.IO.Directory.Exists(origDir & "pdftemp") = False Then
            ''    System.IO.Directory.CreateDirectory(origDir & "pdftemp")
            ''End If

        Catch ex As Exception
            MessageBox.Show("Mek:25 " & ex.Message)

        End Try


        'create ibj
        Try
            If dwfPaths.Length > 0 Then

                'open/modify/save ini file




                Try
                    Dim iniFile As String = (Environment.GetFolderPath(Environment.SpecialFolder.UserProfile) & "\AppData\Roaming\PDF Writer\Bullzip PDF Printer\settings.ini")
                    Dim plotDir As String = targetdir
                    Dim newLine As String
                    If Print1stPageOnly = True Then
                        newLine = "  output=" & plotDir & "<basedocname>page<PageNo>.pdf"
                    Else
                        newLine = "  output=" & plotDir & "<basedocname>.pdf"

                    End If


            If System.IO.File.Exists(iniFile) Then
                        Dim lines() As String = IO.File.ReadAllLines(iniFile)
                        For ii As Integer = 0 To lines.Length - 1
                            If lines(ii).Contains("  output=") Then
                                lines(ii) = newLine

                            ElseIf lines(ii).Contains("  confirmoverwrite=") Then
                                lines(ii) = "  confirmoverwrite=no"
                                Exit For
                            ElseIf lines(ii).Contains("  showsaveas=") Then
                                Exit For
                                lines(ii) = "  showsaveas=never"
                            ElseIf lines(ii).Contains("  showsettings=") Then
                                lines(ii) = "  showsettings=never"
                                Exit For
                            ElseIf lines(ii).Contains("  confirmoverwrite=") Then
                                lines(ii) = "  confirmoverwrite=no"
                                Exit For
                            ElseIf lines(ii).Contains("confirmnewfolder=") Then
                                lines(ii) = "confirmnewfolder=no"
                                Exit For
                            ElseIf lines(ii).Contains("  appendifexists=") Then
                                lines(ii) = "  appendifexists=no"
                                Exit For
                            ElseIf lines(ii).Contains("  appendifexists=") Then
                                lines(ii) = "  appendifexists=no"
                                Exit For
                            ElseIf lines(ii).Contains("  openfolder=") Then
                                lines(ii) = "  openfolder=no"
                                Exit For
                            ElseIf lines(ii).Contains("  showpdf=") Then
                                lines(ii) = "  showpdf=no"
                                Exit For
                            ElseIf lines(ii).Contains("  suppresserrors=") Then
                                lines(ii) = "  suppresserrors=yes"
                                Exit For
                            ElseIf lines(ii).Contains("  subsetfonts=") Then
                                lines(ii) = "  subsetfonts=yes"
                                Exit For
                            ElseIf lines(ii).Contains("  showprogress=") Then
                                lines(ii) = "  showprogress=no"
                                Exit For
                            ElseIf lines(ii).Contains("  showprogressfinished") Then
                                lines(ii) = "  showprogressfinished=no"
                                Exit For
                            ElseIf lines(ii).Contains("  showpdf") Then
                                lines(ii) = "  showpdf=never"
                                Exit For
                            ElseIf lines(ii).Contains("  showsettings") Then
                                lines(ii) = "  showsettings=never"
                                Exit For
                            ElseIf lines(ii).Contains("  rememberlastfoldername") Then
                                lines(ii) = "  rememberlastfoldername=no"
                                Exit For
                            ElseIf lines(ii).Contains("  confirmnewfolder") Then
                                lines(ii) = "  confirmnewfolder=no"
                                Exit For
                            End If
                        Next
                        IO.File.WriteAllLines(iniFile, lines)
                    End If
                Catch ex As Exception
                    MessageBox.Show("mek:26 " & ex.Message)
                End Try



                Try

                    Dim printColor = "0" '0=color, 1= BW
                    Dim MarkupColor = "" '0=color, 1= BW

                    If System.IO.File.Exists(bpjfname) = True Then
                        System.IO.File.Delete(bpjfname)
                    End If
                    Dim writer1 As New System.IO.StreamWriter(bpjfname, True)
                    writer1.Write("<configuration_file>")
                    Dim printer As String = "Bullzip PDF Printer"
                    For i As Integer = 0 To dwfPaths.Count - 1
                        writer1.Write("<DWF_File FileName ='" & dwfPaths(i) & "' PageSize='' NoOfSections='1' Print_to_scale='100' Print_Style='0' Print_What='0' Fit_To_Paper='-1' Paper_Size='' Paper_Size_Width='' Paper_Size_Height='' Orientation='' Number_of_copies='1' PrinterName='" & printer & "' Page_Range='0' Print_Range_Str='' Reverse_Order='0' Collate='0' printColor='" & printColor & "' MarkupColor='" & MarkupColor & "' printAlignment='4' Use_DWF_Paper_Size='-1' PrintasImage='0' PaperName=''  />") 'HPIPMediaID='0' HPIPExcludeEModel='0' HPIPPaperName='' useHPIP='0'
                    Next
                    writer1.Write("</configuration_file>")
                    writer1.Close()


                    'print using design review
                    Dim psInfo As New System.Diagnostics.ProcessStartInfo("C:\Program Files (x86)\Autodesk\Autodesk Design Review\DesignReview.exe", Chr(34) & bpjfname & Chr(34))
                    psInfo.WindowStyle = ProcessWindowStyle.Hidden
                    System.Diagnostics.Process.Start(psInfo)
                Catch ex As Exception
                    MessageBox.Show("mek:27 " & ex.Message)
                End Try

                Try

startTask:
                    For Each p As Process In Process.GetProcesses

                        If p.ProcessName Like "DesignReview*" = True Then
                            GoTo startTask
                        End If
                        If p.ProcessName Like "gui.exe*" = True Then
                            GoTo startTask
                        End If
                    Next
                    'lets wait until pdf is done

                Catch ex As Exception
                    MessageBox.Show("mek:27.1 " & ex.Message)
                End Try


                '''move pdfs to final position
                '''wait until last pdf has plotted


                ''Try


                ''    Dim fname = dwfPaths(dwfPaths.Count - 1).Split("\").Last
                ''    Dim targetname = origDir & fname.Replace("dwf", "pdf")

                ''    Do While System.IO.File.Exists(targetname) = False

                ''    Loop

                ''Catch ex As Exception
                ''    MessageBox.Show("Mek:29 " & ex.Message)
                ''End Try

                '''copy pdfs to target places.

                ''Try




                ''    For i = 0 To dwfPaths.Count - 1
                ''        Dim fname As String = dwfPaths(i).Split("\").Last
                ''        Dim targetfolder As String = dwfPaths(i).Replace(fname, "").Replace("\dwf\", "\pdf\")

                ''        Dim resultname As String = fname.Replace("dwf", "pdf")
                ''        Try
                ''            If System.IO.File.Exists(targetfolder & resultname) Then
                ''                System.IO.File.Delete(targetfolder & resultname)
                ''            End If
                ''        Catch ex As Exception

                ''        End Try


                ''        My.Computer.FileSystem.CopyFile(origDir & resultname, targetfolder & resultname, Microsoft.VisualBasic.FileIO.UIOption.OnlyErrorDialogs, Microsoft.VisualBasic.FileIO.UICancelOption.DoNothing)

                ''    Next
                ''Catch ex As Exception
                ''    MessageBox.Show("Mek:30 " & ex.Message)
                ''End Try

                'try rename pdfs and delete -2 pdfs.
                Try
                    If Print1stPageOnly = True Then
                        Dim pdfPaths As String()
                        Dim pdfPathsTemp As String()
begincalc:
                        pdfPaths = IO.Directory.GetFiles(targetdir, "*.pdf", IO.SearchOption.TopDirectoryOnly)
                        waitSome(5000)
                        pdfPathsTemp = IO.Directory.GetFiles(targetdir, "*.pdf", IO.SearchOption.TopDirectoryOnly)
                        If pdfPaths.Length <> pdfPathsTemp.Length Then
                            GoTo begincalc
                        End If



                        If pdfPaths.Count > 0 Then
                            For i As Integer = 0 To pdfPaths.Count - 1
                                Dim origname As String = pdfPaths(i).Substring(0, pdfPaths(i).Length - 4).Split("\").Last
                                Dim fLen As Integer = origname.Length
                                If origname.Substring(fLen - 5) = "page1" Then
                                    Try
                                        My.Computer.FileSystem.RenameFile(pdfPaths(i), origname.Replace("page1", "") & "." & pdfPaths(i).Split(".").Last)
                                        Do Until My.Computer.FileSystem.FileExists(pdfPaths(i)) = False
                                        Loop

                                    Catch ex As Exception

                                    End Try

                                End If
                                If origname.Substring(fLen - 5, 4) = "page" And origname.Substring(fLen - 1) > 1 Then
                                    Try
                                        My.Computer.FileSystem.DeleteFile(pdfPaths(i))
                                        Do Until My.Computer.FileSystem.FileExists(pdfPaths(i)) = False
                                        Loop
                                    Catch ex As Exception

                                    End Try
                                End If


                            Next



                        End If


                    End If
                Catch ex As Exception

                End Try




                'delete pdf temp
                Try

                    If deletetempfolder = True Then



                        If System.IO.Directory.Exists(origDir) Then
                            Dim di As New IO.DirectoryInfo(origDir)
                            di.Delete(True)
                        End If
                    End If
                Catch ex As Exception
                    MessageBox.Show("Mek:31 " & ex.Message)
                End Try



                'delete bpj
                ''Try
                ''    System.IO.File.Delete(bpjfname)
                ''Catch ex As Exception
                ''    MessageBox.Show("Mek:32 " & ex.Message)
                ''End Try

            End If
        Catch ex As Exception
            MessageBox.Show("Mek:28 " & ex.Message)
        End Try
        'clear dwfpaths
        dwfPaths = Nothing


    End Sub

    Private Shared Sub waitSome(ByVal interval As Integer)
        Dim sw As New Stopwatch
        sw.Start()
        Do While sw.ElapsedMilliseconds < interval
            ' Allows UI to remain responsive
            Application.DoEvents()
        Loop
        sw.Stop()
    End Sub
    Public Shared Function RemoveAttribute(ByVal attributes As System.IO.FileAttributes, ByVal attributesToRemove As System.IO.FileAttributes) As System.IO.FileAttributes
        Return attributes And (Not attributesToRemove)
    End Function

End Class