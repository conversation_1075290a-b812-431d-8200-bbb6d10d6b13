﻿
Imports Autodesk.Connectivity.Explorer.Extensibility
Imports Autodesk.Connectivity.WebServices
Imports Autodesk.Connectivity.WebServicesTools
Imports ACW = Autodesk.Connectivity.WebServices
Imports VDF = Autodesk.DataManagement.Client.Framework
Imports VDFVF = Autodesk.DataManagement.Client.Framework.Vault.Forms
Imports VDFVCP = Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties
Imports Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections
Public Class skAddBomRow
    Private m_conn As Connection = SKCopy2Project.SKCopy2ProjectCommandExtension.m_conn

    Private Sub skAddBomRow_Load(sender As Object, e As EventArgs) Handles MyBase.Load
        Me.Text = "SK Add BOM Row, version " & Me.GetType.Assembly.GetName.Version.Major & "." & Me.GetType.Assembly.GetName.Version.Minor
    End Sub

    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        Try
            If TextBox1.Text <> "" Then

                'then check the item number exist
                Try
                    '   Dim itemExists As Item = SKCopy2Project.SKCopy2ProjectCommandExtension.SKAddBomRowmgr.ItemService.GetLatestItemByItemNumber(TextBox1.Text)

                    Dim itemPropDefs As PropDef() =
                    SKCopy2Project.SKCopy2ProjectCommandExtension.SKAddBomRowmgr.PropertyService.GetPropertyDefinitionsByEntityClassId("ITEM")

                    Dim NumberPropDef As PropDef =
                    itemPropDefs.[Single](Function(n) n.SysName = "Number")

                    Dim NumberExist As New SrchCond() With {
       .PropDefId = NumberPropDef.Id,
      .PropTyp = PropertySearchType.SingleProperty,
      .SrchOper = 3,
       .SrchRule = SearchRuleType.Must,
       .SrchTxt = TextBox1.Text
}

                    Dim bookmark As String = String.Empty
                    Dim status As SrchStatus = Nothing
                    Dim totalResults As New List(Of Item)()
                    Dim results As Item() =
                    SKCopy2Project.SKCopy2ProjectCommandExtension.SKAddBomRowmgr.ItemService.FindItemRevisionsBySearchConditions(
           New SrchCond() {NumberExist}, Nothing, True, bookmark, status)
                    If results.Length > 0 Then
                        For Each selection As ISelection In SKCopy2Project.SKCopy2ProjectCommandExtension.SKAddBomRowSelections
                            Dim selectedItem As Item = Nothing
                            selectedItem = SKCopy2Project.SKCopy2ProjectCommandExtension.SKAddBomRowmgr.ItemService.GetLatestItemByItemNumber(selection.Label)
                            Dim selectedItemRevision As VDF.Vault.Currency.Entities.ItemRevision = New VDF.Vault.Currency.Entities.ItemRevision(SKCopy2Project.SKCopy2ProjectCommandExtension.m_conn, selectedItem)
                            'start to edit selected item
                            Dim _item As Item
                            Try

                                _item = m_conn.WebServiceManager.ItemService.EditItems(New Long() {selectedItem.RevId})(0)
                                Dim Bom As ItemBOM = m_conn.WebServiceManager.ItemService.GetItemBOMByItemIdAndDate(_item.Id, Now, BOMTyp.Tip, BOMViewEditOptions.OmitParents)

                                Dim Updates() As ItemAssocParam
                                ReDim Updates(-1)

                                'first check that item is not already added
                                Dim isInBom As Boolean = False
                                If Bom.ItemAssocArray IsNot Nothing Then


                                    For Each itm As ItemAssoc In Bom.ItemAssocArray
                                        If itm.CldItemID = results(0).Id Then
                                            isInBom = True
                                            MessageBox.Show(TextBox1.Text & " exists already in " & _item.ItemNum & "!")
                                        End If
                                    Next
                                End If
                                m_conn.WebServiceManager.ItemService.UndoEditItems(New Long() {_item.Id})


                                If isInBom = False Then

                                    '''   For Each itm In Bom.ItemAssocArray
                                    ''Dim IAPUpdate As New ItemAssocParam
                                    ''With IAPUpdate
                                    ''    ' .CldItemID = itm.CldItemID
                                    ''    .Id = results(0).Id
                                    ''    .Quant = 1
                                    ''    .EditAct = BOMEditAction.Add ' IIf(results(0).Id, BOMEditAction.Add, BOMEditAction.Update) 
                                    ''End With



                                    ''ReDim Preserve Updates(Updates.Length)
                                    ''    Updates(Updates.Length - 1) = IAPUpdate



                                    '''  Next
                                    '''  
                                    Try
                                        'add bom
                                        AddBOMRow_WB(selectedItem, results(0))


                                    Catch ex As Exception

                                    End Try
                                End If






                            Catch ex As Exception
                                m_conn.WebServiceManager.ItemService.UndoEditItems(New Long() {_item.Id})
                                MessageBox.Show("Vault Error " & ex.Message)
                            End Try

                            ' add bom row


                        Next
                    Else
                        MessageBox.Show("Item does not exist.")
                        Exit Sub
                    End If
                Catch ex As Exception

                End Try
            Else
                MessageBox.Show("Item number field is empty.")
                Exit Sub
            End If
        Catch ex As Exception

        End Try

        TextBox1.Text = ""
        Label1.Text = "Done!"
    End Sub

    Public Shared Sub AddBOMRow_WB(parentItem As Item, childItem As Item)
        Dim item2 As Item = Nothing
        Dim m_conn As Connection = SKCopy2Project.SKCopy2ProjectCommandExtension.m_conn
        Try
            item2 = m_conn.WebServiceManager.ItemService.EditItems(New Long() {parentItem.RevId})(0)

            Dim options As BOMViewEditOptions = BOMViewEditOptions.Defaults Or BOMViewEditOptions.ReturnOccurrences _
                         Or BOMViewEditOptions.ReturnExcluded Or BOMViewEditOptions.ReturnUnassignedComponents

            ' Get the ItemBOM from the Item being edited
            Dim bom As ItemBOM = m_conn.WebServiceManager.ItemService.GetItemBOMByItemIdAndDate _
                                    (item2.Id, DateTime.MinValue, BOMTyp.Latest, options)


            Dim newBomItemAssocParam2 As New ItemAssocParam
            newBomItemAssocParam2.Id = item2.Id
            newBomItemAssocParam2.CldItemID = childItem.Id
            newBomItemAssocParam2.EditAct = BOMEditAction.Add
            newBomItemAssocParam2.Quant = 1
            newBomItemAssocParam2.PositionNum = bom.ItemRevArray.Length
            newBomItemAssocParam2.BOMOrder = bom.ItemRevArray.Length

            Dim bom2 As ItemBOM = m_conn.WebServiceManager.ItemService.UpdateItemBOMAssociations _
                      (item2.Id, {newBomItemAssocParam2}, BOMViewEditOptions.ReturnBOMFragmentsOnEdits)

            m_conn.WebServiceManager.ItemService.UpdateAndCommitItems(New Item() {item2})

        Catch ex As Exception
            MsgBox(ex.Message)
            m_conn.WebServiceManager.ItemService.UndoEditItems(New Long() {item2.Id})
        End Try
    End Sub

    Private Sub Button2_Click(sender As Object, e As EventArgs) Handles Button2.Click
        Close()
        Exit Sub
    End Sub
End Class