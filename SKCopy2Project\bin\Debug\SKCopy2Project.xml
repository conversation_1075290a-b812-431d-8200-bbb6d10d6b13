﻿<?xml version="1.0"?>
<doc>
<assembly>
<name>
SKCopy2Project
</name>
</assembly>
<members>
<member name="T:SKCopy2Project.My.Resources.Resources">
<summary>
  A strongly-typed resource class, for looking up localized strings, etc.
</summary>
</member>
<member name="P:SKCopy2Project.My.Resources.Resources.ResourceManager">
<summary>
  Returns the cached ResourceManager instance used by this class.
</summary>
</member>
<member name="P:SKCopy2Project.My.Resources.Resources.Culture">
<summary>
  Overrides the current thread's CurrentUICulture property for all
  resource lookups using this strongly typed resource class.
</summary>
</member>
<member name="T:SKCopy2Project.ItabERPExport.ItabERPExportCommandExtension">
 <summary>
 This class implements the IExtension interface, which means it tells Vault Explorer what 
 commands and custom tabs are provided by this extension.
 </summary>
</member>
<member name="M:SKCopy2Project.ItabERPExport.ItabERPExportCommandExtension.CommandSites">
 <summary>
 This function tells Vault Explorer what custom commands this extension provides.
 Part of the IExtension interface.
 </summary>
 <returns>A collection of CommandSites, which are collections of custom commands.</returns>
</member>
<member name="M:SKCopy2Project.ItabERPExport.ItabERPExportCommandExtension.DetailTabs">
 <summary>
 This function tells Vault Explorer what custom tabs this extension provides.
 Part of the IExtension interface.
 </summary>
 <returns>A collection of DetailTabs, each object represents a custom tab.</returns>
</member>
<member name="M:SKCopy2Project.ItabERPExport.ItabERPExportCommandExtension.OnLogOn(Autodesk.Connectivity.Explorer.Extensibility.IApplication)">
 <summary>
 This function is called after the user logs in to the Vault Server.
 Part of the IExtension interface.
 </summary>
 <param name="application">Provides information about the running application.</param>
</member>
<member name="M:SKCopy2Project.ItabERPExport.ItabERPExportCommandExtension.OnLogOff(Autodesk.Connectivity.Explorer.Extensibility.IApplication)">
 <summary>
 This function is called after the user is logged out of the Vault Server.
 Part of the IExtension interface.
 </summary>
 <param name="application">Provides information about the running application.</param>
</member>
<member name="M:SKCopy2Project.ItabERPExport.ItabERPExportCommandExtension.OnShutdown(Autodesk.Connectivity.Explorer.Extensibility.IApplication)">
 <summary>
 This function is called before the application is closed.
 Part of the IExtension interface.
 </summary>
 <param name="application">Provides information about the running application.</param>
</member>
<member name="M:SKCopy2Project.ItabERPExport.ItabERPExportCommandExtension.OnStartup(Autodesk.Connectivity.Explorer.Extensibility.IApplication)">
 <summary>
 This function is called after the application starts up.
 Part of the IExtension interface.
 </summary>
 <param name="application">Provides information about the running application.</param>
</member>
<member name="M:SKCopy2Project.ItabERPExport.ItabERPExportCommandExtension.HiddenCommands">
 <summary>
 This function tells Vault Exlorer which default commands should be hidden.
 Part of the IExtension interface.
 </summary>
 <returns>A collection of command names.</returns>
</member>
<member name="M:SKCopy2Project.ItabERPExport.ItabERPExportCommandExtension.CustomEntityHandlers">
 <summary>
 This function allows the extension to define special behavior for Custom Entity types.
 Part of the IExtension interface.
 </summary>
 <returns>A collection of CustomEntityHandler objects.  Each object defines special behavior
 for a specific Custom Entity type.</returns>
</member>
<member name="M:SKCopy2Project.ItabERPExport.ItabERPExportCommandExtension.ItabErpExportCommandHandler(System.Object,Autodesk.Connectivity.Explorer.Extensibility.CommandItemEventArgs)">
 <summary>
 This is the function that is called whenever the custom command is executed.
 </summary>
 <param name="s">The sender object.  Usually not used.</param>
 <param name="e">The event args.  Provides additional information about the environment.</param>
</member>
<member name="M:SKCopy2Project.ItabERPExport.ItabERPExportCommandExtension.propertyTab_SelectionChanged(System.Object,Autodesk.Connectivity.Explorer.Extensibility.SelectionChangedEventArgs)">
 <summary>
 This function is called whenever our custom tab is active and the selection has changed in the main grid.
 </summary>
 <param name="sender">The sender object.  Usually not used.</param>
 <param name="e">The event args.  Provides additional information about the environment.</param>
</member>
</members>
</doc>
