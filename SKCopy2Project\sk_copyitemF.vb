﻿Imports Inventor

Imports Excel = Microsoft.Office.Interop.Excel
Imports Autodesk.Connectivity.Explorer.Extensibility
Imports Autodesk.Connectivity.WebServices
Imports Autodesk.Connectivity.WebServicesTools
Imports ACW = Autodesk.Connectivity.WebServices
Imports VDF = Autodesk.DataManagement.Client.Framework
Imports VDFVF = Autodesk.DataManagement.Client.Framework.Vault.Forms
Imports VDFVCP = Autodesk.DataManagement.Client.Framework.Vault.Currency.Properties
Imports Autodesk.DataManagement.Client.Framework.Vault.Currency.Connections


'Imports System.Runtime.CompilerServices.Extension
Public Class sk_copyitemF
    Private m_conn As Connection = SKCopy2Project.SKCopy2ProjectCommandExtension.m_conn
    Private projectfolderName As String
    Private projectfolderID As Long

    Private SK_ITEM As String
    Private Shared rownumb As Integer = 17
    Public Shared masterArr(rownumb, 0) As String
    Public Shared drwArr(,) As String
    Public Shared derivedArr(,) As String
    Private fileiteration As Object
    Private SKnumberingcheme As Integer
    Private numbofModels As Integer = Nothing
    Private numbofDeriveds As Integer = Nothing
    Private numbofDrawings As Integer = Nothing
    Private numbofTotal As Integer = Nothing
    Private numbofSteps As Double = Nothing
    Private progressStep As Double = Nothing
    Private proggStep As Integer = 1
    '
    Private Shared SK_Shipserie As String()
    Private Shared sk_ship As Object(,)
    Private Shared SK_ShipCode As String()
    Private Shared SK_ProjectName As String()
    Private Shared SK_Yard As String()
    Private Shared SK_Area As Object(,)
    Private Shared sk_AreaCode As String()
    Private rowIndex As Integer = 0





    ' Public Sub New(conn As Connection)
    '     InitializeComponent()

    '    m_conn = co
    '  End Sub
    Private Sub Form2_Load(sender As Object, e As EventArgs) Handles MyBase.Load

        Try
            Dim settings As New VDF.Vault.Forms.Settings.LoginSettings
            settings.AutoLoginMode = VDFVF.Settings.LoginSettings.AutoLoginModeValues.RestoreAndExecute
            '   Dim ee As CommandItemEventArgs = SKCopy2Project.SKCopy2ProjectCommandExtension.ee
            '  m_conn = ee.Context.Application.Connection
            '  m_conn = VDF.Vault.Forms.Library.Login(settings)
            If m_conn Is Nothing Then
                MessageBox.Show("Invalid login")
                Exit Sub
            End If
            'let's get correct numberinscheme

            Dim mgr As WebServiceManager = m_conn.WebServiceManager
            Dim entityClassId As String = VDF.Vault.Currency.Entities.EntityClassIds.Files
            Dim fNumchemes As NumSchm() = mgr.NumberingService.GetNumberingSchemes(entityClassId, NumSchmType.ApplicationDefault)

            For Each fNumcheme As ACW.NumSchm In fNumchemes
                If fNumcheme.Name = "SK-Custom-Code" Then
                    SKnumberingcheme = fNumcheme.SchmID
                End If
            Next


        Catch ex As Exception
            MessageBox.Show(ex.Message & " Mek:LoginError")
        End Try




        ' m_conn = Autodesk.DataManagement.Client.Framework.Vault.Services.IVaultConnectionManagerService(getexistingconnection)
        'read folder from registry
        Dim regSK_Projectfolder As String = Reg_R("SK_Projectfolder_item_F", "$/")
        Dim regSK_toShip As String = Reg_R("SK_toShip_F", "")
        Dim regSK_ERP_PROJECT As String = Reg_R("SK_ERP_PROJECT_F", "")
        Dim regSK_PROJECT_NAME As String = Reg_R("SK_PROJECT_NAME_F", "")
        m_sourcePathTextBox.Text = regSK_Projectfolder
        TextBoxShipTo.Text = regSK_toShip
        TextBoxErpProject.Text = regSK_ERP_PROJECT
        TextBoxProjectName.Text = regSK_PROJECT_NAME






        If regSK_Projectfolder IsNot Nothing Then
            'lets get the folder id for project
            Dim projectFolder As VDF.Vault.Currency.Entities.Folder = findfolder(m_sourcePathTextBox.Text)

            Me.Text = "SK Copy Item, version " & Me.GetType.Assembly.GetName.Version.Major & "." & Me.GetType.Assembly.GetName.Version.Minor



        End If
    End Sub

    Private Function autoCompleteTextBox(ByVal col As AutoCompleteStringCollection, tBox As Windows.Forms.TextBox, mylist As String())
        If Not mylist Is Nothing Then
            '  cBox.Visible = False

            For Each str As String In mylist
                col.Add(str.ToString)
                ' tBox.DisplayMember = str.ToString
                ' cBox.ValueMember = str.ToString

            Next
            tBox.AutoCompleteSource = AutoCompleteSource.CustomSource
            tBox.AutoCompleteMode = AutoCompleteMode.SuggestAppend

            tBox.AutoCompleteCustomSource = col

            'cBox.Visible = true
        End If
    End Function
    Private Sub DataGridView1_CellMouseUp_1(ByVal sender As System.Object, ByVal e As System.Windows.Forms.DataGridViewCellMouseEventArgs) Handles DataGridView1.CellMouseUp
        If e.Button = MouseButtons.Right Then
            Me.DataGridView1.Rows(e.RowIndex).Selected = True
            Me.rowIndex = e.RowIndex
            Me.DataGridView1.CurrentCell = Me.DataGridView1.Rows(e.RowIndex).Cells(3)
            Me.ContextMenuStrip1.Show(Me.DataGridView1, e.Location)
            ContextMenuStrip1.Show(Cursor.Position)
        End If
    End Sub
    Private Sub ContextMenuStrip1_Click(ByVal sender As System.Object, ByVal e As System.EventArgs) Handles ContextMenuStrip1.Click
        If Not Me.DataGridView1.Rows(Me.rowIndex).IsNewRow Then
            Me.DataGridView1.Rows.RemoveAt(Me.rowIndex)
        End If
    End Sub

    Private Function makeCombolist(cBox As ComboBox, mylist As String())
        If Not mylist Is Nothing Then
            cBox.Items.Clear()

            For Each str As String In mylist
                cBox.Items.Add(str)
            Next
        End If
    End Function
    Private Function ClearCombo(ByVal tabindexparent As Integer)
        'got through form components
        For Each ctrl As Control In Me.Controls
            If TypeOf ctrl Is ComboBox Then
                Dim cBox As ComboBox = ctrl
                'get tabindex from comp
                If tabindexparent < cBox.TabIndex Then
                    cBox.Text = ""
                End If

            ElseIf TypeOf ctrl Is Windows.Forms.TextBox Then
                Dim tBox As Windows.Forms.TextBox = ctrl
                'get tabindex from comp
                If tabindexparent < tBox.TabIndex Then
                    tBox.Text = ""

                End If
            End If
        Next


    End Function

    Public Class FolderFilter
        Inherits VDFVF.Settings.SelectEntitySettings.EntityFilter

        Public Sub New(ByVal displayName As String)
            MyBase.New(displayName)
        End Sub

        Public Overrides Function CanDisplayEntity(ByVal entity As Autodesk.DataManagement.Client.Framework.Vault.Currency.Entities.IEntity) As Boolean
            Return entity.EntityClass.Id = "FLDR"
        End Function
    End Class
    '
    Private Sub Button1_Click(sender As Object, e As EventArgs) Handles Button1.Click
        ''Dim settings = New VDFVF.Settings.SelectEntitySettings()
        ''settings.PersistenceKey = "MekSystems.SKCopy2ProjectApp.MyDialog"
        ''settings.MultipleSelect = False
        ''settings.ActionableEntityClassIds.Add(VDF.Vault.Currency.Entities.EntityClassIds.Folder)
        Dim settings = New VDFVF.Settings.SelectEntitySettings()
        ''settings.PersistenceKey = "MekSystems.SKCopy2ProjectApp.MyDialog"
        ''settings.MultipleSelect = False
        '''settings.ActionableEntityClassIds.Add(VDF.Vault.Currency.Entities.EntityClassIds.Folder)
        settings.ShowFolderView = True
        ''settings.SelectionTextLabel = "Folder"
        'settings.ActionableEntityClassIds.Remove(VDF.Vault.Currency.Entities.EntityClassIds.Files)

        settings.ActionButtonEnablementRule = VDFVF.Settings.SelectEntitySettings.ActionButtonEnablementRules.MustExist
        settings.ActionButtonNavigatesContainers = False
        settings.DialogCaption = "Select folder to move into"
        settings.MultipleSelect = False
        settings.ShowHiddenChildren = False
        settings.SelectionTextLabel = "Folder"
        Dim filter As FolderFilter = New FolderFilter("Folders")
        Dim filters As VDFVF.Settings.SelectEntitySettings.EntityFilter() = {filter}
        settings.ConfigureFilters("Folders", filters, filter)
        Dim folderRes As VDFVF.Results.SelectEntityResults = Autodesk.DataManagement.Client.Framework.Vault.Forms.Library.SelectEntity(m_conn, settings)
        Dim mgr As WebServiceManager = m_conn.WebServiceManager

        '   m_conn = SKCopy2Project.SKCopy2ProjectCommandExtension.ee
        ' run the dialog
        ' Dim results = VDFVF.Library.SelectEntity(m_conn, settings)

        ' fill the textbox with the selected file path
        If folderRes IsNot Nothing Then
            Dim selected = folderRes.SelectedEntities.SingleOrDefault()
            If selected Is Nothing Then
                Return
            End If
            Dim selectedFolder = TryCast(selected, VDF.Vault.Currency.Entities.Folder)
            If selectedFolder Is Nothing Then
                Return
            End If
            ' m_conn.FileManager.LoadParentFolders(selectedFolder.ToSingleArray())
            m_sourcePathTextBox.Text = selectedFolder.FullName
        End If







        Reg_W("SK_Projectfolder_item_F", m_sourcePathTextBox.Text)
        'lets get the folder id for project
        Dim projectFolder As VDF.Vault.Currency.Entities.Folder = findfolder(m_sourcePathTextBox.Text)
        projectfolderID = projectFolder.Id
        projectfolderName = m_sourcePathTextBox.Text

        'lets find components


    End Sub

    Private Sub Button2_Click(sender As Object, e As EventArgs) Handles Button2.Click
        '  Dim logoutsettings As New VDFVF.Settings.LogoutSettings
        ' logoutsettings.LogOutMessage = Nothing

        'Dim settings As New VDF.Vault.Forms.Settings.LoginSettings
        'VDFVF.Library.Logout(m_conn, logoutsettings, Nothing)

        Close()


    End Sub

    Function findfolder(ByVal folderstring As String) As VDF.Vault.Currency.Entities.Folder
        Dim myFldrCol As System.Collections.Generic.List _
           (Of VDF.Vault.Currency.Entities.Folder)
        myFldrCol = m_conn.FolderManager.
    GetChildFolders(m_conn.FolderManager.RootFolder,
                                           True, False)


        ' Get the folder to add the new file to change
        ' the FullName test to a Folder in your vault
        Dim myFolder As _
        VDF.Vault.Currency.Entities.Folder = Nothing

        For Each Flder As _
        VDF.Vault.Currency.Entities.Folder In myFldrCol
            '    If Flder.NumberOfChildren > 0 Then
            ' myFolder = findsubfolder(folderstring, Flder)
            '   Else

            If Flder.FullName = folderstring Then
                myFolder = Flder
                Exit For
            End If
            '  End If
        Next
        Return myFolder
    End Function
    Private Sub Button4_Click_1(sender As Object, e As EventArgs) Handles Button4.Click


        Try
            Reg_W("SK_Projectfolder_item_F", m_sourcePathTextBox.Text)
            Reg_W("SK_toShip_F", TextBoxShipTo.Text)
            Reg_W("SK_ERP_PROJECT_F", TextBoxErpProject.Text)
            Reg_W("SK_PROJECT_NAME_F", TextBoxProjectName.Text)




            Try


                Dim projectFolder As VDF.Vault.Currency.Entities.Folder = findfolder(m_sourcePathTextBox.Text)
                projectfolderID = projectFolder.Id

                projectfolderName = m_sourcePathTextBox.Text
                If projectfolderName.Split("/").Last.ToLower <> "furniture" Then
                    MessageBox.Show("Project Folder is not Furniture")
                    Exit Sub
                End If
                If TextBoxShipTo.Text = "" Then
                    MessageBox.Show("Check the Ship To Field")
                    Exit Sub
                End If
            Catch ex As Exception
                MessageBox.Show("Check the Project folder")
                Exit Sub
            End Try
            Try
                If TextBoxShipTo.Text IsNot Nothing Then ' first if
                    'for each row in datagrid
                    Try
                        For i As Integer = 0 To DataGridView1.Rows.Count - 1
                            Dim oMasterId As Long = DataGridView1.Rows(i).Cells(0).Value.ToString
                            Dim selectedFile As ACW.File = m_conn.WebServiceManager.DocumentService.GetLatestFileByMasterId(oMasterId)
                            Try

                                Dim newname As String = selectedFile.Name.Replace(TextBoxShipFom.Text, TextBoxShipTo.Text)
                                Dim docsvc As ACW.DocumentService = Nothing
                                docsvc = m_conn.WebServiceManager.DocumentService

                                Dim propSvc As ACW.PropertyService = m_conn.WebServiceManager.PropertyService
                                Dim props1 As ACW.PropDef() = propSvc.FindPropertyDefinitionsBySystemNames("FILE", New String() {"ClientFileName"})
                                Dim srcSort As New Autodesk.Connectivity.WebServices.SrchSort()



                                'create bookmark
                                Dim bookmark As String = [String].Empty
                                'status
                                Dim srcStatus As Autodesk.Connectivity.WebServices.SrchStatus = Nothing
                                Dim SrcCond1 As New ACW.SrchCond()
                                SrcCond1.PropDefId = Convert.ToInt64(props1(0).Id)
                                SrcCond1.PropTyp = ACW.PropertySearchType.SingleProperty
                                SrcCond1.SrchRule = ACW.SearchRuleType.Must
                                SrcCond1.SrchOper = Convert.ToInt32(3)
                                SrcCond1.SrchTxt = newname.Trim


                                Dim ModelFiles As ACW.File() = docsvc.FindFilesBySearchConditions(New Autodesk.Connectivity.WebServices.SrchCond() {SrcCond1}, New Autodesk.Connectivity.WebServices.SrchSort() {srcSort}, Nothing, True, True, bookmark, srcStatus) '  itemSvc.GetAllLatestItems()

                                If ModelFiles IsNot Nothing Then
                                    'MessageBox.Show("There is similar file in Vault already: " & newname)
                                    Dim tempstr As String = DataGridView1.Rows(i).Cells(8).Value
                                    If tempstr = "" Then
                                        DataGridView1.Rows(i).Cells(8).Value = "File Exits"
                                    Else
                                        DataGridView1.Rows(i).Cells(8).Value = tempstr + "|" + "Target File Exits"

                                    End If

                                End If
                            Catch ex As Exception

                            End Try
                        Next
                    Catch ex As Exception
                    End Try
                End If
            Catch ex As Exception

            End Try
        Catch ex As Exception

        End Try
        Button3.Enabled = True
    End Sub

    Private Sub Button3_Click(sender As Object, e As EventArgs) Handles Button3.Click
        Button3.Enabled = False
        Me.Cursor = Cursors.WaitCursor

        Try 'main try
            Reg_W("SK_Projectfolder_item_F", m_sourcePathTextBox.Text)
            Reg_W("SK_toShip_F", TextBoxShipTo.Text)
            Reg_W("SK_ERP_PROJECT_F", TextBoxErpProject.Text)
            Reg_W("SK_PROJECT_NAME_F", TextBoxProjectName.Text)




            Try


                Dim projectFolder As VDF.Vault.Currency.Entities.Folder = findfolder(m_sourcePathTextBox.Text)
                projectfolderID = projectFolder.Id
                projectfolderName = m_sourcePathTextBox.Text
                If projectfolderName.Split("/").Last.ToLower <> "furniture" Then
                    MessageBox.Show("Project Folder is not Furniture")
                    Exit Sub
                End If
                If TextBoxShipTo.Text = "" Then
                    MessageBox.Show("Check the Ship To Field")
                    Exit Sub
                End If
            Catch ex As Exception
                MessageBox.Show("Check the Project folder")
                Exit Sub
            End Try



            If TextBoxShipTo.Text IsNot Nothing Then ' first if
                'for each row in datagrid
                Try


                    For i As Integer = 0 To DataGridView1.Rows.Count - 1
                        Dim projectFolder As VDF.Vault.Currency.Entities.Folder = findfolder(m_sourcePathTextBox.Text)
                        Dim projectfolderID As Long = projectFolder.Id
                        Dim projectfolderName As String = projectFolder.FullName
                        Dim oMasterId As Long = DataGridView1.Rows(i).Cells(0).Value.ToString
                        Dim projectNewFolder As String = DataGridView1.Rows(i).Cells(6).Value.ToString
                        Dim state As String = DataGridView1.Rows(i).Cells(3).Value.ToString
                        Dim returnValue As String = SK_CopyItemF_Module.CopyFile(oMasterId, projectfolderID, projectfolderName, projectNewFolder, TextBoxShipFom.Text, TextBoxShipTo.Text, state, TextBoxErpProject.Text, TextBoxProjectName.Text)
                        DataGridView1.Rows(i).Cells(8).Value = returnValue
                        Me.Refresh()
                    Next
                Catch ex As Exception

                End Try


            End If 'final if


        Catch ex As Exception
            MessageBox.Show(ex.Message & " Mek:MainTry")

        End Try
        'let's clear arrays
        ' Label6.Text = Label6.Text & " | New Model: " & (masterArr(9, 0))
        ' My.Computer.Clipboard.SetText((masterArr(9, 0)))
        masterArr = Nothing
        drwArr = Nothing
        derivedArr = Nothing


        ' numbofSteps = numbofSteps + numbofSteps

        Button3.Text = "Done!"
        Me.Cursor = Cursors.Default

    End Sub

    Public Function getFileIteration _
          (nameOfFile As String,
                  connection As _
             VDF.Vault.Currency.
         Connections.Connection) _
          As VDF.Vault.Currency.
           Entities.FileIteration

        Dim conditions As ACW.SrchCond()

        ReDim conditions(0)

        Dim lCode As Long = 1

        Dim Defs As ACW.PropDef() =
      connection.WebServiceManager.
                   PropertyService.
    GetPropertyDefinitionsByEntityClassId("FILE")

        Dim Prop As ACW.PropDef = Nothing

        For Each def As ACW.PropDef In Defs
            If def.DispName =
                      "File Name" Then
                Prop = def
            End If
        Next def

        Dim searchCondition As _
    ACW.SrchCond = New ACW.SrchCond()

        searchCondition.PropDefId =
                              Prop.Id

        searchCondition.PropTyp =
    ACW.PropertySearchType.SingleProperty
        searchCondition.SrchOper = lCode

        searchCondition.SrchTxt = nameOfFile

        conditions(0) = searchCondition

        ' search for files
        Dim FileList As List _
    (Of Autodesk.Connectivity.WebServices.File) =
        New List _
    (Of Autodesk.Connectivity.WebServices.File) '()
        Dim sBookmark As String = String.Empty
        Dim Status As ACW.SrchStatus = Nothing

        While (Status Is Nothing OrElse
             FileList.Count < Status.TotalHits)

            Dim files As Autodesk.Connectivity.
    WebServices.File() = connection.WebServiceManager.
    DocumentService.FindFilesBySearchConditions _
                                 (conditions,
                Nothing, Nothing, True, True,
                             sBookmark, Status)

            If (Not files Is Nothing) Then
                FileList.AddRange(files)
            End If
        End While

        Dim oFileIteration As _
            VDF.Vault.Currency.Entities.
                   FileIteration = Nothing
        For i As Integer =
                  0 To FileList.Count - 1
            If FileList(i).Name =
                          nameOfFile Then
                oFileIteration =
               New VDF.Vault.Currency.
    Entities.FileIteration(connection,
                            FileList(i))
            End If
        Next

        Return oFileIteration

    End Function
    Public Function GetFileAndAssociations(path As String, ByRef fileAssocParams As ArrayList) As Autodesk.Connectivity.WebServices.File
        Dim paths As String() = New String(0) {}

        paths(0) = path
        Dim files As Autodesk.Connectivity.WebServices.File() = m_conn.WebServiceManager.DocumentService.FindLatestFilesByPaths(paths)

        Dim assocArray As ACW.FileAssocArray() = m_conn.WebServiceManager.DocumentService.GetFileAssociationsByIds(New Long() {files(0).Id}, ACW.FileAssociationTypeEnum.None, False, ACW.FileAssociationTypeEnum.Dependency, True, False, True) ' tähän muutettiin kaikki depencyt

        Dim assemblyAssoc As ACW.FileAssocArray = assocArray(0)

        If assemblyAssoc IsNot Nothing Then
            For Each assoc As ACW.FileAssoc In assemblyAssoc.FileAssocs

                Dim param1 As New ACW.FileAssocParam()
                param1.CldFileId = assoc.CldFile.Id
                param1.RefId = assoc.RefId
                param1.Source = assoc.Source
                param1.Typ = assoc.Typ
                param1.ExpectedVaultPath = assoc.ExpectedVaultPath

                fileAssocParams.Add(param1)
            Next
        End If
        Return files(0)
    End Function
    Public Function AddMyAttachments _
                 (nameOfFileToAttachTo As String,
                       myFileIterationIds() As Long,
            myFolderIdOfNewFileIteration As Long,
            connection As _
    VDF.Vault.Currency.Connections.Connection) _
                                      As Boolean
        Dim oFileIteration As _
    VDF.Vault.Currency.Entities.FileIteration =
getFileIteration(nameOfFileToAttachTo, connection)



        If oFileIteration Is Nothing Then
            MessageBox.Show("Unable to get FileIteration")
            Return False
        End If

        ' Get settings
        Dim oSettings As _
        VDF.Vault.Settings.AcquireFilesSettings =
      New VDF.Vault.Settings.AcquireFilesSettings _
                                       (connection)

        ' Going to Check Out (not download file)
        oSettings.DefaultAcquisitionOption =
        VDF.Vault.Settings.AcquireFilesSettings.
                        AcquisitionOption.Checkout

        ' add the file to the settings
        oSettings.AddEntityToAcquire(oFileIteration)

        'Do the CheckOut
        Dim myAcquireVaultSettings As _
        VDF.Vault.Results.AcquireFilesResults
        myAcquireVaultSettings =
  connection.FileManager.AcquireFiles(oSettings)

        Dim oNewFileIteration As _
        VDF.Vault.Currency.Entities.FileIteration

        Dim myFileAcqRes As _
        VDF.Vault.Results.FileAcquisitionResult

        myFileAcqRes =
          myAcquireVaultSettings.FileResults(0)

        oNewFileIteration =
                   myFileAcqRes.NewFileIteration

        If oNewFileIteration.IsCheckedOut = True Then

            ' settings used in GetFileAssociationLites()
            Dim myFileRelationshipSettings As _
VDF.Vault.Settings.FileRelationshipGatheringSettings
            myFileRelationshipSettings =
New VDF.Vault.Settings.FileRelationshipGatheringSettings

            myFileRelationshipSettings.
                            IncludeAttachments = True
            myFileRelationshipSettings.
                               IncludeChildren = True
            myFileRelationshipSettings.
                                IncludeParents = True
            myFileRelationshipSettings.
                   IncludeRelatedDocumentation = True

            Dim myColOfFileAssocLite As _
            System.Collections.Generic.IEnumerable _
                   (Of ACW.FileAssocLite) = Nothing

            myColOfFileAssocLite =
    connection.FileManager.GetFileAssociationLites _
  (New Long() {oNewFileIteration.EntityIterationId},
                        myFileRelationshipSettings)

            ' Going to add new FileAssocParam
            ' objects to this list

            ' ArrayList to contain
            ' FileAssocParam objects
            Dim fileAssocParams As ArrayList =
                                New ArrayList

            ' Add FileAssocParam objects to the ArrayList
            ' using values from the collection
            ' of FileAssocLite in the collection
            ' returned from GetFileAssociationLites()
            If Not myColOfFileAssocLite Is Nothing Then
                Dim myFileAssocLite As ACW.FileAssocLite
                '    'Go through each FileAssoLite in the
                '   in the collection of FileAssocLite
                For Each myFileAssocLite In
                               myColOfFileAssocLite
                    ' This is a new FileAssocParam that
                    ' is going to be added to the List
                    ' getting the properties
                    Dim par As ACW.FileAssocParam =
                               New ACW.FileAssocParam()
                    par.CldFileId =
                         myFileAssocLite.CldFileId
                    par.RefId = myFileAssocLite.RefId
                    par.Source = myFileAssocLite.Source
                    par.Typ = myFileAssocLite.Typ
                    par.ExpectedVaultPath =
                   myFileAssocLite.ExpectedVaultPath
                    fileAssocParams.Add(par)
                Next
            End If


            ' Get the folder of the file
            ' we are associating
            Dim myDictionary As IDictionary _
       (Of Long, VDF.Vault.Currency.Entities.Folder)
            myDictionary =
         connection.FolderManager.GetFoldersByIds _
        (New Long() {myFolderIdOfNewFileIteration})

            ' Get the folder from the dictionary
            Dim keyPair As Generic.KeyValuePair _
     (Of Long, VDF.Vault.Currency.Entities.Folder)

            keyPair = myDictionary.First
            Dim myFldr As _
            VDF.Vault.Currency.Entities.Folder _
                                  = keyPair.Value

            ' Add the new association



            Dim newFileAssocPar As ACW.FileAssocParam = New ACW.FileAssocParam()

            Dim myFileAssocParamArray As ACW.FileAssocParam()

            Dim fileAssocParams1 As ArrayList =
                                New ArrayList

            For Each myfileiterationid As Long In myFileIterationIds


                newFileAssocPar.CldFileId =
                            myfileiterationid
                newFileAssocPar.RefId = Nothing
                newFileAssocPar.Source = Nothing
                newFileAssocPar.Typ =
                        ACW.AssociationType.Dependency
                newFileAssocPar.ExpectedVaultPath =
                                myFldr.FolderPath



                ' Add our new FileAssocParam
                fileAssocParams1.Add(newFileAssocPar)
                newFileAssocPar = New ACW.FileAssocParam()

            Next


            ' Populate this with the FileAssocParam
            ' objects that we create from properties'
            ' in the FileAssocArray and the new file
            myFileAssocParamArray = DirectCast(fileAssocParams1.ToArray _
          (GetType(ACW.FileAssocParam)), ACW.FileAssocParam())

            ' Use the overloaded method of CheckInFile
            ' that takes a stream This will allow
            ' checking in a file that has not been
            ' downloaded
            ' (by passing in a stream that is Nothing)

            Dim tbom As Autodesk.Connectivity.WebServices.BOM = m_conn.WebServiceManager.DocumentService.GetBOMByFileId(oFileIteration.EntityIterationId)


            Dim myStream As System.IO.Stream = Nothing
            connection.FileManager.CheckinFile _
            (oNewFileIteration, "Attachments added", False,
                                       Date.Now,
                          myFileAssocParamArray,
                          Nothing, False, Nothing,
                      ACW.FileClassification.None,
                                  False, myStream)

        Else
            MessageBox.Show("Unable to check out")
            Return False
        End If

        Return True

    End Function


End Class
