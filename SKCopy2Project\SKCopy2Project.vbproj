﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{A659EA2E-53F7-4E78-B2FA-FA52E2257BBB}</ProjectGuid>
    <OutputType>Library</OutputType>
    <StartupObject>
    </StartupObject>
    <RootNamespace>SKCopy2Project</RootNamespace>
    <AssemblyName>SK-VaultExtension</AssemblyName>
    <FileAlignment>512</FileAlignment>
    <MyType>Windows</MyType>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>x64</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>C:\ProgramData\Autodesk\Vault 2022\Extensions\SK-VaultExtension\</OutputPath>
    <DocumentationFile>
    </DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <DefineDebug>false</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <Optimize>true</Optimize>
    <OutputPath>..\..\..\..\..\..\..\..\..\..\ProgramData\Autodesk\Vault 2019\Extensions\SKCopy2Project\</OutputPath>
    <DocumentationFile>
    </DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
  </PropertyGroup>
  <PropertyGroup>
    <OptionExplicit>Off</OptionExplicit>
  </PropertyGroup>
  <PropertyGroup>
    <OptionCompare>Binary</OptionCompare>
  </PropertyGroup>
  <PropertyGroup>
    <OptionStrict>Off</OptionStrict>
  </PropertyGroup>
  <PropertyGroup>
    <OptionInfer>On</OptionInfer>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationManifest>SK-VaultExtension.vcet.config</ApplicationManifest>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <DefineDebug>true</DefineDebug>
    <DefineTrace>true</DefineTrace>
    <OutputPath>C:\ProgramData\Autodesk\Vault 2022\Extensions\SK-VaultExtension\</OutputPath>
    <DocumentationFile>
    </DocumentationFile>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <DefineTrace>true</DefineTrace>
    <OutputPath>bin\x64\Release\</OutputPath>
    <DocumentationFile>
    </DocumentationFile>
    <Optimize>true</Optimize>
    <NoWarn>42016,41999,42017,42018,42019,42032,42036,42020,42021,42022</NoWarn>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <CodeAnalysisRuleSet>MinimumRecommendedRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Autodesk.Connectivity.Explorer.Extensibility">
      <HintPath>C:\Program Files\Autodesk\Vault Client 2022\Explorer\Autodesk.Connectivity.Explorer.Extensibility.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Autodesk.Connectivity.Explorer.ExtensibilityTools">
      <HintPath>C:\Program Files\Autodesk\Vault Client 2022\Explorer\Autodesk.Connectivity.Explorer.ExtensibilityTools.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Autodesk.Connectivity.Extensibility.Framework">
      <HintPath>C:\Program Files\Autodesk\Vault Client 2022\Explorer\Autodesk.Connectivity.Extensibility.Framework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Autodesk.Connectivity.WebServices">
      <HintPath>C:\Program Files\Autodesk\Vault Client 2022\Explorer\Autodesk.Connectivity.WebServices.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Autodesk.DataManagement.Client.Framework">
      <HintPath>C:\Program Files\Autodesk\Vault Client 2022\Explorer\Autodesk.DataManagement.Client.Framework.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Autodesk.DataManagement.Client.Framework.Forms">
      <HintPath>C:\Program Files\Autodesk\Vault Client 2022\Explorer\Autodesk.DataManagement.Client.Framework.Forms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Autodesk.DataManagement.Client.Framework.Vault">
      <HintPath>C:\Program Files\Autodesk\Vault Client 2022\Explorer\Autodesk.DataManagement.Client.Framework.Vault.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Autodesk.DataManagement.Client.Framework.Vault.Forms">
      <HintPath>C:\Program Files\Autodesk\Vault Client 2022\Explorer\Autodesk.DataManagement.Client.Framework.Vault.Forms.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="Autodesk.Inventor.Interop, Version=21.0.0.0, Culture=neutral, PublicKeyToken=d84147f8b4276564, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <EmbedInteropTypes>True</EmbedInteropTypes>
      <HintPath>C:\Windows\Microsoft.NET\assembly\GAC_MSIL\Autodesk.Inventor.Interop\v4.0_20.0.0.0__d84147f8b4276564\Autodesk.Inventor.Interop.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Office.Interop.Excel, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <EmbedInteropTypes>True</EmbedInteropTypes>
      <HintPath>C:\Windows\assembly\GAC_MSIL\Microsoft.Office.Interop.Excel\15.0.0.0__71e9bce111e9429c\Microsoft.Office.Interop.Excel.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Vbe.Interop, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <EmbedInteropTypes>True</EmbedInteropTypes>
      <HintPath>C:\Windows\assembly\GAC_MSIL\Microsoft.Vbe.Interop\15.0.0.0__71e9bce111e9429c\Microsoft.Vbe.Interop.dll</HintPath>
    </Reference>
    <Reference Include="OFFICE, Version=15.0.0.0, Culture=neutral, PublicKeyToken=71e9bce111e9429c, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <EmbedInteropTypes>True</EmbedInteropTypes>
      <HintPath>C:\Windows\assembly\GAC_MSIL\office\15.0.0.0__71e9bce111e9429c\OFFICE.DLL</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Data">
      <Private>False</Private>
      <EmbedInteropTypes>False</EmbedInteropTypes>
    </Reference>
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions">
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Net.Http" />
  </ItemGroup>
  <ItemGroup>
    <Import Include="Microsoft.VisualBasic" />
    <Import Include="System" />
    <Import Include="System.Collections" />
    <Import Include="System.Collections.Generic" />
    <Import Include="System.Data" />
    <Import Include="System.Drawing" />
    <Import Include="System.Diagnostics" />
    <Import Include="System.IO" />
    <Import Include="System.Windows.Forms" />
    <Import Include="System.Linq" />
    <Import Include="System.Xml.Linq" />
    <Import Include="System.Threading.Tasks" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="copy2ProjectAisi.Designer.vb">
      <DependentUpon>copy2ProjectAisi.vb</DependentUpon>
    </Compile>
    <Compile Include="copy2ProjectAisi.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Designer.vb">
      <DependentUpon>Form1.vb</DependentUpon>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="sk_copyitemF.Designer.vb">
      <DependentUpon>sk_copyitemF.vb</DependentUpon>
    </Compile>
    <Compile Include="sk_copyitemF.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="My Project\AssemblyInfo.vb" />
    <Compile Include="My Project\Application.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Application.myapp</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="My Project\Resources.Designer.vb">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="My Project\Settings.Designer.vb">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <Compile Include="MyCustomTabControl.designer.vb">
      <DependentUpon>MyCustomTabControl.vb</DependentUpon>
    </Compile>
    <Compile Include="MyCustomTabControl.vb">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="Reg_RW_Module.vb" />
    <Compile Include="skAddBomRow.Designer.vb">
      <DependentUpon>skAddBomRow.vb</DependentUpon>
    </Compile>
    <Compile Include="skAddBomRow.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SKCopy2ProjectCommandExtension.vb" />
    <Compile Include="SKPrint_to_folder_by_state_form.Designer.vb">
      <DependentUpon>SKPrint_to_folder_by_state_form.vb</DependentUpon>
    </Compile>
    <Compile Include="SKPrint_to_folder_by_state_form.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SKPrint_to_folder_form.Designer.vb">
      <DependentUpon>SKPrint_to_folder_form.vb</DependentUpon>
    </Compile>
    <Compile Include="SKPrint_to_folder_form.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="skPromote.Designer.vb">
      <DependentUpon>skPromote.vb</DependentUpon>
    </Compile>
    <Compile Include="skPromote.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="SK_CopyItemF_Module.vb" />
    <Compile Include="sk_copyItemI.Designer.vb">
      <DependentUpon>sk_copyItemI.vb</DependentUpon>
    </Compile>
    <Compile Include="sk_copyItemI.vb">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Util.vb" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="copy2ProjectAisi.resx">
      <DependentUpon>copy2ProjectAisi.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Form1.resx">
      <DependentUpon>Form1.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="sk_copyitemF.resx">
      <DependentUpon>sk_copyitemF.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="My Project\Resources.resx">
      <Generator>VbMyResourcesResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.vb</LastGenOutput>
      <CustomToolNamespace>My.Resources</CustomToolNamespace>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="MyCustomTabControl.resx">
      <DependentUpon>MyCustomTabControl.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="skAddBomRow.resx">
      <DependentUpon>skAddBomRow.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SKPrint_to_folder_by_state_form.resx">
      <DependentUpon>SKPrint_to_folder_by_state_form.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="SKPrint_to_folder_form.resx">
      <DependentUpon>SKPrint_to_folder_form.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="skPromote.resx">
      <DependentUpon>skPromote.vb</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="sk_copyItemI.resx">
      <DependentUpon>sk_copyItemI.vb</DependentUpon>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Include="My Project\Application.myapp">
      <Generator>MyApplicationCodeGenerator</Generator>
      <LastGenOutput>Application.Designer.vb</LastGenOutput>
    </None>
    <None Include="My Project\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <CustomToolNamespace>My</CustomToolNamespace>
      <LastGenOutput>Settings.Designer.vb</LastGenOutput>
    </None>
    <None Include="App.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="SK-VaultExtension.vcet.config">
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.VisualBasic.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>